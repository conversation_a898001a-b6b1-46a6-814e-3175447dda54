# Dependencies
node_modules/
.pnpm-store/
.npm/
.yarn/

# Build outputs
apps/flexirates/dist
apps/flexiratesMerchant/dist
apps/official/dist
apps/merchant/dist
build/
.next/
.nuxt/
.vercel/
.netlify/

# Environment files
.env
.env.local
.env.production
.env.staging

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Cache
.cache/
.parcel-cache/
.eslintcache 

CLAUDE.md