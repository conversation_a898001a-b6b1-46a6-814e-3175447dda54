import antfu from '@antfu/eslint-config'

export default antfu({
  stylistic: {
    indent: 2,
    quotes: 'single',
  },
  typescript: true,
  vue: {
    overrides: {
      'vue/block-order': ['error', {
        order: ['script', 'template', 'style'],
      }],
      'vue/component-definition-name-casing': 'off',
      'antfu/top-level-function': 'off',
      'curly': ['error', 'multi-line'],
      'vue/no-mutating-props': 'off',
    },
    vueVersion: 3,
  },
  jsonc: false,
  yml: false,
}, {
  rules: {
    'antfu/top-level-function': 'off',
    'curly': ['error', 'multi-line'],
    'no-console': 'off',
    'no-use-before-define': 'off',
    '@typescript-eslint/no-use-before-define': 'off',
  },
})
