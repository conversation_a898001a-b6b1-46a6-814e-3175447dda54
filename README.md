# Bill Merchant Apps

一个基于 pnpm workspace 的 monorepo 项目。

## 项目结构

```
bill-merchant-apps/
├── apps/           # 应用程序
├── packages/       # 共享包
├── package.json    # 根配置
└── pnpm-workspace.yaml # workspace 配置
```

## 开发环境要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0

## 安装依赖

```bash
pnpm install
```

## 常用命令

### 基本命令

```bash
# 开发模式（并行启动所有应用）
pnpm dev

# 构建所有项目
pnpm build

# 代码检查
pnpm lint

# 运行测试
pnpm test

# 清理构建文件
pnpm clean
```

### Workspace 管理命令

```bash
# 查看所有 workspace 包
pnpm list --depth=0

# 查看 workspace 依赖树
pnpm list --depth=2

# 在根目录安装依赖
pnpm add <package> -w

# 为特定 workspace 安装依赖
pnpm add <package> --filter <workspace-name>

# 为所有 workspace 安装相同依赖
pnpm add <package> --filter "*"
```

### 选择性操作命令

```bash
# 只运行特定 workspace 的开发模式
pnpm --filter <workspace-name> dev

# 只构建特定 workspace
pnpm --filter <workspace-name> build

# 构建多个 workspace
pnpm --filter <workspace1> --filter <workspace2> build

# 构建某个 workspace 及其依赖
pnpm --filter <workspace-name>... build

# 运行特定 workspace 的脚本
pnpm --filter <workspace-name> run <script-name>
```

### 依赖管理命令

```bash
# 添加 workspace 之间的依赖关系
pnpm add <workspace-name> --filter <target-workspace>

# 更新所有依赖
pnpm update

# 更新特定 workspace 的依赖
pnpm --filter <workspace-name> update

# 检查过时的依赖
pnpm outdated

# 移除依赖
pnpm remove <package> --filter <workspace-name>
```

### 调试和信息命令

```bash
# 查看 workspace 配置
pnpm config list

# 查看某个包的信息
pnpm info <package>

# 检查 workspace 依赖关系
pnpm why <package>

# 验证 package.json 和 lockfile 的一致性
pnpm audit

# 修复 lockfile
pnpm install --frozen-lockfile=false
```

## 添加新的应用

在 `apps/` 目录下创建新的项目文件夹，确保包含 `package.json` 文件。

## 添加共享包

在 `packages/` 目录下创建新的包文件夹，确保包含 `package.json` 文件。
