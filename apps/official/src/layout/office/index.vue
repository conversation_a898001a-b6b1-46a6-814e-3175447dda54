<script setup lang="ts">
import appFooter from './components/appFooter.vue'
import appHeader from './components/appHeader.vue'
</script>

<template>
  <div class="official-layout">
    <app-header />

    <main class="main-content">
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>

    <app-footer />
  </div>
</template>

<style lang="scss" scoped>
.official-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-weight: 500;
}

.header {
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-img {
  height: 40px;
  width: auto;
}

.dropdown {
  position: relative;
}

.dropdown-toggle::after {
  content: "▼";
  font-size: 0.7rem;
  margin-left: 0.3rem;
  vertical-align: middle;
}

.book-call-btn {
  background-color: #f0f0f0;
  color: #333 !important;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.book-call-btn:hover {
  background-color: #e0e0e0;
}

.login-btn {
  background-color: #ff5722;
  color: white !important;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.login-btn:hover {
  background-color: #e64a19;
}

.main-content {
  flex: 1;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

@media (max-width: 768px) {
  .nav-links {
    display: none;
    /* 在移动设备上隐藏导航链接，应该添加一个汉堡菜单 */
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .social-links {
    justify-content: center;
  }
}
</style>
