<script setup lang="ts">
import CookiesPop from '@/components/cookiesPop/index.vue'
import { useAppStore } from '@/store/modules/app'
import { storeToRefs } from 'pinia'

const appStore = useAppStore()

const { isShowCookiesPop } = storeToRefs(appStore)
</script>

<template>
  <footer class="footer">
    <div class="footer-content">
      <div class="footer-section logo-section">
        <div class="footer-logo">
          <img src="@/assets/official/Billbuddy Logo-11.png" alt="Bill Buddy" style="width: 160px;">
        </div>
        <div class="social-links">
          <a href="https://www.facebook.com/BillBuddy" aria-label="Facebook">
            <img src="@/assets/official/Facebook Footer Icon.png" alt="Facebook" style="width: 26px;">
          </a>
          <a href="https://au.linkedin.com/company/billbuddy-au" aria-label="LinkedIn">
            <img src="@/assets/official/LinkedIn Footer Icon.png" alt="LinkedIn" style="width: 30px;">
          </a>
          <a href="https://www.instagram.com/bill_buddy_australia" aria-label="Instagram">
            <img src="@/assets/official/Instagram Footer Icon.png" alt="Instagram" style="width: 26px;">
          </a>
        </div>
      </div>

      <div class="footer-links-container">
        <div class="footer-section">
          <h3 class="footer-section-title">
            Products
            <span class="dot" />
          </h3>
          <router-link class="footer-link" to="/DirectDebit">
            Direct Debit
          </router-link>
          <router-link class="footer-link" to="/BPay">
            BPAY
          </router-link>
          <router-link class="footer-link" to="/PayMyInvoice">
            Pay My Invoice
          </router-link>
          <router-link class="footer-link" to="/WebPay">
            WebPay
          </router-link>
        </div>
        <div class="footer-section">
          <h3 class="footer-section-title">
            Support
            <span class="dot" />
          </h3>
          <router-link class="footer-link" :to="{ name: 'officialSupport', hash: '#supportDivide' }">
            Frequently Asked Questions
          </router-link>
          <router-link class="footer-link" :to="{ name: 'officialSupport', hash: '#billerDivide' }">
            Biller Support
          </router-link>
          <router-link class="footer-link" :to="{ name: 'officialSupport', hash: '#payerDivide' }">
            Payer Support
          </router-link>
        </div>
        <div class="footer-section">
          <h3 class="footer-section-title">
            Quick Links
            <span class="dot" />
          </h3>
          <a class="footer-link" href="https://o2.billbuddy.com/index/login">
            Biller Login
          </a>
          <a class="footer-link" href="https://site.billbuddy.com/index/online-ddr-authority" target="_blank">
            Online DDR Authority
          </a>
          <a class="footer-link" href="https://p.billbuddy.com/">
            Bulk Process
          </a>
          <a href="https://www.paymyinvoice.com.au/" class="footer-link">
            PayMyInvoice
          </a>
        </div>
      </div>
    </div>
    <div class="footer-bottom">
      <div class="footer-bottom-container">
        <p>© {{ new Date().getFullYear() }} BILL BUDDY PTY LTD. ALL RIGHTS RESERVED</p>
        <div class="footer-links">
          <router-link class="footer-link" to="/terms">
            Terms
          </router-link>
          <Divider layout="vertical" />
          <router-link class="footer-link" to="/privacy">
            Privacy
          </router-link>
          <Divider layout="vertical" />
          <div class="footer-link cursor-pointer" @click="appStore.setIsShowCookiesPop(!isShowCookiesPop)">
            Cookies
          </div>
          <Divider layout="vertical" />
          <router-link class="footer-link" to="/Contact-Us">
            Contact Us
          </router-link>
        </div>
      </div>
    </div>
    <CookiesPop v-if="isShowCookiesPop" @accept="appStore.setIsShowCookiesPop(false)" />
  </footer>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints' as *;

.footer {
    background-color: var(--colors-primary);
    color: white;
    padding: 1rem 0 0;
    margin-top: auto;
}

.footer-content {
    max-width: 1340px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    gap: 2rem;
    padding: 2.5rem 0 4rem 0;

    @include media-breakpoint-down(xxxl) {
        max-width: 1400px;
        padding: 2rem 1rem 3.5rem 1rem;
    }

    @include media-breakpoint-down(xxl) {
        max-width: 1200px;
    }

    @include media-breakpoint-down(xl) {
        max-width: 100%;
        padding: 2rem 1.5rem 3rem 1.5rem;
    }

    @include media-breakpoint-down(lg) {
        flex-direction: column;
        padding: 2rem 1.5rem 3rem 1.5rem;
    }

    @include media-breakpoint-down(md) {
        padding: 1.75rem 1.25rem 2.5rem 1.25rem;
    }

    @include media-breakpoint-down(sm) {
        text-align: center;
        padding: 1.5rem 1rem 2rem 1rem;
    }
}

.logo-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 0 0 20%;
    gap: 0 !important;

    @include media-breakpoint-down(xl) {
        flex: 0 0 25%;
    }

    @include media-breakpoint-down(lg) {
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .footer-logo {
        text-align: center;
        @include media-breakpoint-down(lg) {
            text-align: center;
        }

        img {
            @include media-breakpoint-down(xl) {
                width: 140px;
            }

            @include media-breakpoint-down(sm) {
                width: 120px;
            }
        }
    }
}

.footer-links-container {
    display: flex;
    flex-wrap: wrap;
    flex: 1;
    gap: 2rem;
    justify-content: flex-end;

    @include media-breakpoint-down(xxxl) {
        gap: 1.75rem;
    }

    @include media-breakpoint-down(xl) {
        gap: 1.5rem;
    }

    @include media-breakpoint-down(lg) {
        justify-content: space-between;
    }

    @include media-breakpoint-down(md) {
        gap: 1.25rem;
    }

    @include media-breakpoint-down(sm) {
        flex-direction: column;
        align-items: center;
        gap: 2rem;
    }
}

.footer-section {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    flex: 0 0 auto;
    min-width: 200px;
    padding: 0 1rem;

    @include media-breakpoint-down(xxxl) {
        min-width: 190px;
    }

    @include media-breakpoint-down(xl) {
        min-width: 180px;
        gap: 0.7rem;
    }

    @include media-breakpoint-down(lg) {
        min-width: 30%;
    }

    @include media-breakpoint-down(md) {
        min-width: 45%;
        gap: 0.6rem;
    }

    @include media-breakpoint-down(sm) {
        align-items: center;
        min-width: 100%;
        width: 100%;
        max-width: 300px;
    }

    .footer-section-title {
        font-size: 24px;
        font-weight: 700;
        color: rgb(225, 255, 169);

        @include media-breakpoint-down(xxl) {
            font-size: 22px;
        }

        @include media-breakpoint-down(md) {
            font-size: 20px;
        }

        @include media-breakpoint-down(sm) {
            font-size: 20px;
        }
    }

    .dot {
        background-color: #ff5722;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 0.5rem;
    }

    .footer-link {
        font-size: 20px;
        color: white;
        margin-top: 0.25rem;
        &:hover {
            color: #ff5722;
        }

        @include media-breakpoint-down(xxl) {
            font-size: 18px;
        }

        @include media-breakpoint-down(sm) {
            font-size: 16px;
        }
    }
}

.footer-logo-img {
    height: 50px;
    width: auto;
    filter: brightness(0) invert(1);
}

.social-links {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex: 1;

    @include media-breakpoint-down(lg) {
        margin-top: 0.75rem;
    }

    @include media-breakpoint-down(sm) {
        margin-top: 0.75rem;
        justify-content: center;
        gap: 0.75rem;
    }
}

.social-links a {
    color: white;
    font-size: 1.2rem;
}

.footer-section h3 {
    color: white;
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
}

.footer-section a {
    color: #b0b0b0;
    text-decoration: none;
    transition: color 0.3s ease;
    font-size: 0.9rem;
}

.footer-section a:hover {
    color: #ff5722;
}

.footer-bottom {
    display: flex;
    justify-content: center;
    color: #b0b0b0;
    font-size: 0.8rem;
    background-color: #ff5722;
    margin-top: 0.5rem;
    line-height: 60px;
    height: 60px;

    @include media-breakpoint-down(lg) {
        height: auto;
        line-height: normal;
        padding: 0.75rem 0;
    }

    @include media-breakpoint-down(md) {
        height: auto;
        line-height: normal;
        padding: 1rem 0;
    }

    .footer-bottom-container {
        flex: 1;
        display: flex;
        justify-content: space-between;
        max-width: 1340px;
        padding: 0 1.5rem;

        @include media-breakpoint-down(xxl) {
            max-width: 1200px;
        }

        @include media-breakpoint-down(xl) {
            max-width: 100%;
        }

        @include media-breakpoint-down(md) {
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            padding: 0 1.25rem;
        }

        @include media-breakpoint-down(sm) {
            padding: 0 1rem;
        }

        p {
            font-size: 18px;
            line-height: 60px;
            color: white;

            @include media-breakpoint-down(xl) {
                font-size: 17px;
            }

            @include media-breakpoint-down(lg) {
                font-size: 15px;
                line-height: normal;
            }

            @include media-breakpoint-down(md) {
                line-height: normal;
                text-align: center;
                font-size: 14px;
            }

            @include media-breakpoint-down(sm) {
                font-size: 14px;
            }
        }

        .footer-links {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-top: 0;
            color: white;
            padding: 22px 0;

            @include media-breakpoint-down(lg) {
                padding: 0;
                gap: 0.5rem;
            }

            @include media-breakpoint-down(md) {
                padding: 0;
                flex-wrap: wrap;
                justify-content: center;
                gap: 0.5rem;
            }

            @include media-breakpoint-down(sm) {
                gap: 0.25rem;
            }

            .footer-link {
                margin-top: 0;
                font-size: 18px;
                line-height: 36px;
                color: white;
                padding: 5px;
                border-radius: 8px;
                &:hover{
                  background-color: #e1ffa9;
                  color: #181349;
                }

                @include media-breakpoint-down(xl) {
                    font-size: 17px;
                    line-height: 32px;
                }

                @include media-breakpoint-down(lg) {
                    font-size: 16px;
                    line-height: 28px;
                }

                @include media-breakpoint-down(md) {
                    font-size: 15px;
                    line-height: 26px;
                }

                @include media-breakpoint-down(sm) {
                    font-size: 14px;
                    line-height: 24px;
                }
            }
        }
    }
}

.footer-links {
    margin-top: 0.5rem;
}
</style>
