<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'

const merchantUrl = import.meta.env.VITE_MERCHANT_URL

const isMobileMenuOpen = ref(false)
const isDropdownOpen = ref({
  support: false,
  links: false,
  products: false,
})

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const toggleDropdown = (dropdown: 'support' | 'links' | 'products') => {
  for (const key in isDropdownOpen.value) {
    if (key !== dropdown) {
      isDropdownOpen.value[key as keyof typeof isDropdownOpen.value] = false
    }
  }
  isDropdownOpen.value[dropdown] = !isDropdownOpen.value[dropdown]
}

const closeDropdownAfterClick = (dropdown: 'support' | 'links' | 'products') => {
  isDropdownOpen.value[dropdown] = false
  if (isMobileMenuOpen.value) {
    toggleMobileMenu()
  }
}

const closeDropdowns = (event: MouseEvent) => {
  // 同时检查按钮和菜单区域
  const triggers = document.querySelectorAll('.dropdown-toggle')
  const menus = document.querySelectorAll('.dropdown-menu')
  let isClickInside = false

  // 检查点击目标是否在触发按钮或菜单内
  triggers.forEach((trigger) => {
    if (trigger.contains(event.target as Node)) {
      isClickInside = true
    }
  })

  menus.forEach((menu) => {
    if (menu.contains(event.target as Node)) {
      isClickInside = true
    }
  })

  if (!isClickInside) {
    isDropdownOpen.value.support = false
    isDropdownOpen.value.links = false
    isDropdownOpen.value.products = false
  }
}
onMounted(() => {
  document.addEventListener('click', closeDropdowns)
})

onUnmounted(() => {
  document.removeEventListener('click', closeDropdowns)
})
</script>

<template>
  <header class="app-office-header">
    <div class="header-container">
      <!-- Logo -->
      <div class="logo">
        <router-link to="/">
          <div class="logo-image">
            <img src="@/assets/Billbuddy Logo-01.png" alt="BillBuddy Logo" class="logo-image">
          </div>
        </router-link>
      </div>

      <!-- Mobile Menu Toggle -->
      <div class="mobile-menu-toggle" @click="toggleMobileMenu">
        <i class="pi" :class="isMobileMenuOpen ? 'pi-times' : 'pi-bars'" />
      </div>

      <!-- Navigation Links -->
      <nav class="nav-links" :class="{ 'mobile-open': isMobileMenuOpen }">
        <div class="dropdown dropdown-products">
          <button
            class="nav-link dropdown-toggle" :class="{ 'nav-link-active': $route.path.includes('/products') }"
            @click="toggleDropdown('products')"
          >
            PRODUCTS
            <i class="pi" :class="isDropdownOpen.products ? 'pi-chevron-up' : 'pi-chevron-down'" />
          </button>
          <div class="dropdown-menu" :class="{ 'dropdown-open': isDropdownOpen.products }">
            <router-link
              :to="{ path: '/DirectDebit' }" class="dropdown-item" active-class="dropdown-item-active"
              @click="closeDropdownAfterClick('products')"
            >
              DIRECT DEBIT
            </router-link>
            <router-link
              :to="{ path: '/WebPay' }" class="dropdown-item" active-class="dropdown-item-active"
              @click="closeDropdownAfterClick('products')"
            >
              WEBPAY
            </router-link>
            <router-link
              :to="{ path: '/BPay' }" class="dropdown-item" active-class="dropdown-item-active"
              @click="closeDropdownAfterClick('products')"
            >
              BPAY
            </router-link>
            <!-- <a href="https://www.paymyinvoice.com.au/" class="dropdown-item">
              PAYMYINVOICE
            </a> -->
            <a href="/PayMyInvoice" class="dropdown-item">
              PAYMYINVOICE
            </a>
          </div>
        </div>

        <router-link
          to="/biller-registration" class="nav-link" active-class="nav-link-active" @click="() => {
            if (isMobileMenuOpen) {
              toggleMobileMenu()
            }
          }"
        >
          BILLER REGISTRATION
        </router-link>
        <router-link
          to="/About-Us" class="nav-link" active-class="nav-link-active" @click="() => {
            if (isMobileMenuOpen) {
              toggleMobileMenu()
            }
          }"
        >
          ABOUT
        </router-link>

        <!-- Support Dropdown -->
        <div class="dropdown">
          <button
            class="nav-link dropdown-toggle" :class="{ 'nav-link-active': $route.path.includes('/support') }"
            @click="toggleDropdown('support')"
          >
            SUPPORT
            <i class="pi" :class="isDropdownOpen.support ? 'pi-chevron-up' : 'pi-chevron-down'" />
          </button>
          <div class="dropdown-menu" :class="{ 'dropdown-open': isDropdownOpen.support }">
            <router-link
              :to="{ name: 'officialSupport', hash: '#startDivide' }" class="dropdown-item"
              :class="{ 'dropdown-item-active': $route.hash === '#startDivide' }"
              @click="closeDropdownAfterClick('support')"
            >
              GENERAL INQUIRIES
            </router-link>
            <router-link
              :to="{ name: 'officialSupport', hash: '#billerDivide' }" class="dropdown-item"
              :class="{ 'dropdown-item-active': $route.hash === '#billerDivide' }"
              @click="closeDropdownAfterClick('support')"
            >
              BILLER SUPPORT
            </router-link>
            <router-link
              :to="{ name: 'officialSupport', hash: '#payerDivide' }" class="dropdown-item"
              :class="{ 'dropdown-item-active': $route.hash === '#payerDivide' }"
              @click="closeDropdownAfterClick('support')"
            >
              PAYER SUPPORT
            </router-link>
            <router-link
              :to="{ name: 'officialSupport', hash: '#enquiryDivide' }" class="dropdown-item"
              :class="{ 'dropdown-item-active': $route.hash === '#enquiryDivide' }"
              @click="closeDropdownAfterClick('support')"
            >
              TRANSACTION ENQUIRY
            </router-link>
          </div>
        </div>

        <!-- Links Dropdown -->
        <div class="dropdown">
          <button class="nav-link dropdown-toggle" @click="toggleDropdown('links')">
            LINKS
            <i class="pi" :class="isDropdownOpen.links ? 'pi-chevron-up' : 'pi-chevron-down'" />
          </button>
          <div class="dropdown-menu" :class="{ 'dropdown-open': isDropdownOpen.links }">
            <a href="https://o2.billbuddy.com/index/login" class="dropdown-item">
              Biller Login
            </a>
            <a href="https://site.billbuddy.com/index/online-ddr-authority" class="dropdown-item">
              ONLINE DDR AUTHORITY
            </a>
            <a href="https://p.billbuddy.com/" class="dropdown-item">
              BULK PROCESS
            </a>
            <a href="https://www.paymyinvoice.com.au/" class="dropdown-item">
              PAYMYINVOICE
            </a>
          </div>
        </div>
        <!-- Action Buttons -->
        <div class="action-buttons">
          <router-link to="/Contact-Us" class="btn btn-book">
            BOOK A CALL
          </router-link>
          <a :href="merchantUrl" class="btn btn-login">
            MERCHANT LOGIN
          </a>
        </div>
      </nav>
    </div>
  </header>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints' as *;

.app-office-header {
  background-color: var(--bg-colors-white);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  z-index: 3;
  height: 100px;
  display: flex;
  align-items: center;
  padding: 0 50px;

  @include media-breakpoint-down(xxl) {
    padding: 0;
    height: 80px;
  }

  @include media-breakpoint-down(md) {
    padding: 0 20px;
    height: 80px;
  }
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
  padding: 0 1rem;
  width: 100%;

  .logo-image {
    width: 200px;
    height: 50px;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    @include media-breakpoint-down(sm) {
      width: 150px;
      height: 40px;
    }
  }
}

/* Logo Styles */
.logo-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-icon {
  display: flex;
  position: relative;
  width: 30px;
  height: 30px;
}

.logo-square,
.logo-circle {
  position: absolute;
  width: 12px;
  height: 12px;
}

.light-blue {
  background-color: #03a9f4;
  bottom: 0;
  left: 0;
}

.nav-links {
  display: flex;
  gap: 1.5rem;
  align-items: center;

  @include media-breakpoint-down(xxxl) {
    gap: 1rem;

    .btn-login,
    .btn-book {
      font-size: 16px;
      padding: 1.25rem 0.75rem;
    }
  }

  @include media-breakpoint-down(xxl) {
    gap: 0.25rem;

    .btn-login,
    .btn-book {
      font-size: 16px;
      padding: 1.25rem 0.75rem;
    }
  }

  .nav-link {
    display: block;
    padding: 12px;
    text-decoration: none;
    color: #181349;
    font-weight: 800;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-size: 18px;
    white-space: nowrap;

    @include media-breakpoint-down(xl) {
      order: 0;
    }

    i {
      color: #ff5722;
      margin-left: 12px;

      @include media-breakpoint-down(xxl) {
        margin-left: 8px;
      }
    }

    &:hover {
      background-color: rgb(225, 255, 169);
    }

    @include media-breakpoint-down(xxl) {
      font-size: 18px;
      padding: 8px;
    }
  }
}

/* Dropdown Styles */
.dropdown {
  position: relative;

  @include media-breakpoint-down(xl) {
    order: 1;
  }
}

.dropdown-toggle {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  padding: 0;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: #E1FFA9;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  min-width: 350px;
  z-index: 1000;
  padding: 0;
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out, padding 0.3s ease-in-out, visibility 0s 0.3s, margin 0.3s ease-in-out;
  margin-top: 0;

  &.dropdown-open {
    max-height: 300px;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    opacity: 1;
    visibility: visible;
    transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out, padding 0.3s ease-in-out, visibility 0s, margin 0.3s ease-in-out;
  }

  @include media-breakpoint-down(sm) {
    min-width: 250px;
  }
}

.dropdown-item {
  display: block;
  padding: 0.5rem 1rem;
  text-decoration: none;
  transition: background-color 0.3s ease;
  display: block;
  padding: 12px;
  text-decoration: none;
  color: #333;
  font-weight: 800;
  transition: all 0.3s ease;
  text-transform: uppercase;
  font-size: 18px;

  @include media-breakpoint-down(lg) {
    font-size: 16px;
    padding: 10px;
  }

  &:hover,
  &.dropdown-item-active {
    background-color: #f8f9fa;
    color: #ff5722;
  }
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  color: #ff5722;
}

/* Button Styles */
.action-buttons {
  display: flex;
  gap: 1rem;
  margin-left: 1rem;

  @include media-breakpoint-down(xxl) {
    gap: 0.5rem;
    margin-left: 0.25rem;
    order: 2;
  }

  @include media-breakpoint-down(xl) {
    gap: 0.5rem;
    margin-left: 0.5rem;
    order: 2;
  }

  .router-link-active {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.btn-book {
  background-color: var(--colors-pink);
  color: var(--colors-primary);
  padding: 1.25rem 2rem;
  font-weight: 800;
  font-size: 18px;
  border-radius: 16px;

  @include media-breakpoint-down(xl) {
    padding: 1rem 1.5rem;
    font-size: 16px;
  }

  @include media-breakpoint-down(lg) {
    padding: 0.75rem 1.25rem;
    font-size: 14px;
  }
}

.btn-book:hover {
  background-color: #ffd0d0;
}

.btn-login {
  background-color: #ff5722;
  color: white;
  padding: 1.25rem 2rem;
  font-weight: 800;
  font-size: 18px;
  border-radius: 16px;

  @include media-breakpoint-down(xl) {
    padding: 1rem 1.5rem;
    font-size: 16px;
  }

  @include media-breakpoint-down(lg) {
    padding: 0.75rem 1.25rem;
    font-size: 14px;
  }
}

.btn-login:hover {
  background-color: #e64a19;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  cursor: pointer;
  color: #333;

  i {
    font-size: 1.8rem;
  }
}

/* Responsive Styles */
@include media-breakpoint-down(xl) {
  .mobile-menu-toggle {
    display: block;
  }

  .nav-links {
    position: absolute;
    top: 80px;
    left: 0;
    right: 0;
    background-color: white;
    flex-direction: column;
    align-items: flex-start;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-height: 0;
    padding: 0;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transition: max-height 0.35s ease-in-out, opacity 0.35s ease-in-out, padding 0.35s ease-in-out, visibility 0s 0.35s;
    z-index: 10;
  }

  .nav-links.mobile-open {
    max-height: calc(100vh - 80px);
    padding: 1rem;
    opacity: 1;
    visibility: visible;
    overflow-y: auto;
    transition: max-height 0.35s ease-in-out, opacity 0.35s ease-in-out, padding 0.35s ease-in-out, visibility 0s;
  }

  .dropdown {
    width: 100%;
  }

  .dropdown-toggle {
    width: 100%;
    justify-content: space-between;
  }

  .dropdown-menu {
    position: static;
    box-shadow: none;
    width: 100%;
    margin-top: 0;
    padding-left: 0;
    border-radius: 0;
  }

  .dropdown-menu.dropdown-open {
    margin-top: 0.5rem;
    padding-left: 1rem;
  }

  .action-buttons {
    width: 100%;
    flex-direction: column;
    margin-left: 0;
    gap: 0.75rem;
  }

  .btn {
    width: 100%;
    text-align: center;
  }
}

.nav-link-active {
  background-color: rgb(225, 255, 169);
}
</style>
