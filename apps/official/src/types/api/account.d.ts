declare namespace Api {
  interface AccountListReq {
    name?: string
    account_code?: string
    sort_by?: string
    sort_order?: string
    page?: number
    per_page?: number
  }

  interface AccountDetailReq {
    id: number
  }

  interface AddAccountReq {
    account_code: string
    name: string
    type: string
  }

  interface UpdateAccountReq {
    id: number
    account_code: string
    name: string
    type: string
  }

  interface DeleteAccountReq {
    id: number | string
  }
}

declare namespace Account {
  interface Info {
    id: number
    merchant_id: string
    tenant_id: string
    account_id: string
    account_code: string
    name: string
    type: string
    status: string
    bank_account_number: string
    bank_account_type: string
    created_at: string
    updated_at: string
  }
}
