@use "sass:map";

// 断点定义
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px,
  xxxl: 1600px
);

// 向上断点 (min-width)
@mixin media-breakpoint-up($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    $min-width: map.get($breakpoints, $breakpoint);
    @media (min-width: $min-width) {
      @content;
    }
  } @else {
    @warn "无效的断点: #{$breakpoint}。";
  }
}

// 向下断点 (max-width)
@mixin media-breakpoint-down($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    $max-width: map.get($breakpoints, $breakpoint) - 0.02;
    @media (max-width: $max-width) {
      @content;
    }
  } @else {
    @warn "无效的断点: #{$breakpoint}。";
  }
}

// 区间断点 (min-width and max-width)
@mixin media-breakpoint-between($lower, $upper) {
  @if map.has-key($breakpoints, $lower) and map.has-key($breakpoints, $upper) {
    $min-width: map.get($breakpoints, $lower);
    $max-width: map.get($breakpoints, $upper) - 0.02;
    @media (min-width: $min-width) and (max-width: $max-width) {
      @content;
    }
  } @else {
    @warn "无效的断点: #{$lower} 或 #{$upper}。";
  }
} 