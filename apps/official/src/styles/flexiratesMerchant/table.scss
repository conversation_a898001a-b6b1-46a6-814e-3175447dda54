.layout-flexirates-merchant-wrapper {
    .p-datatable {
        --frozen-column-border-color: var(--colors-gray);

        &.p-datatable-striped {
            .p-datatable-table-container {
                .p-datatable-thead {
                    tr {
                        th {
                            &:first-child {
                                border-bottom-left-radius: 8px;
                            }

                            &:last-child {
                                border-bottom-right-radius: 8px;
                            }

                            &.p-datatable-frozen-column {
                                border: none;
                                padding: 0;
                                border-bottom: 1px solid var(--frozen-column-border-color);
                            }
                        }
                    }

                    .p-datatable-column-header-content {
                        border-right: 1px solid #d9d9d9;
                        padding: 8px 14px;

                        &:has(.p-checkbox) {
                            border-right: none;
                        }
                    }

                    .p-datatable-frozen-column {
                        .p-datatable-column-header-content {
                            border-right: none;
                        }
                    }
                }
            }
        }

        .p-datatable-table-container {
            border-radius: 8px;
            border: 1px solid var(--colors-gray);
            max-width: 100%;
            --p-datatable-header-cell-background: #f5f5ff;
            --p-datatable-header-cell-selected-background: #f5f5ff;

            .p-datatable-thead {
                .p-datatable-column-header-content {
                    border-right: 1px solid #d9d9d9;
                    padding: 8px 14px;

                    &:has(.p-checkbox) {
                        border-right: none;
                    }
                }

                .p-datatable-frozen-column {
                    .p-datatable-column-header-content {
                        border-right: none;
                    }
                }
            }

            .p-datatable-header-cell {
                padding-left: 0;
                padding-right: 0;
                border-bottom: 1px solid var(--colors-gray);

                &:last-child {
                    border-right: none;
                }
            }

            .p-datatable-tbody {
                --p-datatable-row-striped-background: #f5f5ff;

                .p-datatable-frozen-column {
                    background: white;
                }

                .p-row-odd {
                    .p-datatable-frozen-column {
                        background: #f5f5ff;
                    }
                }

                tr {
                    td {
                        border-bottom: none;

                        .p-row-odd {
                            background: #f5f5ff !important;
                        }
                    }
                }
            }
        }
    }
}