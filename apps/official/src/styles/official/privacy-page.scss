.privacy-page {
    width: 100%;
    background-color: var(--bg-colors-white);

    .accent-dot {
        color: #ff5722;
    }

    .privacy-header {
        position: relative;
        background-color: #19164b;
        color: #e1ffa9;
        padding: 130px 20px;
    }

    .privacy-header-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .privacy-header-content h1 {
        font-size: 4rem;
        font-weight: 900;
    }

    .privacy-header-right::before {
        content: '';
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background-image: url('@/assets/official/bg/ultra_Contact BG.png');
        background-repeat: no-repeat;
        background-size: 100% 160%;
        background-position: top;

    }

    .privacy-body {
        padding-block: 50px;
        font-size: 16px;
        line-height: 24px;
        font-weight: 500;
    }

    .privacy-body p {
        margin-bottom: 50px;
        font-size: 16px;
    }

    .privacy-body h4 {
        font-size: 1.5rem;
        line-height: 1.2;
        margin-bottom: .5rem;

    }

    .privacy-body h5 {
        font-size: 1.25rem;
        line-height: 1.2;
        margin-bottom: .5rem;
    }

    .privacy-body a {
        text-decoration: underline;
    }

    .privacy-body p {
        margin-bottom: 50px;

    }
    .privacy-body section{
        margin-bottom: 50px;
    }

    .first-level {
        list-style: disc;
        margin-bottom: 1rem;
    }

    .second-level {
        list-style: circle;

    }


    .first-level,
    .second-level {
        padding-left: 2rem;
    }
    .paragraph{
        padding-left: 2rem;
    }
}