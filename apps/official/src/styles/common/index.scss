 
html {
    height: 100%;
    font-size: 14px;
    overflow-x: hidden;
}

body {
    --colors-primary: #181349;
    --colors-primary-1: #1B1548;
    --colors-info: #09DEFF;
    --colors-warn: #FE4C1C;
    --colors-pink: #FFE3E8;
    --colors-green: #E1FFA9;
    --colors-white: #ffffff;
    --colors-gray: #545454;
    --colors-dark-gray: #3b3b3b;
    --bg-colors-white: #f5f5ff;
    --sidebar-bg: var(--colors-white);
    --header-bg: var(--colors-white);
    --body-bg: var(--bg-colors-white);
    --text-primary: var(--p-primary-300);
    --surface-hover: var(--p-surface-200);
    --p-progressspinner-color-1: var(--p-primary-300);
    --menu-item-color: rgb(84, 84, 84);
    --swiper-pagination-color: var(--colors-warn);
    --p-textarea-focus-border-color: var(--colors-warn);
    --p-inputtext-focus-border-color: var(--colors-warn);
    --p-select-focus-border-color: var(--colors-warn);
    --p-select-overlay-background: var(--colors-pink);
    --p-radiobutton-checked-border-color: var(--colors-warn);
    --p-radiobutton-checked-background: var(--colors-warn);
    --p-radiobutton-checked-hover-border-colo: var(--colors-warn);
    --p-radiobutton-checked-hover-background: var(--colors-warn);
    --p-radiobutton-border-color: var(--colors-warn);
    --p-radiobutton-checked-hover-border-color: var(--colors-warn);
    --p-radiobutton-focus-ring-color: var(--colors-warn);
    --p-inputtext-border-color: var(--colors-gray);
    --p-textarea-border-color: var(--colors-gray);
    --p-radiobutton-border-color: var(--colors-gray);
    --p-select-border-color: var(--colors-gray);
    --p-inputgroup-addon-border-color: var(--colors-gray);
    color: var(--colors-primary);

    --border-radius: 16px;
    margin: 0;
    font-family: 'Futura', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

.p-select-overlay {
    --p-select-overlay-border-radius: 8px;
    --p-select-overlay-border-color: rgba(255, 227, 232, 1);
}

.p-select-list {
    background-color: rgba(255, 227, 232, 1);
    --p-select-option-border-radius: 0;
    --p-select-option-selected-background: rgba(255, 200, 200, 1);
    --p-select-list-padding: 8px 16px;
    border-radius: 8px;
    --p-select-option-padding: 0.75rem;

    .p-select-option {
        border-bottom: 1px solid var(--colors-gray);

        &:last-child {
            border-bottom: none;
        }
    }
}