@use '../mixins/breakpoints' as *;

.auth-container {
  min-height: 100vh;
  background: var(--bg-colors-white);
  position: relative;

  .logo-wrap {
    position: absolute;
    top: 44px;
    left: 92px;
    z-index: 1;
    width: 250px;
    border-radius: 8px;
    overflow: hidden;
  }

  .auth-bg-wrap {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 963px;
    top: 0;
    z-index: 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }


  .login-form {
    display: flex;
    flex-direction: column;
    gap: 20px
  }


  .auth-content-wrap {
    position: absolute;
    top: 208px;
    left: 92px;
    display: flex;
    justify-content: center;
    align-items: center;

    .auth-content {
      width: 680px;
      background-color: var(--colors-white);
      padding: 48px;
      border-radius: 24px;

      .title {
        margin-top: 0;
        margin-bottom: 0.25rem;
        font-size: 54px;
        font-weight: 800;
      }

      .subtitle {
        font-size: 18px;
        margin-bottom: 3rem;
        color: var(--colors-warn);

        .subtitle-account {
          color: var(--colors-gray);
        }
      }

      .forgot-subtitle {
        font-size: 18px;
        color: var(--p-gray-500);
        margin-top: 1.5rem;
        margin-bottom: 3rem;
      }

      .auth-form {
        .submit-button {
          width: 100%;
        }
      }

      .new-password-form {
        .form-label {
          font-weight: 600;
          font-size: 22px;
          padding-left: 1rem;
        }

        .strength-container {
          // margin-top: 0.2rem;
          padding: 0 1rem;
        }

        .strength-bars {
          height: 4px;
          display: flex;
          gap: 4px;
          margin-bottom: 0.25rem;
        }

        .strength-segment {
          flex: 1;
          height: 100%;
          background-color: #e5e7eb;
          border-radius: 2px;
          transition: all 0.3s ease;
        }

        .strength-indicator {
          display: block;
          color: #64748b;
          font-size: 14px;
          margin-top: .5rem;

          span {
            font-weight: 700;
          }
        }
      }

      .form-input {
        border-radius: 22px;
        font-size: 18px;
        height: 64px;
        font-weight: 600;
        --p-inputtext-padding-y: 16px;
        --p-inputtext-padding-x: 24px;
        --p-inputtext-border-color: var(--p-gray-800);
      }

      :deep(.p-password) {
        .p-inputtext {
          border-radius: 18px;
          font-size: 18px;
          height: 64px;
          font-weight: 600;
          --p-inputtext-padding-y: 16px;
          --p-inputtext-padding-x: 24px;
          --p-inputtext-border-color: var(--p-gray-800);
        }
      }

      .login-submit {
        border-radius: 4px;
        font-size: 14px;
        height: 64px;
        --p-button-padding-y: 16px;
        --p-button-padding-x: 24px;
        --p-button-border-color: var(--p-gray-800);
      }

      .back-to-login {
        border-radius: 18px;
        font-size: 18px;
        height: 48px;
        --p-button-padding-y: 16px;
        --p-button-padding-x: 24px;
        --p-button-border-color: var(--p-gray-800);
        --p-button-info-color: #181349;
      }

      .tools {
        color: var(--menu-item-color);

        :deep(.p-button-label) {
          color: var(--menu-item-color);
        }
      }
    }
  }

}

/* 响应式布局样式 */
@include media-breakpoint-down(xxl) {
  .auth-container {
    .auth-content-wrap {
      .auth-content {
        width: 520px;
        padding: 36px;

        .title {
          font-size: 40px;
        }

        .subtitle {
          font-size: 20px;
          margin-bottom: 2.5rem;
        }
      }
    }
  }
}

@include media-breakpoint-down(lg) {
  .auth-container {
    .auth-bg-wrap {
      width: 963px;
    }

    .logo-wrap {
      left: 50%;
      transform: translateX(-50%);
    }

    .auth-content-wrap {
      left: 50%;
      transform: translateX(-50%);

      .auth-content {
        width: 460px;
        padding: 32px;
      }
    }
  }
}

@include media-breakpoint-down(md) {
  .auth-container {
    display: flex;
    flex-direction: column;

    .logo-wrap {
      position: relative;
      top: 24px;
      left: 0;
      margin: 0 auto;
      width: 180px;
    }

    .auth-bg-wrap {
      position: absolute;
      width: 100%;
      height: 30%;
      top: auto;
      bottom: 0;
      opacity: 0.3;
    }

    .auth-content-wrap {
      top: 20%;
      left: 50%;
      transform: translateX(-50%);

      .auth-content {
        width: 100%;
        max-width: 420px;
        padding: 24px;

        .title {
          font-size: 36px;
        }

        .subtitle {
          font-size: 18px;
          margin-bottom: 2rem;
        }
      }
    }
  }
}

@include media-breakpoint-down(sm) {
  .auth-container {
    .logo-wrap {
      top: 10%;
      left: 50%;
      transform: translateX(-50%);
      width: 180px;
      margin: 0;
    }

    .auth-content-wrap {
      top: 20%;
      left: 50%;
      transform: translateX(-50%);

      .auth-content {
        padding: 20px;

        .title {
          font-size: 32px;
        }

        .subtitle {
          font-size: 16px;
          margin-bottom: 1.5rem;
        }

        .login-submit {
          height: 44px;
          font-size: 16px;
        }

        .form-input,
        :deep(.p-password) .p-inputtext {
          height: 44px;
          font-size: 16px;
          --p-inputtext-padding-y: 12px;
          --p-inputtext-padding-x: 18px;
        }
      }
    }
  }
}