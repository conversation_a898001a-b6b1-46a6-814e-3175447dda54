import { GET, POST } from '@/services/http'

/**
 * Get invoice list
 */
export const getInvoiceList = (params: Api.InvoiceListReq) => {
  return GET<CommonListRes<Invoice.Info[]>>('xero/getInvoiceList', { params })
}

/**
 * Get invoice detail
 */
export const getInvoiceDetail = (params: Api.InvoiceDetailReq) => {
  return GET<Invoice.Info>('xero/getInvoiceDetail', { params })
}

export const sendInvoiceEmail = (data: Api.InvoiceDetailSendInvoiceEmailReq) => {
  return POST('xero/sendInvoiceEmail', data)
}

export const getInvoiceDashboard = () => {
  return GET<Invoice.DashboardData[]>('xero/getInvoiceStatus')
}
