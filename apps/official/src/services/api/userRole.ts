import { GET, POST } from '../http'

// Get merchant role list with pagination and filtering
export const getList = (params: Api.UserRoleListReq) => GET<CommonListRes<UserRole.Info[]>>('/role/list', { params })

// Create new merchant role
export const create = (data: UserRole.CreateReq) => POST<UserRole.Info>('/role/create', data)

// Update merchant role
export const update = (id: string, data: UserRole.UpdateReq) => POST<UserRole.Info>(`/role/update`, { ...data, role_id: id })

// Delete merchant role
export const remove = (id: string) => POST<void>(`/role/delete`, { role_id: id })

// Get merchant role detail
export const detail = (id: string) => GET<UserRole.Info>(`/role/detail`, { params: { role_id: id } })

// Get merchant role permission list
export const getPermissionList = () => GET<UserRole.PermissionListRes[]>('permission/list')
