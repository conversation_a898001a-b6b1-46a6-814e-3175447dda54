import { GET, POST } from '../http'

export const xeroSubmitPayment = (data: Api.XeroSubmitPaymentReq) => POST<{ url: string }>('xero/submit', data)

export const xeroDisconnect = () => GET('xero/break')

export const getXeroInfo = () => GET<Api.XeroInfoRes>('xero/info')

export const refreshXeroStatus = () => GET('xero/refreshAccessToken')

export const getXeroChartOfAccounts = () => GET<Api.XeroChartOfAccountsRes[]>('xero/getAllAccountCode')

export const getXeroInvoiceTemplates = () => GET<Api.XeroInvoiceTemplatesRes[]>('xero/getThemeList')

export const createXeroInvoice = (data: Api.XeroCreateInvoiceReq) => POST<{ id: string }>('xero/addInvoice', data)

export const getXeroInvoiceConfig = (query: Api.GetXeroInvoiceConfigReq) => GET<Api.InvoiceConfigRes>('xero/getInvoicePaymentDetail', { params: query })

export const pay = (data: Api.XeroPayReq) => POST<CommonRes<Api.XeroPayRes>>('xero/invoicePaymentSubmit', data)

export const syncXeroData = () => POST<CommonRes<Api.XeroPayRes>>('xero/syncChannelData')

export const getXeroAccountSettings = () => GET<Api.XeroSettingsRes>('xero/getAccountConfig')

export const updateXeroSettings = (data: { update_list: Api.XeroSettingsRes['branding_themes'] }) => POST<CommonRes<Api.XeroSettingsRes>>('xero/updateBrandingThemeConfig', data)

export const updateXeroBankAccount = (data: { bank_account_id: number, logo: string, file_name: string }) => POST<CommonRes<Api.XeroSettingsRes>>('xero/updateInvoiceChannelConfig', data)
