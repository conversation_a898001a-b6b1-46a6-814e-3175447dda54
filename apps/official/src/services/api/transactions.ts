import { GET, POST } from '@/services/http'

export const getList = (params: Api.TransactionListReq) => GET<Api.TransactionListRes>('/trans/list', { params })
export const getDetail = (plan_id: string) => GET<Plan.Info>('/plan/detail', { params: { plan_id } })
export const getTransactionDetail = (id: string) => GET<Transaction.Info>('/trans/detail', { params: { id } })
export const refund = (data: Api.RefundReq) => POST('/trans/refund', data)
export const exportTransactions = (params: Api.TransactionListReq) => GET<Api.TransactionListRes>('/trans/export', { params })
