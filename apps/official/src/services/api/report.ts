import { GET } from '@/services/http'

export const getRevenue = () => GET<Report.Revenue>('/report/revenue', { })
export const getRevenueDetail = () => GET<Report.Revenue>('/report/revenueDetail', { })
export const getTransactionPayout = (params: Api.TransactionPayoutReq) => GET<Report.transactionPayout>('/report/transactionPayout', { params })
export const getSubscription = (params: Api.SubscriptionReq) => GET<Report.subscription>('/report/subscription', { params })
