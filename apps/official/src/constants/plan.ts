import type dayjs from 'dayjs'

export enum BillingPeriodType {
  Daily = 2,
  Weekly = 3,
  Fortnightly = 4,
  Monthly = 5,
  Yearly = 8,
  Every3Months = 6,
  Every6Months = 7,
  Custom = 12,
}

export enum BillingPeriodCustomType {
  Day = 1,
  Week = 2,
  Month = 3,
  Year = 4,
}

export enum RecurringPricingModel {
  Flat = 1,
  StandardPricing = 2,
  TieredPricing = 3,
}

export enum UnitBasedPricingModel {
  StandardPricing = 2,
  TieredPricing = 3,
}

export enum UnitBasedModelUnitType {
  Unit = 'Unit',
  Hours = 'Hours',
  User = 'User',
  Custom = 'Custom',
}

export enum ScheduleType {
  Recurring = 1,
  OneOff = 2,
  UnitBased = 3,
}

export enum RecurringTieredPaymentMethod {
  Volume = 1,
  Graduated = 2,
}

export enum PlanStatus {
  ACTIVE = 1,
  INACTIVE = 2,
  ARCHIVE = 3,
}

export enum CustomerPlanStatus {
  ACTIVE = 1,
  INACTIVE = 2,
  PROCESSING = 3,
  PROCESSED = 4,
  ON_HOLD = 5,
  COMPLETED = 6,
  CANCELLED = 7,
}

export enum PlanEndDateType {
  SpecifyByEndDate = 1,
  SpecifyByTerm = 2,
  GoodTillCancel = 3,
}

export const periodMap: Record<string, string> = {
  [BillingPeriodType.Daily]: 'Daily',
  [BillingPeriodType.Weekly]: 'Weekly',
  [BillingPeriodType.Fortnightly]: 'Fortnightly',
  [BillingPeriodType.Monthly]: 'Monthly',
  [BillingPeriodType.Yearly]: 'Yearly',
  [BillingPeriodType.Every3Months]: 'Every 3 months',
  [BillingPeriodType.Every6Months]: 'Every 6 months',
  [BillingPeriodType.Custom]: 'Custom',
}

// 单位的映射
export const customPeriodMap: Record<BillingPeriodCustomType, dayjs.ManipulateType> = {
  [BillingPeriodCustomType.Day]: 'day',
  [BillingPeriodCustomType.Week]: 'week',
  [BillingPeriodCustomType.Month]: 'month',
  [BillingPeriodCustomType.Year]: 'year',
}
