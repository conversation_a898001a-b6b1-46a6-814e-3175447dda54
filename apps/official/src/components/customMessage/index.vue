<script setup lang="ts">
import { useToast } from 'primevue/usetoast'

const toast = useToast()

// 定义消息类型
export interface MessageOptions {
  message: string
  type?: 'success' | 'error'
  duration?: number
  closable?: boolean
}

// 显示消息的方法
const showMessage = (options: MessageOptions) => {
  toast.add({
    group: 'headless',
    detail: options.type,
    summary: options.message,
    life: options.duration || 0, // 默认为0，表示不自动关闭
    closable: options.closable ?? true, // 默认显示关闭按钮
  })
}

// 导出方法供其他组件使用
defineExpose({
  showMessage,
})
</script>

<template>
  <div class="card flex justify-center">
    <Toast
      :unstyled="true"
      position="top-center"
      :style="{ top: '120px', left: '50%', transform: 'translateX(-50%)' }"
      group="headless"
    >
      <template #container="{ message, closeCallback }">
        <section
          class="z-1000 flex items-center justify-between py-6 px-4 w-188 " :class="[
            {
              'bg-[#e1ffa9]': message.detail === 'success',
              'bg-[#ff3131]': message.detail === 'error',
            },
          ]"
        >
          <div class="flex items-center gap-3">
            <i
              class="text-white dark:text-black text-xl" :class="[
                {
                  '': message.detail === 'success',
                  'pi pi-exclamation-triangle': message.detail === 'error',
                },
              ]"
            />
            <span
              class="font-bold " :class="[
                {
                  'text-[#181349]': message.detail === 'success',
                  'text-[#fff]': message.detail === 'error',
                },
              ]"
            >
              {{ message.summary }}
            </span>
          </div>
          <button
            v-if="message.closable"
            class="text-white dark:text-black hover:opacity-70"
            @click="closeCallback"
          >
            <i
              class="pi pi-times text-lg" :class="[
                {
                  'text-[#181349]': message.detail === 'success',
                  'text-[#fff]': message.detail === 'error',
                },
              ]"
            />
          </button>
        </section>
      </template>
    </Toast>
  </div>
</template>

<style scoped>
</style>
