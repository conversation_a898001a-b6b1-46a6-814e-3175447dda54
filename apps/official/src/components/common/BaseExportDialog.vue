<script setup lang="ts">
import { ref } from 'vue'

interface Props {
  title?: string
  exportButtonLabel?: string
  cancelButtonLabel?: string
  loading?: boolean
  exportLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Export Options',
  exportButtonLabel: 'EXPORT',
  cancelButtonLabel: 'CANCEL',
  loading: false,
  exportLoading: false,
})

const emit = defineEmits<{
  (e: 'export', format: 'csv' | 'xls' | 'pdf'): void
}>()

const visible = ref(false)
const selectedFormat = ref<string | null>('csv')
const fileFormats = [
  { name: 'CSV', value: 'csv' },
  // { name: 'XLS', value: 'xls' },
  // { name: 'PDF', value: 'pdf' },
]

const openDialog = () => {
  visible.value = true
  selectedFormat.value = 'csv'
}

const handleExport = () => {
  if (selectedFormat.value) {
    emit('export', selectedFormat.value as 'csv' | 'xls' | 'pdf')
    visible.value = false
  }
}
</script>

<template>
  <div class="export-dialog">
    <Button label="Export" icon="pi pi-share-alt" :loading="props.loading" @click="openDialog" />
    <Dialog
      v-model:visible="visible"
      :header="props.title"
      :modal="true"
      :closable="true"
      :style="{ width: '300px' }"
      class="export-dialog"
    >
      <div class="export-options">
        <div class="file-type-options">
          <div v-for="format in fileFormats" :key="format.value" class="format-option">
            <RadioButton
              v-model="selectedFormat"
              :value="format.value"
              :input-id="`format_${format.value}`"
            />
            <label :for="`format_${format.value}`" class="ml-2">{{ format.name }}</label>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <Button
            :label="props.cancelButtonLabel"
            severity="secondary"
            :loading="props.exportLoading"
            class="dialog-cancel-button w-26"
            @click="visible = false"
          />
          <Button
            :label="props.exportButtonLabel"
            severity="warn"
            :disabled="!selectedFormat"
            :loading="props.exportLoading"
            class="w-26"
            @click="handleExport"
          />
        </div>
      </template>
    </Dialog>
  </div>
</template>
