<script setup lang="ts">
import Button from 'primevue/button'
import ProgressSpinner from 'primevue/progressspinner'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  loadingMessage: '',
  error: null,
  errorTitle: '',
  errorMessage: '',
  retryable: true,
  isEmpty: false,
  emptyMessage: '',
})

defineEmits<{
  /**
   * 重试事件
   */
  (e: 'retry'): void
}>()

const { t } = useI18n()

interface Props {
  /**
   * 加载状态
   */
  loading?: boolean
  /**
   * 加载中显示的消息
   */
  loadingMessage?: string
  /**
   * 错误信息
   */
  error?: string | Error | null
  /**
   * 错误标题
   */
  errorTitle?: string
  /**
   * 错误详细信息
   */
  errorMessage?: string
  /**
   * 是否允许重试
   */
  retryable?: boolean
  /**
   * 是否为空数据状态
   */
  isEmpty?: boolean
  /**
   * 空数据状态显示的消息
   */
  emptyMessage?: string
}

// 如果错误是Error对象，获取其message
const errorMsg = computed(() => {
  if (!props.error) { return '' }
  return props.error instanceof Error ? props.error.message : props.error
})
</script>

<template>
  <div class="base-loading-wrap">
    <!-- 加载中状态 -->
    <div v-if="loading" class="loading-state">
      <div class="spinner">
        <ProgressSpinner />
      </div>
      <p v-if="loadingMessage" class="loading-message">
        {{ loadingMessage }}
      </p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state">
      <div class="error-icon">
        <i class="pi pi-exclamation-triangle" />
      </div>
      <h3 class="error-title">
        {{ errorTitle || t('common.loadFailed') }}
      </h3>
      <p class="error-message">
        {{ errorMessage || errorMsg }}
      </p>
      <Button v-if="retryable" class="retry-button" @click="$emit('retry')">
        {{ t('common.retry') }}
      </Button>
    </div>

    <!-- 无数据状态 -->
    <div v-else-if="isEmpty" class="empty-state">
      <div class="empty-icon">
        <i class="pi pi-inbox" />
      </div>
      <p class="empty-message">
        {{ emptyMessage || t('common.noData') }}
      </p>
    </div>

    <!-- 正常内容展示 -->
    <div v-else class="content-wrapper">
      <slot />
    </div>
  </div>
</template>

<style scoped>
.base-loading-wrap {
  position: relative;
  width: 100%;
  min-height: 200px;
}

.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 200px;
  padding: 2rem;
  text-align: center;
}

.spinner {
  margin-bottom: 1rem;
}

.loading-message {
  color: var(--text-color-secondary);
  font-size: 0.875rem;
}

.error-icon,
.empty-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--text-color-secondary);
}

.error-title {
  margin: 0 0 0.5rem 0;
  font-weight: 600;
  color: var(--text-color);
}

.error-message {
  margin: 0 0 1.5rem 0;
  color: var(--text-color-secondary);
}

.empty-message {
  color: var(--text-color-secondary);
}

.retry-button {
  margin-top: 1rem;
}
</style>
