<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'BaseTag',
  props: {
    text: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: 'default',
      validator: (value: string) => ['default', 'paid', 'upcoming', 'failed'].includes(value),
    },
    clickable: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['click'],
  setup(props, { emit }) {
    const handleClick = () => {
      if (props.clickable) {
        emit('click')
      }
    }

    return {
      handleClick,
    }
  },
})
</script>

<template>
  <div
    class="base-tag"
    :class="[
      `base-tag--${type}`,
      { 'base-tag--clickable': clickable },
    ]"
    @click="handleClick"
  >
    <slot>{{ text }}</slot>
  </div>
</template>

<style scoped>
.base-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  padding: 6px 16px;
  font-size: 14px;
  font-weight: 500;
  min-width: 80px;
  text-align: center;
  white-space: nowrap;
  transition: all 0.2s ease;
  height: 100%;
}

.base-tag--clickable {
  cursor: pointer;
}

.base-tag--clickable:hover {
  opacity: 0.8;
}

/* 标签类型样式 */
.base-tag--default {
  background-color: #f5f5f5;
  color: #666666;
}

.base-tag--paid {
  background-color: #e9fcd4;
  color: #389e0d;
}

.base-tag--upcoming {
  background-color: #ff6434;
  color: white;
}

.base-tag--failed {
  background-color: #f5222d;
  color: white;
}
</style>
