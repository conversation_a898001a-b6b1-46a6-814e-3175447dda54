<script setup lang="ts">
import type { Placement, Strategy } from '@floating-ui/vue'
import type { CSSProperties, PropType } from 'vue'
import { arrow, autoUpdate, flip, offset, shift, useFloating } from '@floating-ui/vue'
import { computed, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue'

type TriggerType = 'click' | 'focus' | 'hover' | 'contextmenu'
type Effect = 'dark' | 'light' | string

const props = defineProps({
  trigger: {
    type: String as PropType<TriggerType>,
    default: 'hover',
    validator: (value: string) => ['click', 'focus', 'hover', 'contextmenu'].includes(value),
  },
  title: {
    type: String,
    default: '',
  },
  effect: {
    type: String as PropType<Effect>,
    default: 'light',
  },
  content: {
    type: String,
    default: '',
  },
  width: {
    type: [String, Number],
    default: 'auto',
  },
  placement: {
    type: String as PropType<Placement>,
    default: 'bottom',
    validator: (value: string) => [
      'top',
      'top-start',
      'top-end',
      'bottom',
      'bottom-start',
      'bottom-end',
      'left',
      'left-start',
      'left-end',
      'right',
      'right-start',
      'right-end',
    ].includes(value),
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  visible: {
    type: Boolean,
    default: undefined,
  },
  offset: {
    type: Number,
    default: 12,
  },
  transition: {
    type: String,
    default: 'el-fade-in-linear',
  },
  showArrow: {
    type: Boolean,
    default: true,
  },
  popperOptions: {
    type: Object,
    default: () => ({
      modifiers: [{
        name: 'computeStyles',
        options: {
          gpuAcceleration: false,
        },
      }],
    }),
  },
  popperClass: {
    type: String,
    default: '',
  },
  popperStyle: {
    type: [String, Object],
    default: '',
  },
  showAfter: {
    type: Number,
    default: 0,
  },
  hideAfter: {
    type: Number,
    default: 200,
  },
  autoClose: {
    type: Number,
    default: 0,
  },
  tabindex: {
    type: [Number, String],
    default: 0,
  },
  teleported: {
    type: Boolean,
    default: true,
  },
  persistent: {
    type: Boolean,
    default: true,
  },
  virtualTriggering: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void
  (event: 'show'): void
  (event: 'beforeEnter'): void
  (event: 'afterEnter'): void
  (event: 'hide'): void
  (event: 'beforeLeave'): void
  (event: 'afterLeave'): void
}>()

const referenceRef = ref<HTMLElement | null>(null)
const floatingRef = ref<HTMLElement | null>(null)
const arrowRef = ref<HTMLElement | null>(null)
const isOpen = ref(false)

// Computed style for width
const widthStyle = computed(() => {
  if (typeof props.width === 'number') {
    return `${props.width}px`
  }
  return props.width
})

// Controlled mode or uncontrolled mode
watch(() => props.visible, (val) => {
  if (val !== undefined) {
    isOpen.value = val
  }
})

// Watch isOpen and emit update:visible event
watch(isOpen, (val) => {
  emit('update:visible', val)
  if (val) {
    emit('show')
  }
  else {
    emit('hide')
  }
})

// Floating UI setup
// 使用 ref 存储 floating 配置，以便动态更新
const placementRef = ref(props.placement as Placement)

const {
  floatingStyles,
  middlewareData,
  placement: computedPlacement,
  isPositioned,
  update: updateFloating,
} = useFloating(referenceRef, floatingRef, {
  placement: placementRef,
  strategy: 'absolute' as Strategy,
  middleware: [
    offset(props.offset),
    flip(),
    shift({ padding: 5 }),
    ...(props.showArrow ? [arrow({ element: arrowRef })] : []),
  ],
  whileElementsMounted: autoUpdate,
})

// 监听 placement 变化，更新 placementRef 并重新计算位置
watch(() => props.placement, (newPlacement) => {
  placementRef.value = newPlacement as Placement
  // 手动触发位置更新
  nextTick(() => {
    if (updateFloating) {
      updateFloating()
    }
  })
})

// Convert popperStyle prop to CSSProperties
const normalizedPopperStyle = computed(() => {
  if (typeof props.popperStyle === 'string') {
    try {
      return JSON.parse(props.popperStyle)
    }
    catch {
      return {}
    }
  }
  return props.popperStyle
})

// Combined styles for the floating element
const combinedStyles = computed(() => {
  return {
    ...floatingStyles.value,
    width: widthStyle.value,
    ...normalizedPopperStyle.value,
  } as CSSProperties
})

// Arrow styles
const arrowStyles = computed(() => {
  if (!props.showArrow) { return {} }

  // 添加安全检查，避免访问 undefined 的属性
  if (!middlewareData.value?.arrow) { return {} }

  const { x, y } = middlewareData.value.arrow

  // 基本样式
  const isDark = props.effect === 'dark'

  const staticStyles = {
    position: 'absolute',
    width: '8px',
    height: '8px',
    backgroundColor: isDark ? 'var(--tw-gray-800)' : 'white', // 根据主题设置颜色
    zIndex: 1,
  }

  // 根据弹出框的位置确定箭头的方向和位置
  const placement = computedPlacement.value
  const transform = 'rotate(45deg)'
  let top: string | undefined
  let right: string | undefined
  let bottom: string | undefined
  let left: string | undefined

  // 根据不同的位置设置箭头的位置和边框样式
  let borderStyles = {}

  if (placement.includes('top')) {
    bottom = '-4px'
    borderStyles = {
      borderTop: 'none',
      borderLeft: 'none',
      borderRight: '1px solid var(--tw-gray-200)',
      borderBottom: '1px solid var(--tw-gray-200)',
    }
  }
  else if (placement.includes('bottom')) {
    top = '-4px'
    borderStyles = {
      borderBottom: 'none',
      borderRight: 'none',
      borderLeft: '1px solid var(--tw-gray-200)',
      borderTop: '1px solid var(--tw-gray-200)',
    }
  }
  else if (placement.includes('left')) {
    right = '-4px'
    borderStyles = {
      borderLeft: 'none',
      borderBottom: 'none',
      borderRight: '1px solid var(--tw-gray-200)',
      borderTop: '1px solid var(--tw-gray-200)',
    }
  }
  else if (placement.includes('right')) {
    left = '-4px'
    borderStyles = {
      borderRight: 'none',
      borderTop: 'none',
      borderLeft: '1px solid var(--tw-gray-200)',
      borderBottom: '1px solid var(--tw-gray-200)',
    }
  }

  return {
    ...staticStyles,
    ...borderStyles,
    transform,
    top: y != null ? `${y}px` : top,
    left: x != null ? `${x}px` : left,
    right,
    bottom,
  }
})

// Theme class
const themeClass = computed(() => {
  return props.effect === 'dark' ? 'is-dark' : 'is-light'
})

// Event handlers for different trigger types
let showTimer: number | undefined
let hideTimer: number | undefined
let autoCloseTimer: number | undefined

const clearTimers = () => {
  if (showTimer) {
    window.clearTimeout(showTimer)
    showTimer = undefined
  }
  if (hideTimer) {
    window.clearTimeout(hideTimer)
    hideTimer = undefined
  }
  if (autoCloseTimer) {
    window.clearTimeout(autoCloseTimer)
    autoCloseTimer = undefined
  }
}

const show = () => {
  if (props.disabled) { return }

  clearTimers()

  if (props.showAfter === 0) {
    isOpen.value = true
    startAutoClose()
  }
  else {
    showTimer = window.setTimeout(() => {
      isOpen.value = true
      startAutoClose()
    }, props.showAfter)
  }
}

const hide = () => {
  if (props.disabled) { return }

  clearTimers()

  if (props.hideAfter === 0) {
    isOpen.value = false
  }
  else {
    hideTimer = window.setTimeout(() => {
      isOpen.value = false
    }, props.hideAfter)
  }
}

const startAutoClose = () => {
  if (props.autoClose > 0) {
    autoCloseTimer = window.setTimeout(() => {
      isOpen.value = false
    }, props.autoClose)
  }
}

const onClickOutside = (event: MouseEvent) => {
  const target = event.target as Node
  if (
    isOpen.value
    && !props.virtualTriggering
    && referenceRef.value
    && floatingRef.value
    && !referenceRef.value.contains(target)
    && !floatingRef.value.contains(target)
  ) {
    hide()
  }
}

// Setup event handlers based on trigger type
onMounted(() => {
  // Add click outside listener
  document.addEventListener('click', onClickOutside)

  nextTick(() => {
    // If visible prop is provided and true, show popover
    if (props.visible !== undefined) {
      isOpen.value = props.visible
    }
  })
})

onBeforeUnmount(() => {
  // Clean up event listeners and timers
  document.removeEventListener('click', onClickOutside)
  clearTimers()
})

watch(isPositioned, (val) => {
  if (val) {
    emit('beforeEnter')
    nextTick(() => {
      emit('afterEnter')
    })
  }
  else {
    emit('beforeLeave')
    nextTick(() => {
      emit('afterLeave')
    })
  }
})

// Expose methods
defineExpose({
  hide,
})

// Event handlers based on trigger type
// 用于跟踪鼠标是否在参考元素或弹出元素上
const isOverReference = ref(false)
const isOverFloating = ref(false)

const onMouseEnter = () => {
  if (props.trigger === 'hover') {
    isOverReference.value = true
    show()
  }
}

const onMouseLeave = () => {
  if (props.trigger === 'hover') {
    isOverReference.value = false
    // 添加延迟检查，仅当鼠标既不在参考元素上也不在弹出元素上时才隐藏
    setTimeout(() => {
      if (!isOverReference.value && !isOverFloating.value) {
        hide()
      }
    }, 50) // 小延迟让鼠标有时间移动到弹出元素上
  }
}

const onPopoverMouseEnter = () => {
  if (props.trigger === 'hover') {
    isOverFloating.value = true
  }
}

const onPopoverMouseLeave = () => {
  if (props.trigger === 'hover') {
    isOverFloating.value = false
    // 如果鼠标也不在参考元素上，则隐藏
    if (!isOverReference.value) {
      hide()
    }
  }
}

const onClick = () => {
  if (props.trigger === 'click') {
    if (isOpen.value) {
      hide()
    }
    else {
      show()
    }
  }
}

const onFocus = () => {
  if (props.trigger === 'focus') { show() }
}

const onBlur = () => {
  if (props.trigger === 'focus') { hide() }
}

const onContextMenu = (e: MouseEvent) => {
  if (props.trigger === 'contextmenu') {
    e.preventDefault()
    if (isOpen.value) {
      hide()
    }
    else {
      show()
    }
  }
}
</script>

<template>
  <div class="base-popover-container">
    <div
      ref="referenceRef"
      class="base-popover-reference"
      :tabindex="!props.disabled ? props.tabindex : undefined"
      @mouseenter="onMouseEnter"
      @mouseleave="onMouseLeave"
      @click="onClick"
      @focus="onFocus"
      @blur="onBlur"
      @contextmenu="onContextMenu"
    >
      <slot name="reference" :is-open="isOpen" />
    </div>

    <!-- Floating element (popover content) -->
    <Teleport to="body" :disabled="!props.teleported">
      <transition
        :name="props.transition"
        @before-enter="emit('beforeEnter')"
        @after-enter="emit('afterEnter')"
        @before-leave="emit('beforeLeave')"
        @after-leave="emit('afterLeave')"
      >
        <div
          v-if="isOpen || props.persistent"
          v-show="isOpen"
          ref="floatingRef"
          class="base-popover" :class="[
            themeClass,
            props.popperClass,
          ]"
          :style="combinedStyles"
          role="tooltip"
          @mouseenter="onPopoverMouseEnter"
          @mouseleave="onPopoverMouseLeave"
        >
          <div v-if="props.title" class="base-popover-title">
            {{ props.title }}
          </div>
          <div class="base-popover-content">
            <slot>{{ props.content }}</slot>
          </div>
          <div v-if="props.showArrow" ref="arrowRef" class="base-popover-arrow" :style="arrowStyles" />
        </div>
      </transition>
    </Teleport>
  </div>
</template>

<style scoped>
.base-popover-container {
  display: contents;
}

.base-popover {
  position: absolute;
  background: white;
  min-width: 150px;
  border-radius: 4px;
  border: 1px solid var(--tw-gray-200);
  padding: 12px;
  z-index: 2000;
  color: var(--tw-gray-700);
  line-height: 1.4;
  text-align: justify;
  font-size: 14px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  word-break: break-all;
  box-sizing: border-box;
}

.base-popover.is-dark {
  background: var(--tw-gray-800);
  color: white;
}

.base-popover.is-light {
  background: white;
  color: var(--tw-gray-700);
}

.base-popover-title {
  color: var(--tw-gray-700);
  font-size: 16px;
  line-height: 1;
  margin-bottom: 12px;
  font-weight: bold;
}

.base-popover.is-dark .base-popover-title {
  color: white;
}

.base-popover-arrow {
  position: absolute;
  width: 8px !important;
  height: 8px !important;
  z-index: 1;
  transform-origin: center;
  visibility: visible !important;
}

.base-popover.is-light .base-popover-arrow {
  background-color: white;
}

.base-popover.is-dark .base-popover-arrow {
  background-color: var(--tw-gray-800);
}

/* Transition animations */
.el-fade-in-linear-enter-active,
.el-fade-in-linear-leave-active {
  transition: opacity 0.2s linear;
}

.el-fade-in-linear-enter-from,
.el-fade-in-linear-leave-to {
  opacity: 0;
}
</style>
