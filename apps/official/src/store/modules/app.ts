import { i18n } from '@/i18n'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // state
  const loading = ref(false)
  const locale = ref(localStorage.getItem('locale') || 'en')
  const keepAliveComponents = ref<string[]>([])
  const theme = ref(localStorage.getItem('theme') || 'Lara')
  const isShowCookiesPop = ref(true)
  const isMobileMenuVisible = ref(false)
  const currentClient = ref<'merchant' | 'flexiratesMerchant' | 'flexirates'>('merchant')

  // actions
  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }

  const setLocale = (newLocale: 'en' | 'zh') => {
    locale.value = newLocale
    localStorage.setItem('locale', newLocale)
    i18n.global.locale.value = newLocale
  }

  const setKeepAlive = (keeps: string[]) => {
    keepAliveComponents.value = keeps
  }

  const setTheme = (newTheme: string) => {
    theme.value = newTheme
    localStorage.setItem('theme', newTheme)
  }

  const toggleMobileMenu = () => {
    isMobileMenuVisible.value = !isMobileMenuVisible.value
  }

  const closeMobileMenu = () => {
    isMobileMenuVisible.value = false
  }

  const setIsShowCookiesPop = (isShow: boolean) => {
    isShowCookiesPop.value = isShow
  }

  const setCurrentClient = (client: 'merchant' | 'flexiratesMerchant' | 'flexirates') => {
    if (currentClient.value === client) {
      return
    }
    currentClient.value = client
  }

  // Initialize i18n
  if (i18n.global.locale.value !== locale.value) {
    i18n.global.locale.value = locale.value as 'en' | 'zh'
  }

  // getters
  const currentLocale = computed(() => locale.value)
  const isLoading = computed(() => loading.value)
  const currentTheme = computed(() => theme.value)

  return {
    // state
    loading,
    locale,
    theme,
    isMobileMenuVisible,
    isShowCookiesPop,
    currentClient,
    // actions
    setLoading,
    setLocale,
    setTheme,
    toggleMobileMenu,
    closeMobileMenu,
    setIsShowCookiesPop,
    setCurrentClient,

    // getters
    currentLocale,
    isLoading,
    currentTheme,
    keepAliveComponents,
    setKeepAlive,
  }
}, {
  persist: true,
})
