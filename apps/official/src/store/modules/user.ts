import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { user as userApi } from '@/services/api'
import { handleKeepAlive, setRouter } from '@/utils/router'

export const useUserStore = defineStore('user', () => {
  const token = ref<string | null>(null)
  const refresh_token = ref<string | null>(null)
  const user = ref<User.Info | null>(null)
  const rememberMe = ref(false)
  const userMenu = ref<Menu.Item[]>([])
  const isNeed2FA = ref(true)

  const setToken = (newToken: string) => {
    token.value = newToken
  }

  // actions
  const login = async (email: string, password: string, google_token: string, remember: boolean) => {
    try {
      const { data, code } = await userApi.login({ email, password, rememberMe: remember, google_token })

      if (code === 1) {
        throw new Error('Invalid credentials')
      }

      const { access_token, refresh_token: r_token } = data

      token.value = access_token
      refresh_token.value = r_token
      rememberMe.value = remember
      await getUserInfo()
      await getMenus()
    }
    catch {
      throw new Error('Invalid credentials')
    }
  }

  const register = async (email: string, password: string) => {
    try {
      await userApi.register({ email, password })
    }
    catch {
      throw new Error('Registration failed')
    }
  }

  const forgotPassword = async (email: string) => {
    try {
      await userApi.forgotPassword({ email })
    }
    catch {
      throw new Error('Failed to send password reset email')
    }
  }

  const logout = async () => {
    try {
      await userApi.logout()
    }
    finally {
      setToken('')
      user.value = null
      rememberMe.value = false
      location.reload()
    }
  }

  const initializeFromStorage = async () => {
    await getUserInfo()
    getMenus()
  }

  // 转换 menu 菜单
  const transformMenu = (menu: Api.RouterItem): Menu.Item => {
    return {
      path: menu.path,
      name: menu.name,
      redirect: menu.redirect as string || '',
      children: menu.children?.map(transformMenu) || [],
      meta: {
        isSeparator: menu?.isSeparator,
        breadcrumbTitle: menu?.breadcrumbTitle,
        isHideBreadcrumb: menu?.isHideBreadcrumb,
        keepAlive: menu?.isKeepAlive,
        i18nKey: menu.i18nKey,
        icon: menu.icon,
      },
    }
  }

  // 获取菜单
  const getMenus = () => {
    return new Promise<boolean>((resolve) => {
      const keeps: string[] = []
      // 模拟菜单数据
      const routes: Api.RouterItem[] = [
        {
          i18nKey: 'menu.home',
          path: '/merchant/home',
          name: 'home',
          component: 'views/merchant/home/<USER>',
          icon: 'merchant/menu-icons/home.png',
          isHide: false,
          breadcrumbTitle: 'Your Dashboard',
        },
        {
          i18nKey: 'menu.customers',
          path: '/merchant/customers',
          name: 'customers',
          icon: 'merchant/menu-icons/customer-new.png',
          isHide: false,
          redirect: {
            name: 'customersList',
          },
          children: [{
            path: 'list',
            i18nKey: 'menu.customersList',
            icon: 'pi pi-list',
            name: 'customersList',
            component: 'views/merchant/customers/list.vue',
            isHide: false,
            isKeepAlive: true,
            isHideBreadcrumb: true,
          }, {
            path: 'detail/:id',
            i18nKey: 'menu.customersDetail',
            icon: 'pi pi-info',
            name: 'customersDetail',
            component: 'views/merchant/customers/detail.vue',
            isHide: true,
            breadcrumbTitle: 'Details',
            children: [
              {
                path: 'createInvoice',
                i18nKey: 'menu.customersCreateInvoice',
                icon: 'pi pi-plus',
                name: 'customersCreateInvoice',
                component: 'views/merchant/customers/createInvoice.vue',
              },
            ],
          }, {
            path: 'createInvite',
            i18nKey: 'menu.customersDetail',
            icon: 'pi pi-envelope',
            name: 'customersCreateInvite',
            component: 'views/merchant/customers/createInvite.vue',
            isHide: true,
            breadcrumbTitle: 'Create Invite',
          }, {
            path: 'invite/:id',
            i18nKey: 'menu.customersDetail',
            icon: 'pi pi-envelope',
            name: 'customersEditInvite',
            component: 'views/merchant/customers/invite.vue',
            isHide: true,
            isHideBreadcrumb: true,
          }],
        },
        {
          i18nKey: 'menu.planSubscription',
          path: '/merchant/planSubscription',
          name: 'planSubscription',
          icon: 'merchant/menu-icons/plans-and-subscriptions.png',
          isHide: false,
          redirect: {
            name: 'planSubscriptionList',
          },
          children: [
            {
              path: 'add',
              i18nKey: 'menu.planSubscriptionAdd',
              icon: 'pi pi-plus',
              name: 'planSubscriptionAdd',
              component: 'views/merchant/planSubscription/add.vue',
              isHide: true,
            },
            {
              path: 'edit/:id',
              i18nKey: 'menu.planSubscriptionEdit',
              icon: 'pi pi-pencil',
              name: 'planSubscriptionEdit',
              component: 'views/merchant/planSubscription/edit.vue',
              isHide: true,
            },
            {
              path: 'details/:id',
              i18nKey: 'menu.planSubscriptionDetails',
              icon: 'pi pi-info-circle',
              name: 'planSubscriptionDetails',
              component: 'views/merchant/planSubscription/details.vue',
              isHide: true,
            },
            {
              path: 'list',
              i18nKey: 'menu.planSubscriptionList',
              icon: 'pi pi-list',
              name: 'planSubscriptionList',
              component: 'views/merchant/planSubscription/list.vue',
              isHide: false,
              isKeepAlive: true,
              isHideBreadcrumb: true,
            },
          ],
        },
        {
          i18nKey: 'menu.transactions',
          path: '/merchant/transactions',
          name: 'transactions',
          icon: 'merchant/menu-icons/transactions.png',
          isHide: false,
          redirect: {
            name: 'transactionsList',
          },
          children: [
            {
              path: 'list',
              i18nKey: 'menu.transactions',
              icon: 'pi pi-list',
              name: 'transactionsList',
              component: 'views/merchant/transactions/list.vue',
              isHide: false,
              isKeepAlive: true,
              isHideBreadcrumb: true,
            },
            {
              path: 'detail/:id',
              i18nKey: 'menu.transactionsDetail',
              icon: 'pi pi-info',
              name: 'transactionsDetail',
              component: 'views/merchant/transactions/details.vue',
              isHide: true,
              breadcrumbTitle: 'Details',
            },
          ],
        },
        {
          i18nKey: 'menu.payout',
          path: '/merchant/payout',
          name: 'payout',
          icon: 'merchant/menu-icons/payout.png',
          isHide: false,
          redirect: {
            name: 'payoutList',
          },
          children: [
            {
              path: 'list',
              i18nKey: 'menu.payoutList',
              icon: 'pi pi-list',
              name: 'payoutList',
              component: 'views/merchant/payout/list.vue',
              isHide: false,
              isKeepAlive: true,
              isHideBreadcrumb: true,
              breadcrumbTitle: 'Payout List',
            },
          ],
        },
        {
          i18nKey: 'menu.user',
          path: '/merchant/user',
          name: 'user',
          icon: 'merchant/menu-icons/user.png',
          isHide: false,
          redirect: {
            name: 'userList',
          },
          children: [
            {
              path: 'list',
              i18nKey: 'menu.userList',
              icon: 'pi pi-list',
              name: 'userList',
              component: 'views/merchant/user/list.vue',
              isHide: false,
              isKeepAlive: true,
              isHideBreadcrumb: true,
            },
            {
              path: 'detail/:id',
              i18nKey: 'menu.userDetail',
              icon: 'pi pi-info',
              name: 'userDetail',
              component: 'views/merchant/user/details.vue',
              isHide: true,
            },
            {
              path: 'create',
              i18nKey: 'menu.userCreate',
              icon: 'pi pi-plus',
              name: 'userCreate',
              component: 'views/merchant/user/create.vue',
              isHide: true,
            },
            {
              path: 'edit/:id',
              i18nKey: 'menu.userEdit',
              icon: 'pi pi-pencil',
              name: 'userEdit',
              component: 'views/merchant/user/edit.vue',
              isHide: true,
            },
            {
              path: 'roles',
              i18nKey: 'menu.userRole',
              icon: 'pi pi-users',
              name: 'userRoleList',
              component: 'views/merchant/user/roleList.vue',
              isHide: true,
              isKeepAlive: true,
            },
            {
              path: 'roles/create',
              i18nKey: 'menu.userRoleCreate',
              icon: 'pi pi-plus',
              name: 'userRoleCreate',
              component: 'views/merchant/user/roleCreate.vue',
              isHide: true,
            },
            {
              path: 'roles/edit/:id',
              i18nKey: 'menu.userRoleEdit',
              icon: 'pi pi-pencil',
              name: 'userRoleEdit',
              component: 'views/merchant/user/roleEdit.vue',
              isHide: true,
            },
          ],
        },
        {
          i18nKey: 'menu.balance',
          path: '/merchant/balance',
          name: 'balance',
          component: 'views/merchant/balance/index.vue',
          icon: 'merchant/menu-icons/balance.png',
          isHide: true,
        },
        {
          i18nKey: 'menu.report',
          path: '/merchant/report',
          name: 'report',
          component: 'views/merchant/report/index.vue',
          icon: 'merchant/menu-icons/report.png',
          isHide: false,
        },
        {
          i18nKey: 'menu.report',
          path: '/merchant/reportDetail',
          name: 'reportDetail',
          component: 'views/merchant/report/detail.vue',
          icon: 'merchant/menu-icons/report.png',
          isHide: true,
        },
        {
          i18nKey: 'menu.accounting',
          path: '/merchant/accounting',
          name: 'accounting',
          component: 'views/merchant/accounting/index.vue',
          icon: 'merchant/menu-icons/accounting.png',
        },
        {
          i18nKey: 'menu.payMyInvoice',
          path: '/merchant/payMyInvoice',
          name: 'payMyInvoice',
          icon: 'pi pi-check',
          component: 'views/merchant/payMyInvoice/dashboard.vue',
          isHide: !user.value?.xero_link,
          children: [
            {
              i18nKey: 'menu.payMyInvoiceCreateInvoice',
              path: '/merchant/payMyInvoice/createInvoice',
              name: 'payMyInvoiceCreateInvoice',
              icon: '',
              component: 'views/merchant/payMyInvoice/createInvoice.vue',
              isHide: false,
              isKeepAlive: true,
              isHideBreadcrumb: false,
            },
            {
              i18nKey: 'menu.payMyInvoiceList',
              path: '/merchant/payMyInvoice/list',
              name: 'payMyInvoiceInvoiceList',
              icon: '',
              component: 'views/merchant/payMyInvoice/payMyInvoiceList.vue',
              isHide: false,
              isKeepAlive: true,
              isHideBreadcrumb: false,
            },
            {
              i18nKey: 'menu.payMyInvoiceDetail',
              path: '/merchant/payMyInvoice/detail/:id',
              name: 'payMyInvoiceInvoiceDetail',
              icon: '',
              component: 'views/merchant/payMyInvoice/payMyInvoiceDetail.vue',
              isHide: true,
              isHideBreadcrumb: true,
            },
            {
              i18nKey: 'menu.payMyInvoiceDetailAndSend',
              path: '/merchant/payMyInvoice/detailAndSend/:id',
              name: 'payMyInvoiceInvoiceDetailAndSend',
              icon: '',
              component: 'views/merchant/payMyInvoice/payMyInvoiceDetailAndSend.vue',
              isHide: true,
              isHideBreadcrumb: false,
              breadcrumbTitle: 'Invoice Detail Send',
            },
            {
              i18nKey: 'menu.payMyInvoiceXeroConfig',
              path: '/merchant/payMyInvoice/xeroConfig',
              name: 'payMyInvoiceXeroConfig',
              icon: '',
              component: 'views/merchant/payMyInvoice/xeroConfig.vue',
              isHide: false,
              isHideBreadcrumb: false,
              breadcrumbTitle: 'Xero Config',
            },
          ],
        },
        {
          i18nKey: 'menu.integrations',
          path: '/merchant/integrations',
          name: 'integrations',
          component: 'views/merchant/integrations/index.vue',
          icon: 'pi pi-cog',
        },
        // {
        //   i18nKey: 'menu.contact',
        //   path: '/merchant/contact',
        //   name: 'contact',
        //   component: 'views/merchant/contact/index.vue',
        //   icon: 'pi pi-users',
        // },
        // {
        //   i18nKey: 'menu.account',
        //   path: '/merchant/account',
        //   name: 'account',
        //   component: 'views/merchant/account/index.vue',
        //   icon: 'merchant/menu-icons/accounting.png',
        // },
        // {
        //   i18nKey: 'menu.invoiceList',
        //   path: '/merchant/invoiceList',
        //   name: 'invoiceList',
        //   component: 'views/merchant/invoice/list.vue',
        //   icon: 'pi pi-file-pdf',
        // },
        // {
        //   i18nKey: 'menu.council',
        //   path: '/merchant/council',
        //   name: 'council',
        //   component: 'views/merchant/council/index.vue',
        //   icon: 'merchant/menu-icons/council.png',
        //   isHide: true,
        // },
        {
          i18nKey: 'menu.downloadCenter',
          path: '/merchant/downloadCenter',
          name: 'downloadCenter',
          component: 'views/merchant/downloadCenter/list.vue',
          icon: 'merchant/menu-icons/download.png',
        },
        {
          i18nKey: 'menu.profile',
          path: '/merchant/user/profile',
          name: 'profile',
          component: 'views/merchant/user/profile.vue',
          icon: 'merchant/menu-icons/profile.png',
          isHide: true,
        },
        {
          i18nKey: 'menu.settings',
          path: '/merchant/user/settings',
          name: 'settings',
          component: 'views/merchant/user/settings.vue',
          icon: 'merchant/menu-icons/settings.png',
          isHide: true,
        },
        {
          i18nKey: '',
          path: '',
          name: '',
          component: '',
          icon: 'pi pi-cog',
          isSeparator: true,
        },
        {
          i18nKey: 'menu.support',
          path: '/support',
          name: 'support',
          component: '',
          icon: 'merchant/menu-icons/question.png',
        },
      ] as Api.RouterItem[]

      // 递归过滤和转换菜单
      const filterAndTransformMenu = (items: Api.RouterItem[]): Menu.Item[] => {
        return items
          .filter((item) => {
            if (item?.isKeepAlive) {
              keeps.push(item.name)
            }
            return !item.isHide
          })
          .map((item) => {
            const menuItem = transformMenu(item)
            if (item.children && item.children.length > 0) {
              menuItem.children = filterAndTransformMenu(item.children)
            }
            return menuItem
          })
      }

      // 设置路由
      setRouter(routes)
      // 设置菜单
      userMenu.value = filterAndTransformMenu(routes)
      // 设置 keepAlive 名称
      handleKeepAlive(keeps)
      resolve(true)
    })
  }

  const getUserInfo = async (): Promise<User.Info> => {
    const data = await userApi.getUserInfo()
    isNeed2FA.value = data.code === 403
    user.value = data.data
    return user.value
  }

  const updateUserInfo = async (updateData: User.UserInfoUpdateReq) => {
    const { data, code } = await userApi.updateUserInfo(updateData)
    if (code === 0) {
      user.value = data
    }
    return user.value
  }

  // getters
  const isLoggedIn = computed(() => !!token.value)
  const currentUsername = computed(() => user.value?.name)

  return {
    // state
    token,
    user,
    rememberMe,
    userMenu,
    isNeed2FA,
    setToken,

    // actions
    login,
    register,
    forgotPassword,
    logout,
    initializeFromStorage,
    getMenus,
    getUserInfo,
    updateUserInfo,

    // getters
    isLoggedIn,
    currentUsername,
  }
}, {
  persist: {
    omit: ['userMenu', 'isLogin', 'user', 'isNeed2FA'],
  },
})
