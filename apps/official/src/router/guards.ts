import type { NavigationGuardNext, RouteLocationNormalized, Router } from 'vue-router'
import nProgress from 'nprogress'

/**
 * 身份验证导航守卫，处理用户认证和初始化
 */
export const authGuard = async (
  _to: RouteLocationNormalized,
  _from: RouteLocationNormalized,
  next: NavigationGuardNext,
) => {
  nProgress.start()

  next()
}

/**
 * 路由完成后的处理
 */
export const afterEachGuard = () => {
  nProgress.done()
}

/**
 * 路由错误处理
 */
export const errorGuard = () => {
  nProgress.remove()
}

export const routerGuardsSetup = (router: Router) => {
  router.beforeEach(authGuard)
  router.afterEach(afterEachGuard)
  router.onError(errorGuard)
}
