export const officialRoutes = [
  {
    path: '',
    name: 'officialHome',
    component: () => import('@/views/home/<USER>'),
  },
  {
    path: 'about-us',
    name: 'officialAbout',
    component: () => import('@/views/aboutUs/index.vue'),
  },
  {
    path: 'Contact-Us',
    name: 'officialContact',
    component: () => import('@/views/contact/index.vue'),
  },
  {
    path: 'pricing',
    name: 'officialPricing',
    component: () => import('@/views/pricing/index.vue'),
  },
  {
    path: 'features',
    name: 'officialFeatures',
    component: () => import('@/views/features/index.vue'),
  },
  {
    path: 'products',
    name: 'officialProducts',
    component: () => import('@/views/products/index.vue'),
  },
  {
    path: 'DirectDebit',
    name: 'officialProductsDirectDebit',
    component: () => import('@/views/products/components/debit.vue'),
  },
  {
    path: 'BPay',
    name: 'officialProductsBPay',
    component: () => import('@/views/products/components/bPay.vue'),
  },
  {
    path: 'WebPay',
    name: 'officialProductsWebPay',
    component: () => import('@/views/products/components/webPay.vue'),
  },
  {
    path: 'PayMyInvoice',
    name: 'officialProductsPayMyInvoice',
    component: () => import('@/views/products/components/payMyInvoice.vue'),
  },
  {
    path: 'support',
    name: 'officialSupport',
    component: () => import('@/views/support/index.vue'),
  },
  {
    path: 'biller-registration',
    name: 'officialBillerRegistration',
    component: () => import('@/views/billerRegistration/index.vue'),
  },
  {
    path: 'terms',
    name: 'officialTerms',
    component: () => import('@/views/terms/index.vue'),
  },
  {
    path: 'privacy',
    name: 'officialPrivacy',
    component: () => import('@/views/privacy/index.vue'),
  },
  {
    path: 'payment',
    name: 'officialPayment',
    component: () => import('@/views/support/components/payment.vue'),
  },
  {
    path: 'termsandconditions',
    name: 'officialTermsAndConditions',
    component: () => import('@/views/termsAndConditions/index.vue'),
  },
  {
    path: 'merchantagreement',
    name: 'officialMerchantAgreement',
    component: () => import('@/views/merchantAgreement/index.vue'),
  },
]
