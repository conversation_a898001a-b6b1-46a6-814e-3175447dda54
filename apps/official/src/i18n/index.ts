import { createI18n } from 'vue-i18n'
import en from './locales/en.json'
import customersPage from './locales/en/customersPage.json'
import home from './locales/en/home.json'
// import zh from './locales/zh.json'

const savedLocale = localStorage.getItem('locale') || import.meta.env.VITE_APP_I18N_LOCALE
export const i18n = createI18n({
  legacy: false,
  locale: savedLocale,
  messages: {
    en: {
      ...en,
      customersPage,
      home,
    },
  },
  globalInjection: true,
})

export default i18n
