# 商户账单管理系统

[English](README.md)

一个基于 Vue 3 + TypeScript + Vite 的现代化商户账单管理系统。

## 功能特点

- 📊 商户账单管理
- 💰 收支明细统计
- 📈 数据可视化展示
- 🌍 国际化支持
- 🎨 主题定制
- 📱 响应式设计

## 技术栈

- Vue 3 - 渐进式 JavaScript 框架
- TypeScript - JavaScript 的类型增强
- Vite - 下一代前端构建工具
- Pinia - Vue 的状态管理库
- Vue Router - Vue.js 的官方路由
- PrimeVue - UI 组件库
- Vue I18n - 国际化插件
- Vitest - 单元测试框架
- Day.js - 时间处理库
- Decimal.js - 精确十进制运算库

## 环境要求

```bash
Node.js >= 18.20.0
npm >= 9.8.1
Git

# 推荐的开发工具
Visual Studio Code

# 推荐的 VSCode 插件
- Vue Language Features (Volar)
- TypeScript Vue Plugin (Volar)
- ESLint
- Prettier
```

## 安装和设置

### 安装依赖

```bash
# 进入项目目录
cd bill-merchant

# 使用 npm 安装依赖
npm install

# 或使用 pnpm
pnpm install
```

### 开发

```bash
# 启动开发服务器
npm run dev
# 或
pnpm dev

# 默认开发服务器地址
http://localhost:3005/
```

### 构建和预览

```bash
# 构建生产环境
npm run build
# 或
pnpm build

# 预览生产构建
npm run preview
# 或
pnpm preview
```

### 代码检查和格式化

```bash
# 运行代码检查
npm run lint
# 或
pnpm lint

# 修复代码格式
npm run format
# 或
pnpm format
```

### 测试

```bash
# 运行单元测试
npm run test
# 或
pnpm test

# 运行测试覆盖率报告
npm run test:coverage
# 或
pnpm test:coverage
```

## 项目结构

```
bill-merchant/
├── src/                    # 源代码
│   ├── components/         # 通用组件
│   ├── composables/        # 组合式函数
│   ├── i18n/              # 国际化配置
│   │   └── locales/       # 语言文件
│   ├── views/             # 页面视图
│   ├── router/            # 路由配置
│   ├── store/             # Pinia 状态管理
│   ├── assets/            # 静态资源
│   ├── utils/             # 工具函数
│   ├── services/          # API 服务
│   ├── layout/            # 布局组件
│   ├── types/             # TypeScript 类型定义
│   ├── App.vue            # 根组件
│   └── main.ts            # 入口文件
├── public/                # 公共资源
├── dist/                  # 构建输出
├── tests/                 # 测试文件
├── node_modules/          # 依赖包
├── .vscode/              # VSCode 配置
├── .env                  # 环境变量
├── .env.development      # 开发环境变量
├── .env.production       # 生产环境变量
├── .eslintrc.js         # ESLint 配置
├── .prettierrc          # Prettier 配置
├── tsconfig.json        # TypeScript 配置
├── vite.config.ts       # Vite 配置
├── vitest.config.ts     # Vitest 配置
├── package.json         # 项目配置文件
└── README.md            # 项目文档
```

## 环境变量

```bash
# .env.development
VITE_API_BASE_URL=http://localhost:3005
VITE_APP_TITLE=Bill Merchant System (Dev)

# .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_APP_TITLE=Bill Merchant System
```

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88
