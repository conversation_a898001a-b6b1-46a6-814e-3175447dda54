# Bill Merchant System

[中文文档](README-zh_CN.md)

A modern merchant billing management system based on Vue 3 + TypeScript + Vite.

## Features

- 📊 Merchant Bill Management
- 💰 Income and Expense Statistics
- 📈 Data Visualization
- 🌍 Internationalization
- 🎨 Theme Customization
- 📱 Responsive Design

## Tech Stack

- Vue 3 - Progressive JavaScript Framework
- TypeScript - JavaScript with syntax for types
- Vite - Next Generation Frontend Tooling
- Pinia - Vue Store Management
- Vue Router - Official Router for Vue.js
- PrimeVue - UI Component Library
- Vue I18n - Internationalization Plugin
- Vitest - Unit Testing Framework
- Day.js - Time Processing Library
- Decimal.js - Decimal Computation Library

## Prerequisites

```bash
Node.js >= 18.20.0
npm >= 9.8.1
Git

# Recommended IDE
Visual Studio Code

# Recommended VSCode Extensions
- Vue Language Features (Volar)
- TypeScript Vue Plugin (Volar)
- ESLint
- Prettier
```

## Installation & Setup

### Install Dependencies

```bash
# Enter project directory
cd bill-merchant

# Install dependencies with npm
npm install

# Or use pnpm
pnpm install
```

### Development

```bash
# Start development server
npm run dev
# Or
pnpm dev

# Default development server address
http://localhost:5173
```

### Build & Preview

```bash
# Build for production
npm run build
# Or
pnpm build

# Preview production build
npm run preview
# Or
pnpm preview
```

### Lint & Format

```bash
# Run code linting
npm run lint
# Or
pnpm lint

# Fix code formatting
npm run format
# Or
pnpm format
```

### Test

```bash
# Run unit tests
npm run test
# Or
pnpm test

# Run test coverage report
npm run test:coverage
# Or
pnpm test:coverage
```

## Project Structure

```
bill-merchant/
├── src/                    # Source code
│   ├── components/         # Common components
│   ├── composables/        # Composable functions
│   ├── i18n/              # Internationalization
│   │   └── locales/       # Language files
│   ├── views/             # Page views
│   ├── router/            # Router configuration
│   ├── store/             # Pinia store
│   ├── assets/            # Static assets
│   ├── utils/             # Utility functions
│   ├── services/          # API services
│   ├── layout/            # Layout components
│   ├── types/             # TypeScript types
│   ├── App.vue            # Root component
│   └── main.ts            # Entry point
├── public/                # Public assets
├── dist/                  # Build output
├── tests/                 # Test files
├── node_modules/          # Dependencies
├── .vscode/              # VSCode configuration
├── .env                  # Environment variables
├── .env.development      # Development env vars
├── .env.production       # Production env vars
├── .eslintrc.js         # ESLint config
├── .prettierrc          # Prettier config
├── tsconfig.json        # TypeScript config
├── vite.config.ts       # Vite config
├── vitest.config.ts     # Vitest config
├── package.json         # Project manifest
└── README.md            # Project documentation
```

## Environment Variables

```bash
# .env.development
VITE_API_BASE_URL=http://localhost:3005
VITE_APP_TITLE=Bill Merchant System (Dev)

# .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_APP_TITLE=Bill Merchant System
```
