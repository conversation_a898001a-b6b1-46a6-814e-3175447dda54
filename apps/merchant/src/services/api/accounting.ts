import { GET, POST } from '../http'

export const create = (data: Api.CreateAccountingReq) => POST('/accounting/create', data)

export const createConfig = (data: Api.CreateAccountingConfigReq) => POST<Api.CreateAccountingConfigRes>('/accounting/channel', data)

export const getList = (params: Api.AccountingListReq) => GET<Api.AccountingListRes>('/accounting/list', { params })

export const getDetail = (id: string) => GET<Api.AccountingDetailRes>(`/accounting/detail`, { params: { id } })

export const remove = (invoice_id: string) => POST<CommonRes>('/accounting/delete', { invoice_id })

export const update = (data: Api.UpdateAccountingReq) => POST<CommonRes>('/accounting/update', data)

export const getInvoiceNumberDetail = (params: Api.AccountingNumberDetailReq) => GET<Api.AccountingNumberDetailRes>(`/accounting/getData`, { params })

export const pay = (data: Api.AccountingNumberPayReq) => POST<CommonRes>('/accounting/submit', data)
