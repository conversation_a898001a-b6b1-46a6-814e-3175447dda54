import type { AxiosRequestConfig } from 'axios'
import { GET, POST } from '@/services/http'

export const getList = (params: Api.PlanListReq, options?: AxiosRequestConfig) => GET<CommonRes<Plan.Info[]>>('/plan/list', { params, ...options })
export const create = (data: Api.PlanCreateReq, options?: AxiosRequestConfig) => POST<CommonRes<Plan.Info>>('/plan/create', data, options)
export const remove = (plan_ids: string[]) => POST<CommonRes>('/plan/delete', { plan_ids })
export const getDetail = (plan_id: string) => GET<Plan.Info>('/plan/detail', { params: { plan_id } })
export const edit = (data: Api.PlanEditReq) => POST<CommonRes>('/plan/update', data)
export const exportPlans = (params: Api.PlanListReq) => GET<CommonRes>('/plan/export', { params })
export const createByCustomer = (data: Api.PlanCreateByCustomerReq, options?: AxiosRequestConfig) => POST<{ customer_id: string }>('/subscription/create', data, options)
export const updatePayMethod = (data: Api.PlanUpdatePayMethodReq) => POST<CommonRes>('/subscription/sendModPaymentMethodMail', data)
export const updateCustomerPlanInfo = (data: Api.UpdateCustomerPlanInfoReq, options?: AxiosRequestConfig) => POST<CommonRes>('/subscription/update', data, options)
