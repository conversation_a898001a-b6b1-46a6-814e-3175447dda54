import { GET, POST } from '@/services/http'

export const getList = (params: Api.DownloadCenterListReq) => GET<Api.DownloadCenterListRes>('/downloadCenter/list', { params })

export const remove = (file_id: string) => POST<CommonRes>('/downloadCenter/delete', { file_id })

export const upload = (data: FormData) => POST<{ file_path: string[] }>('/downloadCenter/upload', data, {
  headers: {
    'Content-Type': 'multipart/form-data',
  },
})
