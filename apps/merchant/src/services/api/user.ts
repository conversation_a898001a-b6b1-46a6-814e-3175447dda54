import { GET, POST } from '@/services/http'

export const login = (data: Api.UserLoginReq) => POST<Api.UserLoginRes>('/userLogin', data)
export const register = (data: Api.UserRegisterReq) => POST<Api.UserRegisterRes>('/register', data)
export const forgotPassword = (data: Api.UserForgotPasswordReq) => POST<Api.UserForgotPasswordRes>('/forgetPassword', data)

export const setNewPassword = (data: Api.UserSetNewPasswordRequest) => POST<CommonRes>('/forgetPasswordSubmit', data)
export const getUserInfo = () => GET<User.Info>('/getUserInfo')
export const logout = () => GET('/userLoginOut')

export const getMerchantConfig = () => GET<Api.MerchantConfigRes>('/getMerchantConfig')

export const getMenu = () => GET<Api.RouterItem[]>('/getMenu')

// 2FA Authentication APIs
export const get2FASetupInfo = () => GET<Api.UserTwoFactorSetupRes>('/getValidatePage')
export const verify2FACode = (data: Api.UserTwoFactorVerifyReq) => POST<Api.UserTwoFactorVerifyRes>('/validateMfa', data)

// Get user list with pagination and filtering
export const getUserList = (params: Api.UserListReq) => GET<Api.UserListRes>('/user/list', { params })

// Get user detail by ID
export const getUserDetail = (id: string) => GET<User.UserInfo>(`/user/detail`, { params: { user_id: id } })

export const editUpdateUser = (id: string, data: User.UserUpdateReq) => POST<User.UserInfo>(`user/update`, { ...data, user_id: id })

// Create new user
export const createUser = (data: User.UserCreateReq) => POST<User.UserInfo>('/user/create', data)

// Update user
export const updateUser = (id: string, data: User.UserUpdateReq) => POST<User.UserInfo>(`/user/update/${id}`, data)

// Delete user
export const removeUser = (id: string) => POST<void>(`/user/delete`, { user_id: id })

// Update user
export const updateUserInfo = (data: User.UserInfoUpdateReq) => POST<User.UserInfo>(`/userUpdate`, data)

// Update User Info Password
export const updateUserPassword = (data: User.UserPasswordUpdateReq) => POST<User.UserInfo>(`/userUpdatePassword`, data)

export const getGroupList = () => GET<User.GroupList>('/getBusinessList')

// Bid APIs
export const getBidList = (params: Api.BidListReq) => GET<User.GroupList>('/business/list', { params })
export const createBid = (data: Api.BidCreateReq) => POST<User.BidCreateRes>('/business/create', data)
export const editBid = (id: string, data: Api.BidUpdateReq) => POST<User.BidUpdateRes>(`/updateBusiness/${id}`, data)
export const deleteBid = (id: string) => POST<void>(`/deleteBusiness/${id}`)
export const getBidDetail = (id: string) => GET<User.BidDetailRes>(`/business/detail`, {
  params: {
    id,
  },
})

// Business APIs
export const getBusinessList = (params?: Record<string, any>) => GET<User.BusinessListRes>('/business/list', { params })
export const getBusinessDetail = (id: string) => GET<User.BusinessDetailRes>('/business/detail', { params: { id } })

// Bid Account APIs
export const getBidAccountList = (params: Api.BidAccountListReq) => GET<User.BidAccountListRes>('/businessAccount/list', { params })
export const createBidAccount = (data: Api.BidAccountCreateReq) => POST<Api.BidAccountDetailRes>('/businessAccount/create', data)
export const updateBidAccount = (data: Api.BidAccountUpdateReq) => POST<void>('/businessAccount/update', data)
export const deleteBidAccount = (data: Api.BidAccountDeleteReq) => POST<void>('/businessAccount/delete', data)
export const getBidAccountDetail = (id: string) => GET<Api.BidAccountDetailRes>('/businessAccount/detail', { params: { id } })
export const updateBidAccountStatus = (data: Api.BidAccountStatusUpdateReq) => POST<void>('/businessAccount/statusUpdate', data)
