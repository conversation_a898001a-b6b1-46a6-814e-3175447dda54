declare namespace Api {

  interface FlexiratesGetPropertyListRes extends CommonListRes {
    data: FlexiratesGetPropertyListInfo[]
  }
  interface PropertyInfo {
    active_register_flag: string
    active_registration_id: string
    allow_quaterly: string
    allow_register: string
    assessment_number: string
    end_date: string
    full_amount: string
    instalment_1_amount: string
    instalment_1_due: string
    instalment_2_due: string
    instalment_2_mount: string
    instalment_3_amount: string
    instalment_3_due: string
    instalment_4_amount: string
    instalment_4_due: string
    message: string
    postcode: string
    property_address: string
    property_suburb: string
    scheduled_payments_flag: string
    start_date: string
  }

  interface FlexiratesGetPropertyListInfo {
    assessment_number?: string
    created_at?: null
    created_uid?: number
    deleted_at?: null
    deleted_uid?: null
    end_date?: string
    flexi_user_id?: number
    full_amount?: string
    id?: number
    post_code?: string
    property_address?: string
    property_info?: PropertyInfo
    start_date?: null
    updated_at?: string
    updated_uid?: number
  }
  interface FlexiratesCreatePropertyReq {
    property_number: string
    verification_code: string
  }
  interface FlexiratesDeletePropertyReq {
    property_ids: [number]
  }
  interface FlexiratesNotifyDeleteReq {
    /**
     * 多个用英文逗号隔开，is_all为空时必填，ids不为空且is_all=1时按ids删除
     */
    ids?: string
    /**
     * 是否删除全部，0、否 1、是，ids为空时必填
     */
    is_all?: number
  }
}
