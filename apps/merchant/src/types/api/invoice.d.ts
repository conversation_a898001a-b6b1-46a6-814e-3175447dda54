declare namespace Api {
  interface InvoiceListReq {
    /**
     * 金额
     */
    'amount'?: string
    /**
     * 创建时间[start, end]
     */
    'created_at[]'?: string[]
    /**
     * 支付方式
     */
    'credit_brand'?: string
    /**
     * 币种
     */
    'currency'?: string
    /**
     * 发票编号
     */
    'invoice_number'?: string
    /**
     * 服务类型
     */
    'service_type'?: string
    /**
     * 页数
     */
    'page'?: number
    /**
     * 页大小
     */
    'page_size'?: number
    /**
     * 状态
     */
    'status'?: number | null
    'sort_by'?: string
    'sort_order'?: 'asc' | 'desc'
  }

  interface InvoiceDetailReq {
    id: number
  }

  interface InvoiceDetailSendInvoiceEmailReq {
    id: number
  }
}
