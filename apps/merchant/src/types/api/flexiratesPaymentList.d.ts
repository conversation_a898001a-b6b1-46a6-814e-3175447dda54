declare namespace Api {

  interface FlexiratesGetCardListReq extends CommonSearchListParams {
    customer_id: string
  }

  interface FlexiratesGetCardListRes extends CommonListRes {
    data: FlexiratesCard.Info[]
  }

  interface FlexiratesGetBankAccountListReq extends CommonSearchListParams {
    customer_id: string
  }

  interface FlexiratesXeroSubmitPaymentReq {
    redirect_uri: string
  }

  interface FlexiratesXeroSubmitPaymentRes {
    url: string
  }
}
