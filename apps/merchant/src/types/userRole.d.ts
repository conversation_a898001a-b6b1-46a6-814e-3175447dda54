declare namespace UserRole {
  interface Info {
    id?: number
    merchant_id: string
    name: string
    slug: string
    permissions?: PermissionListRes[]
    created_at: string
    updated_at: string | null
  }

  interface CreateReq {
    role_name: string
    role_mark: string
  }

  interface UpdateReq extends Partial<CreateReq> {
    role_id?: string
  }

  interface PermissionListRes {
    id: number
    name: string
    slug: string
    http_method: string | null
    http_path: string[]
    order: number
    parent_id: number
    created_at: string
    updated_at: string
    children: UserRole.PermissionListRes[]
  }
}
