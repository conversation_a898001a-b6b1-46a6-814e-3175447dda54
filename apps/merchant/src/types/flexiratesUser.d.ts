declare namespace FlexiratesUser {
  interface Info {
    id: number
    name: string
    email?: string
    avatar?: string
    merchant_id?: string
    mfa_check: number
  }

  interface UserInfoUpdateReq {
    /**
     * MFA开关，(0:关闭,1:开启)
     */
    mfa_check: number
    /**
     * 头像
     */
    avatar: string
    /**
     * 邮箱
     */
    email: string
    /**
     * 账户名称
     */
    user_name: string
  }
}
