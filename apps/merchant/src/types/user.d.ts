declare namespace User {
  interface Info {
    id: number
    name: string
    email?: string
    avatar?: string
    merchant_id?: string
    mfa_check: number
    status: number
    type: number
    xero_link: boolean
  }

  interface BidInfo {
    id: number
    name: string
    description: string
    created_at: string
    updated_at: string
    status: number
  }

  interface Profile {
    id?: number
    fullName: string
    nickName: string
    gender: string
    avatar: string
    email: string
    emails: Email[]
  }

  interface UserInfo {
    mfa_check: number
    created_at: string
    email: string
    email_verified_at: string
    google2fa_code: string
    google2fa_secret: string
    id: number
    merchant_id: string
    name: string
    status: number
    type: number
    xero_link: boolean
    updated_at: string

    merchant_user_business?: {
      business_id: string
      business: {
        business_id: string
        business_name: string
      }
    }[]
  }

  interface UserCreateReq {
    /**
     * 是否开启MFA验证 ，0：关闭，1：开启
     */
    mfa_check?: number
    /**
     * 邮箱
     */
    email: string
    /**
     * 密码
     */
    password: string
    /**
     * 角色ID，例：1,2,3
     */
    roles?: string
    /**
     * 用户名
     */
    user_name: string
    /**
     * 业务ID，例：1,2,3
     */
    business_ids?: string[]
  }

  interface UserUpdateReq extends Partial<UserCreateReq> {}

  interface UserInfoUpdateReq {
    /**
     * MFA开关，(0:关闭,1:开启)
     */
    mfa_check: number
    /**
     * 头像
     */
    avatar: string
    /**
     * 邮箱
     */
    email: string
    /**
     * 账户名称
     */
    user_name: string
  }

  interface UserPasswordUpdateReq {
    password: string
    new_password: string
  }

  interface GroupList extends CommonListRes<BidInfo[]> {
    business_id_list?: Record<string, string>
  }

  /**
   * Response from creating a business/bid
   */
  interface BidCreateRes {
    id: number
    name: string
    description: string
    created_at: string
    updated_at: string
  }

  /**
   * Response from updating a business/bid
   */
  interface BidUpdateRes {
    id: number
    name: string
    description: string
    created_at: string
    updated_at: string
  }

  /**
   * Response from the business/bid detail endpoint
   */
  interface BidDetailRes {
    id: number
    name: string
    description: string
    created_at: string
    updated_at: string
  }

  /**
   * Bid account information
   */
  interface BidAccountInfo {
    id: number
    merchant_id: string
    business_id: string
    bsb: string
    account_no: string
    account_name: string
    status: number
    user_id: number
    reviewer_id: number
    admin_id: number
    created_at: string
    updated_at: string
    user: {
      id: number
      name: string
    }
    reviewer: {
      id: number
      name: string
    }
    admin: {
      id: number
      name: string
    }
  }

  /**
   * Response from bid account list endpoint
   */
  interface BidAccountListRes extends CommonListRes<BidAccountInfo> {}

  /**
   * Business information
   */
  interface BusinessInfo {
    merchant_id: string
    merchant_name: string
    business_id: string
    business_name: string
    settlement_type: number
    status: number
    bpay_switch: number
    created_at: string
    updated_at: string
  }

  /**
   * Response from business list endpoint
   */
  interface BusinessListRes extends CommonListRes<BusinessInfo[]> {}

  /**
   * Fee structure for business details
   */
  interface FeeItem {
    fee_rate: string
    fee_value: string
  }

  /**
   * Business detail response from /api/merchant/business/detail
   */
  interface BusinessDetailRes {
    merchant_id: string
    merchant_name: string
    business_id: string
    account_fees: {
      account_setup_fee: FeeItem[]
      account_termination_fee: FeeItem[]
      minimum_monthly_transaction_fee: FeeItem[]
    }
    integration_fees: {
      integration_setup_fee: FeeItem[]
      integration_customisation_and_development_fee: FeeItem[]
    }
    platform_fees: {
      user_licence_fee: FeeItem[]
      extended_helpdesk_fee: FeeItem[]
      hosting_maintenance_fee: FeeItem[]
    }
    chargeback_fees: {
      chargeback_fee: FeeItem[]
      no_document_fee: FeeItem[]
      delay_of_information_fee: FeeItem[]
      dispute_investigation_fee: FeeItem[]
    }
    administration_fees: {
      other_fee: FeeItem[]
      customer_report_fee: FeeItem[]
      direct_debit_setup_fee: FeeItem[]
      direct_debit_variation_fee: FeeItem[]
      bulk_file_processing_or_bath_fee: FeeItem[]
      manual_cancellation_or_refund_fee: FeeItem[]
    }
    communication_fees: {
      qr_code_fee: FeeItem[]
      sms_notifications_fee: FeeItem[]
      email_notifications_fee: FeeItem[]
    }
    settlement_into_bill_account_fees: {
      settlement_fee: FeeItem[]
    }
    bank_account_fees: {
      per_dishonour_fee: FeeItem[]
      per_transaction_fee: FeeItem[]
    }
    credit_card_fees: {
      eftpos_fee: FeeItem[]
      domestic_visa_fee: FeeItem[]
      international_visa_fee: FeeItem[]
      domestic_mastercard_fee: FeeItem[]
      per_transaction_premium_fee: FeeItem[]
      international_mastercard_fee: FeeItem[]
      per_transaction_standard_fee: FeeItem[]
    }
    credit_card_invoice_fees: {
      eftpos_fee: FeeItem[]
      domestic_visa_fee: FeeItem[]
      international_visa_fee: FeeItem[]
      domestic_mastercard_fee: FeeItem[]
      per_transaction_premium_fee: FeeItem[]
      international_mastercard_fee: FeeItem[]
      per_transaction_standard_fee: FeeItem[]
    }
    bpay_fees: {
      per_transaction_standard_fee: FeeItem[]
    }
    surcharge_fees: {
      surcharge_fee: FeeItem[]
    }
    gst: {
      gst: FeeItem[]
    }
    settlement_type: number
    status: number
    bpay_switch: number
    created_at: string
    updated_at: string
  }
}
