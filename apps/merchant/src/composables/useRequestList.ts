import type { HttpResponse } from '@/services/http'
import type { DataTablePageEvent } from 'primevue/datatable'
import type { Ref } from 'vue'
import dayjs from 'dayjs'
import { onMounted, reactive, ref } from 'vue'

// 实现防抖函数
function debounce<T extends (...args: any[]) => any>(fn: T, delay: number): (...args: Parameters<T>) => void {
  let timer: number | null = null
  return function (this: any, ...args: Parameters<T>) {
    if (timer) { window.clearTimeout(timer) }
    timer = window.setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

export interface RequestListParams {
  page?: number
  page_size?: number
  [key: string]: any
}

export interface UseRequestListOptions<T, P extends RequestListParams = RequestListParams> {
  // 请求方法
  requestFn: (params: P) => Promise<HttpResponse<CommonListRes<T>>>
  // 初始参数
  defaultParams?: Partial<P>
  // 是否自动请求
  immediate?: boolean
  // 手动传入的分页大小
  page_size?: number
  // 是否需要防抖
  debounce?: boolean
  // 防抖时间
  debounceTime?: number
}

export function useRequestList<T, P extends RequestListParams = RequestListParams>(
  options: UseRequestListOptions<T, P>,
) {
  const {
    requestFn,
    defaultParams = {},
    immediate = true,
    page_size = 50,
    debounce: needDebounce = true,
    debounceTime = 200,
  } = options

  // 列表数据
  const list = ref<T[]>([]) as Ref<T[]>
  // 加载状态
  const loading = ref(false)
  // 是否有错误
  const error = ref<Error | null>(null)
  // 搜索失败状态 - 区别于技术错误，表示搜索条件无效或API返回失败
  const failed = ref(false)
  // 失败消息
  const failureMessage = ref('')
  // 总数
  const total = ref(0)
  // 其他数据
  const other = ref<Record<string, any> | null>(null)
  // 当前页码
  const currentPage = ref(1)
  // 查询参数
  const queryParams = reactive<Partial<P>>({
    ...defaultParams,
  })

  const formatData = (data: Record<string, any>) => {
    const returnData: Record<string, any> = {}
    for (const key in data) {
      if (Array.isArray(data[key]) && data[key].length > 0) {
        returnData[key] = data[key]
      }
      else if (!Array.isArray(data[key]) && String(data[key]).length > 0) {
        returnData[key] = data[key]
      }
    }
    return returnData
  }

  // 获取列表数据
  const fetchList = async (params?: Partial<P>) => {
    try {
      loading.value = true
      error.value = null
      failed.value = false
      failureMessage.value = ''

      const finalParams = {
        page_size,
        page: currentPage.value,
        ...formatData(queryParams),
        ...params,
      } as P

      const res = await requestFn(finalParams)

      // 检查API响应状态
      if (res.code !== 0) {
        failed.value = true
        failureMessage.value = res.message || 'Failed'
        list.value = []
        total.value = 0
        return
      }

      const { data: listData = [], total: totalData = 0, ...otherData } = res.data
      list.value = listData as T[]
      total.value = totalData
      other.value = otherData
    }
    catch (err) {
      error.value = err as Error
      failed.value = true
      failureMessage.value = (err as Error).message || '搜索出错，请稍后重试'
      list.value = []
      total.value = 0
    }
    finally {
      loading.value = false
    }
  }

  // 防抖处理
  const debouncedFetchList = needDebounce
    ? debounce(fetchList, debounceTime)
    : fetchList

  // 只更新参数,不触发请求
  const setParams = (params: Partial<P>) => {
    Object.assign(queryParams, params)
    currentPage.value = 1
  }

  const setSearchParams = (data: Partial<P>, formatKey: Array<keyof T> = []) => {
    const formattedData = { ...data }
    // 处理特殊字段格式化
    for (const key in formattedData) {
      // 特殊处理日期字段
      if (key === 'created_at[]' || key === 'updated_at[]') {
        if (Array.isArray(formattedData[key]) && formattedData[key].length === 2) {
          const dateArray = formattedData[key]
          const startDate = dayjs(dateArray[0]).format('YYYY-MM-DD')
          const endDate = dateArray?.[1] ? dayjs(dateArray[1]).format('YYYY-MM-DD') : startDate
          formattedData[key] = [startDate, endDate] as P[Extract<keyof P, string>]
        }
      }
      // 处理其他formatKey中指定的字段
      if (formatKey.includes(key as unknown as keyof T)) {
        if (Array.isArray(formattedData[key]) && formattedData[key].length === 2) {
          const dateArray = formattedData[key]
          const startDate = dayjs(dateArray[0]).format('YYYY-MM-DD')
          const endDate = dateArray?.[1] ? dayjs(dateArray[1]).format('YYYY-MM-DD') : startDate
          formattedData[key] = [startDate, endDate] as P[Extract<keyof P, string>]
        }
      }
    }
    // 更新查询参数
    Object.assign(queryParams, formattedData)
    return queryParams
  }

  // 使用当前参数执行搜索
  const search = () => {
    currentPage.value = 1
    return debouncedFetchList()
  }

  // 更新参数并立即搜索
  const updateParams = (params: Partial<P>) => {
    setParams(params)
    return search()
  }

  // 刷新列表
  const refresh = () => {
    return fetchList()
  }

  // 重置列表
  const reset = () => {
    currentPage.value = 1
    Object.assign(queryParams, defaultParams)
    return fetchList()
  }

  // 页码改变
  const onPageChange = (e: DataTablePageEvent) => {
    currentPage.value = e.page + 1
    return fetchList({ page_size: e.rows } as Partial<P>)
  }

  // 获取搜索条件
  const getSearchParams = () => {
    return formatData(queryParams)
  }

  onMounted(() => {
    if (immediate) {
      search()
    }
  })

  return {
    // 状态
    list,
    loading,
    error,
    failed,
    failureMessage,
    total,
    currentPage,
    queryParams,
    other,

    // 方法
    refresh,
    reset,
    setParams,
    search,
    updateParams,
    onPageChange,
    getSearchParams,
    setSearchParams,
  }
}
