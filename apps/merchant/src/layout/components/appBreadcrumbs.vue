<script setup lang="ts">
import type { RouteRecordRaw } from 'vue-router'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'

const currentRoute = useRoute()
const router = useRouter()
const { t } = useI18n()

const breadcrumbList = computed(() =>
  currentRoute.matched.filter(item => !item.meta?.isHideBreadcrumb),
)

// 处理面包屑点击事件
const handleClick = (toName: string | undefined | null | symbol) => {
  if (toName) {
    router.push({ name: toName })
  }
}

const getLabel = (item: RouteRecordRaw) => {
  return item?.meta?.breadcrumbTitle || t(item?.meta?.i18nKey as string) || item?.name
}

const checkClickableName = (item: any) => {
  return item?.name || item?.redirect?.name
}
</script>

<template>
  <div class="app-breadcrumbs">
    <div class="breadcrumb-container">
      <div
        v-for="(item, index) in breadcrumbList"
        :key="index"
        class="breadcrumb-item"
      >
        <h1
          :class="{ clickable: checkClickableName(item), active: index === breadcrumbList.length - 1 }"
          @click="item?.name ? handleClick(item?.name as string) : null"
        >
          {{ getLabel(item) }}
        </h1>
        <i v-if="index < breadcrumbList.length - 1" class="pi pi-angle-right separator" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints' as *;
.app-breadcrumbs {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
}

.breadcrumb-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.breadcrumb-item {
  display: flex;
  align-items: center;

  h1 {
    color: var(--color-gray-500);
    font-size: 23px;
    font-weight: 800;

    @include media-breakpoint-down(sm) {
      font-size: 1.2rem;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &.clickable {
      cursor: pointer;

      &:hover {
        color: var(--primary-color);
        text-decoration: underline;
      }
    }

    &.active {
      color: var(--text-color);
    }
  }

  .separator {
    margin: 0 0.5rem;

    font-size: 1.5rem;
  }
}
</style>
