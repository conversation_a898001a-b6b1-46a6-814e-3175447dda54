<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/yup'
import { storeToRefs } from 'pinia'
import { Field, Form } from 'vee-validate'
import { computed, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import * as yup from 'yup'
import userDefaultImage from '@/assets/merchant/account-icon.png'
import noticeImage from '@/assets/merchant/notification-bell.png'
import { useAppStore } from '@/store/modules/app'
import { useLayoutStore } from '@/store/modules/layout'
import { useUserStore } from '@/store/modules/user'
import Breadcrumbs from './appBreadcrumbs.vue'

const route = useRoute()
const router = useRouter()
const { t } = useI18n()
const userSettingMenu = ref()
const userStore = useUserStore()
const appStore = useAppStore()

const { locale, rememberCompanyChangeConfirm } = storeToRefs(appStore)

const { groupList, currentGroup, isLoadingGroupList, isShowSelectBid, activeBid } = storeToRefs(userStore)

const selectSchema = toTypedSchema(yup.object({
  selectBid: yup.string().required('Please select a business'),
}))

const disabledPages = ref([
  'customersCreateInvite',
  'customersCreateInvoice',
  'planSubscriptionAdd',
  'payMyInvoiceCreateInvoice',
  'payMyInvoiceInvoiceDetailAndSend',
])

watch(locale, (newValue) => {
  appStore.setLocale(newValue as 'en' | 'zh')
})

const avatarUrl = computed(() => {
  return userStore.user?.avatar || userDefaultImage
})

const layoutStore = useLayoutStore()
const { isSidebarSlim } = storeToRefs(layoutStore)

const userSettingItems = computed(() => {
  return [
    {
      label: t('user.profile'),
      icon: 'pi pi-user',
      command: () => router.push('/user/profile'),
    },
    {
      label: t('user.settings.title'),
      icon: 'pi pi-cog',
      command: () => router.push('/user/settings'),
    },
    { separator: true },
    {
      label: t('user.logout'),
      icon: 'pi pi-power-off',
      command: async () => {
        await userStore.logout()
        router.push('/login')
      },
    },
  ]
})

const toggleSidebar = () => {
  layoutStore.setSidebarMode(isSidebarSlim.value ? 'expanded' : 'slim')
}

const toggleMobileMenu = () => {
  appStore.toggleMobileMenu()
}

// 计算属性：处理选项禁用逻辑
const groupListOptions = computed(() => {
  return groupList.value?.map(item => ({
    value: item.value,
    label: item.label,
    disabled: currentGroup.value.length === 1 && currentGroup.value.includes(item.value),
  }))
})
const multiSelectRef = ref()

// 自定义确认对话框状态
const showRememberConfirm = ref(false)
const isRememberConfirm = ref(false)

const handleClick = () => {
  if (rememberCompanyChangeConfirm.value) {
    multiSelectRef.value?.show()
    return
  }

  if (isRememberConfirm.value) {
    multiSelectRef.value?.hide()
    showRememberConfirm.value = true
    isRememberConfirm.value = false
    return
  }

  multiSelectRef.value?.hide()
  if (!isRememberConfirm.value) {
    showRememberConfirm.value = true
  }
}

const handleConfirmAccept = () => {
  isRememberConfirm.value = true
  showRememberConfirm.value = false
  multiSelectRef.value?.show()
}

const handleConfirmReject = () => {
  showRememberConfirm.value = false
  isRememberConfirm.value = false
}

const handleGroupChange = (e: { value: string[] }) => {
  if (e.value.length === 1) {
    activeBid.value = e.value[0]
  }
  multiSelectRef.value?.hide()
  // 刷新页面
  appStore.refreshRouteView()
}
</script>

<template>
  <div class="app-header">
    <div class="header-start">
      <Button class="mobile-menu-toggle" severity="secondary" @click="toggleMobileMenu">
        <i class="pi pi-bars" />
      </Button>
      <Button class="sidebar-toggle" severity="secondary" @click="toggleSidebar">
        <i class="pi" :class="isSidebarSlim ? 'pi-angle-right' : 'pi-angle-left'" />
      </Button>
      <Divider layout="vertical" />
      <Breadcrumbs />
    </div>

    <div class="header-end">
      <div class="flex items-center gap-2">
        <span class="hidden lg:block">Business :</span>
        <MultiSelect
          ref="multiSelectRef" v-model="currentGroup" :options="groupListOptions" :option-disabled="(e) => e.disabled" :loading="isLoadingGroupList" option-label="label" option-value="value"
          placeholder="Please select" :show-toggle-all="false" :disabled="disabledPages.includes(route.name as string)"
          class="lg:w-60"
          @change="handleGroupChange"
          @click="handleClick"
        />
      </div>

      <div class="user-notice">
        <img class="notice-image" :src="noticeImage" alt="notice">
      </div>
      <Menu ref="userSettingMenu" :model="userSettingItems" :popup="true" />
      <div class="user-profile" @click="userSettingMenu.toggle($event)">
        <Avatar :image="avatarUrl" shape="circle" class="user-avatar" />
      </div>
    </div>

    <!-- 自定义确认对话框 -->
    <Dialog
      v-model:visible="showRememberConfirm"
      :modal="true"
      :closable="false"
      header="Confirm"
      class="w-96"
      pt:root:class="!border-0 !bg-(--colors-pink) !rounded-[20px]" pt:mask:class="backdrop-blur-sm"
    >
      <div class="flex align-items-center gap-3 mb-4">
        <i class="pi pi-exclamation-triangle text-orange-500 text-2xl" />
        <span>Changing the business will refresh the current page. Do you want to continue?</span>
      </div>

      <div class="flex align-items-center gap-2 mb-4">
        <Checkbox v-model="rememberCompanyChangeConfirm" input-id="rememberCompanyChangeConfirm" binary />
        <label for="rememberCompanyChangeConfirm">Remember my choice</label>
      </div>

      <template #footer>
        <Button
          label="Cancel"
          severity="secondary"
          @click="handleConfirmReject"
        />
        <Button
          label="Continue"
          severity="warning"
          @click="handleConfirmAccept"
        />
      </template>
    </Dialog>

    <Dialog
      v-model:visible="isShowSelectBid"
      :modal="true"
      header="Select Business"
      class="w-120"
      :closable="false"
      pt:root:class="!border-0 !bg-(--colors-pink) !rounded-[20px]" pt:mask:class="backdrop-blur-sm"
    >
      <div class="flex align-items-center gap-3 mb-4">
        <span>Please choose a business to continue</span>
      </div>

      <Form :validation-schema="selectSchema" class="flex flex-col gap-4" @submit="userStore.confirmSelectBid">
        <Field v-slot="{ errorMessage, handleChange }" name="selectBid" class="flex flex-col gap-2">
          <div v-for="item in groupList" :key="item.value" class="flex items-center">
            <RadioButton v-model="activeBid" :invalid="!!errorMessage" :input-id="`selectBid-${item.value}`" name="selectBid" :value="item.value" @value-change="handleChange" />
            <label :for="`selectBid-${item.value}`" class="ml-2 cursor-pointer">{{ item.label }} - {{ item.value }}</label>
          </div>
          <Message v-if="errorMessage" severity="error" variant="simple" class="mt-2">
            {{ errorMessage }}
          </Message>
        </Field>
        <div class="flex justify-end gap-4">
          <!-- <Button label="Cancel" severity="secondary" @click="isShowSelectBid = false" /> -->
          <Button label="Continue" severity="warning" type="submit" />
        </div>
      </Form>
    </Dialog>
  </div>
</template>

<style lang="scss" scoped>
.mobile-menu-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
