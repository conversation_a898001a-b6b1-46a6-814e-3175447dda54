<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import billbuddyLogo from '@/assets/merchant/bill-buddy-hq-horizontal-logo.png'
import billbuddyLogoMini from '@/assets/merchant/bill-buddy-hq-vertical-logo.png'
import { useLayoutStore } from '@/store/modules/layout'
import { useUserStore } from '@/store/modules/user'
import AppMenu from './appMenu.vue'
import MobileMenu from './mobileMenu.vue'

const { t } = useI18n()

const layoutStore = useLayoutStore()

const userStore = useUserStore()

const { isSidebarSlim, isDarkTheme } = storeToRefs(layoutStore)

const { userMenu } = storeToRefs(userStore)

const transformMenu = (menu: Menu.Item): Menu.Item | null => {
  if (!menu) {
    return null
  }
  const label = menu?.meta?.i18nKey ? t(menu.meta.i18nKey) : menu?.meta?.label
  return {
    ...menu,
    meta: {
      ...menu.meta,
      label,
    },
    children: menu.children?.map(transformMenu).filter((item): item is Menu.Item => item !== null),
  }
}

const items = computed(() => {
  return userMenu.value.map((menu: Menu.Item) => transformMenu(menu)) as Menu.Item[]
})
</script>

<template>
  <aside
    class="app-merchant-sidebar" :class="{
      'sidebar-slim': isSidebarSlim,
      'theme-dark': isDarkTheme,
    }"
  >
    <div class="sidebar-header">
      <Image
        v-if="!isSidebarSlim" :src="billbuddyLogo" alt="Image" width="100%"
        @click="$router.push({ path: '/' })"
      />
      <Image
        v-else :src="billbuddyLogoMini" alt="Image" width="150px"
        @click="$router.push({ path: '/' })"
      />
    </div>
    <AppMenu :items="items" :slim="isSidebarSlim" />
    <!-- 移动设备菜单抽屉 -->
    <MobileMenu :items="items" />
  </aside>
</template>

<style>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(0);
}
</style>
