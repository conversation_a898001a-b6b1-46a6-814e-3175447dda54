<script setup lang="ts">
import { useAppStore } from '@/store/modules/app'
import { getAssetsFiles } from '@/utils/getAssetsFile'
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
// import { getAssetsFiles } from '@/utils/getAssetsFile';

const props = defineProps<{
  item: Menu.Item
  slim?: boolean
}>()

const appStore = useAppStore()

const router = useRouter()
const route = useRoute()
const menuPopover = ref()

const isExpanded = ref(route.fullPath.startsWith(props.item.path as string))

const isExternalLink = computed(() => {
  // 特殊判断 支持 /support 跳转
  if (props.item.path === '/support') {
    return true
  }
  return typeof props.item.path === 'string' && props.item.path.startsWith('http')
})

const hasSubmenu = computed(() => {
  return !!props.item?.children?.length && props.item?.children.length > 1
})

const hasMultipleSubmenuItems = computed(() => {
  return props.item?.children && props.item.children.length > 1
})

const singleSubmenuItem = computed(() => {
  return props.item?.children?.length === 1 ? props.item.children[0] : null
})

const isActive = computed(() => {
  if (!props.item.path) {
    return false
  }

  if (typeof props.item.name === 'string') {
    if (route.name === props.item.name) {
      return true
    }

    const itemPath = `/${props.item.name.toLowerCase()}`
    const currentPath = route.path.toLowerCase()

    return currentPath === itemPath
      || currentPath.startsWith(`${itemPath}/`)
  }

  return false
})

const hasActiveChild = computed(() => {
  if (!props.item.children?.length) {
    return false
  }

  return props.item.children.some((child) => {
    if (!child.name) {
      return false
    }

    return route.name === child.name
      || (typeof child.name === 'string'
        && route.path.toLowerCase().startsWith(`/${child.name.toLowerCase()}`))
  })
})

const slimMenuItems = computed<any[]>(() => {
  if (hasSubmenu.value) {
    return transformToTieredItems(props.item.children || [])
  }
  return [
    {
      ...props.item,
      label: props.item.meta?.label,
      icon: props.item.meta?.icon,
      command: () => {
        if (isExternalLink.value) {
          window.open(props.item.path, '_blank')
        }
        else if (props.item.name) {
          router.push({ name: props.item.name })
        }
      },
    },
  ]
})

const transformToTieredItems = (items: Menu.Item[]): any[] => {
  return items.map((item) => {
    const isItemExternalLink = typeof item.path === 'string' && item.path.startsWith('http')

    return {
      ...item,
      label: item.meta?.label,
      icon: item.meta?.icon,
      items: item.children ? transformToTieredItems(item.children) : undefined,
      command: () => {
        if (isItemExternalLink) {
          window.open(item.path, '_blank')
        }
        else if (item.name) {
          router.push({ name: item.name })
        }
      },
    }
  })
}

const handleClick = (event: Event, isActive: boolean) => {
  if (isExternalLink.value) {
    window.open(props.item.path, '_blank')
    return
  }

  if (props.slim) {
    if (singleSubmenuItem.value) {
      if (typeof singleSubmenuItem.value.path === 'string' && singleSubmenuItem.value.path.startsWith('http')) {
        window.open(singleSubmenuItem.value.path, '_blank')
        return
      }
      else if (singleSubmenuItem.value.name) {
        router.push({ name: singleSubmenuItem.value.name })
        return
      }
    }
    if (hasMultipleSubmenuItems.value) {
      showSlimMenu(event)
      return
    }
  }

  if (hasSubmenu.value) {
    router.push({ name: props.item.name })
    isExpanded.value = !isExpanded.value
    return
  }

  if (isActive) {
    event.preventDefault()
    return
  }

  if (props.item?.name) {
    router.push({ name: props.item.name })
    appStore.closeMobileMenu()
  }
}

const showSlimMenu = (event: Event) => {
  if (menuPopover.value && props.slim) {
    menuPopover.value.toggle(event)
  }
}

const getIconPath = (icon: string) => {
  if (icon.includes('/')) {
    return getAssetsFiles(icon)
  }
  return icon
}
</script>

<template>
  <div
    class="app-merchant-menu-item"
    :class="{ 'has-submenu': hasSubmenu, 'menu-item-slim': slim }"
  >
    <router-link
      v-if="item.path && !hasSubmenu && !slim && !isExternalLink"
      v-slot="{ isActive: linkActive }"
      :to="{ name: item.name }"
      custom
    >
      <div
        class="menu-item-link"
        :class="{ active: item.path === route.fullPath || linkActive }"
        @click="(event) => handleClick(event, linkActive)"
      >
        <div class="menu-item-icon-wrapper" :class="{ 'is-background': item?.meta?.icon?.includes('question.png') }">
          <i v-if="item?.meta?.icon && String(item?.meta?.icon)?.includes('pi')" class="menu-item-icon" :class="[item.meta.icon]" />
          <img
            v-else-if="item?.meta?.icon && String(item?.meta?.icon)?.includes('/')" class="menu-item-icon-img" :class="{
              'is-background': item?.meta?.icon?.includes('question.png'),
            }" :src="getIconPath(item?.meta?.icon)"
          >
        </div>
        <span class="menu-item-label">{{ item?.meta?.label }}</span>
      </div>
    </router-link>

    <a
      v-else-if="isExternalLink && !slim"
      :href="item.path"
      target="_blank"
      class="menu-item-link external-link"
    >
      <div class="menu-item-icon-wrapper" :class="{ 'is-background': item?.meta?.icon?.includes('question.png') }">
        <i v-if="item?.meta?.icon && String(item?.meta?.icon)?.includes('pi')" class="menu-item-icon" :class="[item.meta.icon]" />
        <img v-else-if="item?.meta?.icon && String(item?.meta?.icon)?.includes('/')" class="menu-item-icon-img" :src="getIconPath(item?.meta?.icon)">
      </div>
      <span class="menu-item-label">{{ item?.meta?.label }}</span>
      <i class="pi pi-external-link external-link-icon" />

    </a>

    <div
      v-else
      class="menu-item-link"
      :class="{ 'expanded': isExpanded && !slim, 'active': isActive || hasActiveChild, 'external-link': isExternalLink }"
      @click="handleClick($event, isActive)"
    >
      <div class="menu-item-icon-wrapper" :class="{ 'is-background': item?.meta?.icon?.includes('question.png') }">
        <i v-if="item?.meta?.icon && String(item?.meta?.icon)?.includes('pi')" class="menu-item-icon" :class="[item.meta.icon]" />
        <img v-else-if="item?.meta?.icon && String(item?.meta?.icon)?.includes('/')" class="menu-item-icon-img" :src="getIconPath(item?.meta?.icon)">
      </div>
      <span v-if="!slim" class="menu-item-label">{{ item?.meta?.label }}</span>
      <i
        v-if="hasSubmenu && !slim"
        class="submenu-icon pi pi-angle-down"
        :class="{ expanded: isExpanded }"
      />
    </div>

    <TieredMenu
      v-if="slim && hasMultipleSubmenuItems"
      ref="menuPopover"
      :model="slimMenuItems"
      popup
    />

    <div v-if="hasSubmenu && !slim" class="submenu" :class="{ expanded: isExpanded }">
      <app-menu-item
        v-for="(subItem, index) in item.children"
        :key="index"
        :item="subItem"
      />
    </div>
  </div>
</template>
