{"title": "Customers", "subtitle": "Manage your customers", "columns": {"customerId": "Customer ID", "customerName": "Customer Name", "ddrId": "DDR ID", "createdDate": "Created Date", "email": "Email", "phone": "Phone", "status": "Status", "action": "Action", "type": "Type"}, "fields": {"abn": "ABN", "title": "Title", "firstName": "First Name", "middleName": "Middle Name", "lastName": "Last Name", "birthday": "Date of Birth", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2", "addressLine3": "Address Line 3", "suburb": "Suburb", "postcode": "Postcode", "state": "State", "sameAsAbove": "Same as Above", "daytimePhoneArea": "Daytime Phone Area", "afterhourPhoneArea": "Afterhour Phone Area", "mobilePhone": "Mobile Phone", "faxArea": "Fax Area", "number": "Number", "areaCode": "Area Code", "phoneNumber": "Phone Number", "primaryEmail": "Primary Email", "secondaryEmail": "Secondary Email", "email": "Email Address", "paymentType": "Payment Type", "selectPaymentType": "Select Payment Type", "bsb": "BSB", "accountNumber": "Account Number", "accountName": "Account Name", "address": "Address", "billingDetails": "Billing Details"}, "actions": {"customerDetail": "Customer Detail", "debitSchedules": "Debit&Schedules", "ddrForecast": "DDR Forecast", "deleteDdr": "Delete DDR", "addCustomer": "Add Customer"}, "filters": {"all": "All Customers", "active": "Active", "inactive": "Inactive", "pending": "Pending", "filterBy": "Filter <PERSON>"}, "dialogs": {"addCustomer": "Add Customer", "editCustomer": "Edit Customer", "deleteCustomer": "Delete Customer", "deleteCustomerMessage": "Are you sure you want to delete this customer?", "confirmDelete": "Confirm Delete", "deleteConfirmMessage": "Are you sure you want to delete this item?"}, "form": {"customerName": "Customer Name", "email": "Email", "phoneNumber": "Phone Number", "customerNamePlaceholder": "Enter customer name", "emailPlaceholder": "Enter email address", "phoneNumberPlaceholder": "Enter phone number"}, "messages": {"customerCreated": "Customer created successfully", "customerUpdated": "Customer updated successfully", "customerDeleted": "Customer deleted successfully"}, "invite": {"title": "Invite Customers", "description": "Invite one or multiple customers to set up a Direct Debit so you're authorised to collect payments in the future.", "form": {"customerName": "Customer Name", "email": "Email", "phoneNumber": "Phone Number", "planSelection": "Select Plan & Subscription", "planOptional": "Optional, you can proceed without adding a plan", "selectPlan": "Select a plan", "unitQuantity": "Unit Quantity", "planStartDate": "Plan Start Date", "planOverview": "Plan Overview", "noPlanSelected": "No plan selected"}, "validation": {"nameRequired": "Please enter customer name", "emailRequired": "Please enter email address", "emailInvalid": "Please enter a valid email address", "phoneRequired": "Please enter phone number", "phoneInvalid": "Please enter a valid phone number", "phoneFormat": "Phone number can only contain digits, +, -, () and spaces", "phoneLength": "Phone number should be between 5 and 20 characters"}, "preview": {"title": "Preview", "emailTitle": "What your customer sees the email", "pageTitle": "What your customer sees the page", "smsTitle": "What your customer sees SMS"}, "buttons": {"sendEmail": "Send Email", "sendEmailAndSms": "Send Email & SMS", "edit": "Edit"}, "messages": {"inviteSuccess": "Invitation created successfully", "inviteFailed": "Creation failed, please try again", "checkForm": "Please check if the form is filled correctly"}}, "payments": {"createPayment": "Create Payment", "amount": "Amount", "description": "Description", "date": "Date", "enterAmount": "Please enter amount", "amountMustBePositive": "Amount must be greater than 0", "enterDescription": "Enter payment description", "selectDate": "Please select a date", "paymentCreated": "Payment created successfully", "confirmPayment": "Confirm", "cancelPayment": "Cancel"}}