import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

// Extend dayjs with timezone support
dayjs.extend(utc)
dayjs.extend(timezone)

// Server timezone constant
export const SERVER_TIMEZONE = 'Australia/Melbourne'

export const formatDate = (dateString: string | number | Date | Dayjs | null | undefined, format: string = 'MMM DD h:mm A') => {
  const defaultReturn = '-'
  if (!dateString) {
    return defaultReturn
  }
  const targetDate = dayjs(dateString)
  if (!targetDate.isValid()) {
    return defaultReturn
  }
  return targetDate.format(format)
}

/**
 * Get the current date in server timezone (Melbourne)
 * @returns Dayjs object in Melbourne timezone
 */
export const getServerDate = () => {
  return dayjs().tz(SERVER_TIMEZONE)
}

/**
 * Get tomorrow's date in server timezone (Melbourne)
 * @returns Dayjs object representing tomorrow in Melbourne timezone
 */
export const getServerTomorrow = () => {
  return getServerDate().add(1, 'day')
}

/**
 * Convert a date to server timezone and return as Date object
 * @param date - Date to convert (defaults to current date)
 * @returns Date object in server timezone
 */
export const toServerTimezoneDate = (date?: string | number | Date | Dayjs) => {
  const targetDate = date ? dayjs(date) : dayjs()
  return targetDate.tz(SERVER_TIMEZONE).toDate()
}

/**
 * Get minimum payment due date (tomorrow in server timezone)
 * @returns Date object representing tomorrow in Melbourne timezone
 */
export const getMinPaymentDueDate = () => {
  return getServerTomorrow().toDate()
}

export const formatSearchDate = (searchData: Record<string, any>, formatKeys: string[] | string, format: string = 'YYYY-MM-DD') => {
  const formatKey = Array.isArray(formatKeys) ? formatKeys : [formatKeys]
  const returnData = { ...searchData }
  formatKey.forEach((key) => {
    if (searchData[key]) {
      if (Array.isArray(searchData[key])) {
        returnData[key] = searchData[key].map((date: string) => dayjs(date).format(format))
      }
      else {
        returnData[key] = dayjs(searchData[key]).format(format)
      }
    }
  })
  return returnData
}
