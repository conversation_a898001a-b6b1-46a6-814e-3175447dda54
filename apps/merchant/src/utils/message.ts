import CustomMessage from '@/components/customMessage/index.vue'
import PrimeVue from 'primevue/config'
import Toast from 'primevue/toast'
import ToastService from 'primevue/toastservice'
import { createApp } from 'vue'

interface MessageOptions {
  message: string
  type?: 'success' | 'error'
  duration?: number
  closable?: boolean
  detail?: any
}

let messageInstance: any = null

const createMessage = () => {
  const messageNode = document.createElement('div')
  document.body.appendChild(messageNode)

  const app = createApp(CustomMessage)
  app.use(PrimeVue)
  app.use(ToastService)
  app.component('Toast', Toast)

  messageInstance = app.mount(messageNode)

  return messageInstance
}

export const message = (options: MessageOptions | string) => {
  if (!messageInstance) {
    messageInstance = createMessage()
  }

  if (typeof options === 'string') {
    messageInstance.showMessage({
      message: options,
      type: 'info',
      duration: 0, // 默认不自动关闭
      closable: true, // 默认显示关闭按钮
    })
  }
  else {
    messageInstance.showMessage({
      ...options,
      duration: options.duration || 0, // 默认不自动关闭
      closable: options.closable ?? true, // 默认显示关闭按钮
    })
  }
}
