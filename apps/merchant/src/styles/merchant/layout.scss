@use '../mixins/breakpoints' as *;

.layout-merchant-wrapper {
    min-height: 100vh;
    width: 100%;
    overflow: hidden;
    background: var(--color-white-100);

    .layout-merchant-content-wrapper {
        margin-left: calc(20rem + 24px);
        display: flex;
        flex-direction: column;
        transition: margin 0.3s ease;
        padding-left: 24px;
        padding-right: 24px;

        @include media-breakpoint-down(lg) {
            margin-left: 0;
            /* 在移动设备上充满整个宽度 */
            width: 100%;
        }

        .layout-merchant-content {
            height: 100%;
            flex: auto;
            overflow: auto;
            padding-top: calc(48px + 5rem);
            padding-bottom: 24px;
        }
    }

    .layout-merchant-sidebar {
        width: calc(20rem + 24px);

        @include media-breakpoint-down(lg) {
            display: none;
            /* 在移动设备上隐藏常规侧边栏 */
        }
    }

    &.sidebar-slim {
        .layout-merchant-sidebar {
            width: 7.1429rem;
        }

        .layout-merchant-content-wrapper {
            margin-left: calc(7.1429rem + 24px);

            @include media-breakpoint-down(lg) {
                margin-left: 0;
                /* 在移动设备上充满整个宽度 */
            }
        }
    }

    // 移动设备布局
    &.mobile-layout {

        .layout-merchant-header {
            &.sidebar-slim {
                left: 8px !important;
            }
        }

        .layout-merchant-content-wrapper {
            margin-left: 0;
            width: 100%;
            padding-left: 8px;
            padding-right: 8px;

            .layout-merchant-content {
                padding-top: calc(108px + 58px);
            }
        }

        .layout-merchant-header {
            left: 8px;
            right: 8px;
            top: 0;
        }

        .app-breadcrumbs {
            position: fixed;
            top: 108px;
            left: 8px;
            right: 8px;
            z-index: 99;
            background-color: var(--color-white);
            border-radius: 16px;
            padding: 4px 8px;
            overflow-x: auto;
            max-width: calc(100% - 16px);
        }
    }


    // Layout Components
    .layout-merchant-header {
        position: fixed;
        top: 0;
        left: calc(20rem + 48px);
        right: calc(24px + var(--p-scrollbar-width, 0px));
        padding-top: 24px;
        z-index: 99;
        transition: left 0.3s ease;
        background: var(--color-white-100);
        border-bottom-left-radius: 16px;
        border-bottom-right-radius: 16px;

        @include media-breakpoint-down(lg) {
            left: 24px;
            top: 24px;
        }

        &.sidebar-slim {
            left: calc(7.1429rem + 48px);
        }

        .app-header {
            border-radius: 16px;
            padding: 0 1.5rem;
            height: 5.713rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
            background-color: var(--color-white);
            z-index: 99;

            .header-start {
                display: flex;
                align-items: center;
                gap: 0.5rem;

                .sidebar-toggle {
                    width: 2.75rem;
                    height: 2.75rem;
                    border-radius: 50%;

                    @include media-breakpoint-down(lg) {
                        display: none;
                    }

                    .pi {
                        font-size: 1.5rem;
                        color: var(--text-color-secondary);
                    }

                    &:hover {
                        background-color: var(--surface-hover);
                    }
                }

                .mobile-menu-toggle {
                    width: 2.5rem;
                    height: 2.5rem;
                    border-radius: 50%;
                    display: none;
                    /* 默认不显示 */

                    @include media-breakpoint-down(lg) {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }

                    .pi {
                        font-size: 1.25rem;
                        color: var(--text-color-secondary);
                    }

                    &:hover {
                        background-color: var(--surface-hover);
                    }
                }

                :deep(.p-breadcrumb) {
                    background: transparent;
                    border: none;
                    padding: 0;

                    .p-breadcrumb-chevron {
                        color: var(--text-color-secondary);
                    }
                }
            }

            .header-end {
                display: flex;
                align-items: center;
                border-left: 2px solid var(--p-divider-border-color);
                padding-left: 1rem;
                gap: 1.25rem;

                .user-notice,
                .user-avatar {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 2.5rem;
                    height: 2.5rem;
                    border-radius: 50%;
                    cursor: pointer;
                    padding: 0;
                }

                .user-profile {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    cursor: pointer;
                    border-radius: 0.5rem;
                    transition: background-color 0.2s;

                    &:hover {
                        background-color: var(--surface-hover);
                    }

                    .user-name {
                        font-size: 0.875rem;
                        color: var(--text-color);
                        font-weight: 500;
                    }

                    :deep(.p-avatar) {
                        width: 2.5rem;
                        height: 2.5rem;

                        &.p-avatar-circle {
                            border-radius: 50%;
                        }

                        .p-avatar-text {
                            font-size: 1rem;
                            font-weight: 600;
                        }
                    }
                }
            }

            .sidebar-slim & {
                left: 7.1429rem;
            }
        }
    }

    .app-merchant-sidebar {
        position: fixed;
        height: calc(100vh - 48px);
        top: 24px;
        left: 24px;
        bottom: 24px;
        width: 20rem;
        display: flex;
        flex-direction: column;
        background-color: var(--color-white);
        transition: all 0.3s ease;
        padding: 12px 1.25rem;
        border-radius: 16px;

        @include media-breakpoint-down(lg) {
            display: none;
            /* 在移动设备上隐藏常规侧边栏 */
        }

        .sidebar-header {
            transition: none;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            cursor: pointer;
            position: relative;

            img {
                transition: none;
                cursor: pointer;
                width: 100%;
                max-height: 55px;
            }
        }

        &.sidebar-slim {
            width: 7.1429rem;

            .sidebar-header {
                position: relative;
                height: 75px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                padding: 0;
                transition: none;

                img {
                    opacity: 1;
                    width: 80px;
                    max-width: 150%;
                    max-height: 80px;
                    position: absolute;
                    top: 0;
                    left: -10%;
                    right: -10%;
                }
            }

            .menu-item-icon {
                margin-inline: 0;
            }

            .menu-item-icon-wrapper {
                margin-right: 0;
            }
        }

        &.theme-dark {
            background-color: var(--surface-900);
            border-color: var(--surface-700);
        }
    }
}

.app-merchant-menu {
    flex: 1;
    overflow-y: auto;
    padding-top: 0;
    height: calc(100vh - 48px - 80px - 24px);

    &.menu-slim {
        .menu-separator {
            margin: 0.5rem;
        }
    }

    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-thumb {
        background-color: var(--surface-300);
        border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
        background-color: var(--surface-ground);
    }

    .menu-separator {
        height: 1px;
        background-color: var(--p-divider-border-color);
        margin: 0.5rem 1rem;
    }
}


.app-merchant-menu-item {
    &.menu-item-slim {

        .menu-item-link {
            justify-content: center;
            padding: 1rem 0;

            .menu-item-label,
            .menu-item-badge,
            .menu-item-shortcut,
            .submenu-icon {
                display: none;
            }

            .menu-item-icon {
                font-size: 1.25rem;
            }

            .menu-item-icon-img {
                width: 1.8rem;
                height: 1.8rem;
            }
        }

        .submenu {
            position: absolute;
            left: 100%;
            top: 0;
            margin-left: 0;
            border-left: none;
            background-color: var(--colors-info);
            border: 1px solid var(--p-surface-300);
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            min-width: 200px;
            padding: 0.5rem;
            z-index: 1000;

            .menu-item-link {
                justify-content: flex-start;
                padding: 0.75rem 1rem;
                margin: 0.5rem 0;

                .menu-item-label,
                .menu-item-badge,
                .menu-item-shortcut,
                .submenu-icon {
                    display: inline-flex;
                }

                .menu-item-icon {
                    font-size: 18px;
                }

                .menu-item-icon-img {
                    width: 1.90rem;
                    height: 1.90rem;
                }
            }
        }
    }

    .menu-item-icon-wrapper {
        border-radius: 50%;
        margin-right: 0.75rem;
    }

    .menu-item-link {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        margin: 0.5rem 0;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        color: var(--color-gray-500);
        text-decoration: none;


        &.external-link {
            display: flex;
            align-items: center;
        }

        &.external-link .external-link-icon {
            margin-left: 5px;
            font-size: 0.8rem;
        }

        &:hover {
            background: var(--surface-hover);
        }

        &.expanded {
            background: var(--surface-hover);
        }

        &.active {
            background: var(--colors-info);
        }
    }

    .menu-item-icon {
        font-size: 18px;
        width: 1.25rem;
        text-align: center;
        color: var(--color-gray-500);
        margin-right: 6px;
        margin-left: 4px;
    }

    .menu-item-icon-img {
        width: 1.90rem;
        height: 1.90rem;
    }


    .menu-item-label {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: var(--color-gray-500);
        font-size: 18px;
    }

    .menu-item-badge {
        margin-left: 0.5rem;
        background: var(--primary-100);
        color: var(--primary-700);
        font-weight: 600;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 1rem;
    }

    .menu-item-shortcut {
        margin-left: 0.5rem;
        padding: 0.2rem 0.4rem;
        background: var(--surface-ground);
        border-radius: 4px;
        font-size: 0.75rem;
        color: var(--text-color-secondary);
        font-weight: 600;
    }

    .submenu-icon {
        margin-left: 0.5rem;
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        color: var(--text-color-secondary);

        &.expanded {
            transform: rotate(-180deg);
        }
    }

    .submenu {
        overflow: hidden;
        position: relative;
        margin-left: 1rem;
        border-left: 1px solid var(--surface-border);
        opacity: 0;
        max-height: 0;
        transform-origin: top;
        transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
            opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &.expanded {
            opacity: 1;
            max-height: 1000px;
        }
    }
}


// 移动设备菜单抽屉样式
.merchant-mobile-menu-drawer {
    :deep(.p-sidebar) {
        background-color: var(--color-white);
    }

    :deep(.p-sidebar-content) {
        padding: 0;
    }

    .mobile-menu-header {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1.5rem 1rem;
        border-bottom: 1px solid var(--surface-border);

        img {
            max-width: 80%;
            height: auto;
            cursor: pointer;
        }
    }
}