 
html {
    font-size: 14px;
}

body {
    --colors-primary: #181349;
    --colors-primary-1: #1B1548;
    --bg-colors-white: #f5f5ff;
    --colors-info: #09DEFF;
    --colors-pink: #FFE3E8;
    --text-primary: var(--p-primary-300);
    --surface-hover: var(--p-surface-200);
    --p-progressspinner-color-1: var(--p-primary-300);
    --swiper-pagination-color: var(--color-orange-500);
    --p-textarea-focus-border-color: var(--color-orange-500);
    --p-inputtext-focus-border-color: var(--color-orange-500);
    --p-select-focus-border-color: var(--color-orange-500);
    --p-multiselect-focus-border-color: var(--color-orange-500);
    --p-select-overlay-background: var(--colors-pink);
    --p-radiobutton-checked-border-color: var(--color-orange-500);
    --p-radiobutton-checked-background: var(--color-orange-500);
    --p-radiobutton-checked-hover-border-colo: var(--color-orange-500);
    --p-radiobutton-checked-hover-background: var(--color-orange-500);
    --p-radiobutton-border-color: var(--color-orange-500);
    --p-radiobutton-checked-hover-border-color: var(--color-orange-500);
    --p-radiobutton-focus-ring-color: var(--color-orange-500);
    --p-inputtext-border-color: var(--color-gray-500);
    --p-textarea-border-color: var(--color-gray-500);
    --p-radiobutton-border-color: var(--color-gray-500);
    --p-select-border-color: var(--color-gray-500);
    --p-multiselect-border-color: var(--color-gray-500);
    --p-inputgroup-addon-border-color: var(--color-gray-500);
    color: var(--colors-primary);

    --border-radius: 16px;
    margin: 0;
    font-family: 'Futura', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

.p-select-overlay {
    --p-select-overlay-border-radius: 8px;
    --p-select-overlay-border-color: rgba(255, 227, 232, 1);
}

.p-select-list {
    background-color: var(--colors-pink);
    --p-select-option-border-radius: 0;
    --p-select-option-selected-background: rgba(255, 200, 200, 1);
    --p-select-list-padding: 8px 16px;
    border-radius: 8px;
    --p-select-option-padding: 0.75rem;

    .p-select-option {
        border-bottom: 1px solid var(--color-gray-500);

        &:last-child {
            border-bottom: none;
        }
    }
}