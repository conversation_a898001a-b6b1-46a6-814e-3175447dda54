@use './mixins/breakpoints' as *;
@use './common/index.scss' as *;
@use './fonts.css' as *;

@use './merchant/layout.scss' as *;
@use './merchant/table.scss' as *;

.container {
    margin-left: auto;
    margin-right: auto;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    width: calc(100% - 3rem);
    max-width: 1340px;

    @include media-breakpoint-down(xxl) {
        max-width: 1140px;
    }

    @include media-breakpoint-down(lg) {
        max-width: 920px;
    }

    @include media-breakpoint-down(md) {
        max-width: 540px;
        padding-left: 1.25rem;
        padding-right: 1.25rem;
    }

    @include media-breakpoint-down(sm) {
        max-width: 100%;
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

.merchant-common-page {
    padding: 24px;
    background-color: var(--color-white);
    border-radius: 16px;

    @include media-breakpoint-down(md) {
        padding: 16px;
    }
}



// 统计卡片样式
.common-stat-card {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;

    &__item {
        flex: 1;
        padding: 1rem;
        background-color: var(--color-white);
        border-radius: 16px;
        padding: 24px;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
            box-shadow: inset 0 0 0 2px var(--color-orange-500);
        }
    }

    &__title {
        color: var(--color-gray-500);
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;
    }

    &__count {
        font-size: 24px;
        font-weight: 800;
    }

}

// 卡掩码
.add-card-mask-wrapper {
    &::before {
        content: '••••';
        padding: 0 8px;
    }
}

.talk-to-us-button {
    &.register {
        background-color: var(--bg-colors-white);
        font-weight: 700;
        margin-right: 1.5rem;
        color: var(--colors-primary);
        height: 50px;
        width: 240px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        font-size: 16px;
        transition: all 0.3s ease;

        &:hover {
            background-color: hsl(16, 100%, 70%);
        }

        &:active {
            background-color: hsl(16, 100%, 60%);
        }
    }

    &.request-call {
        background-color: var(--color-orange-500);
        font-weight: 700;
        color: white;
        height: 50px;
        width: 240px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        font-size: 16px;
        transition: all 0.3s ease;

        &:hover {
            background-color: hsl(16, 100%, 70%);
        }

        &:active {
            background-color: hsl(16, 100%, 60%);
        }
    }
}

.product-start-now-button {
    display: inline-block;
    padding: 1.5rem 2.5rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    color: #fff;
    background-color: var(--color-orange-500);
    font-weight: 700;
    text-align: center;
    font-size: 16px;

    &:hover {
        background-color: hsl(16, 100%, 70%);
    }

    &:active {
        background-color: hsl(16, 100%, 60%);
    }
}

.export-dialog {
    --p-dialog-background: rgb(255, 227, 232);

    &.p-dialog {

        .p-dialog-header {
            padding-bottom: 12px;
            border-bottom: 1px solid #e9e9e9;

            .p-dialog-close-button {
                --p-button-text-secondary-color: var(--color-orange-500);
            }
        }

        border: none;

        .p-dialog-content {
            padding-bottom: 34px;
        }
    }

    .export-options {
        border-top: 2px solid rgb(172, 184, 192);
        padding-top: 22px;
    }

    .dialog-cancel-button {
        background-color: rgb(245, 245, 255);
    }

    .export-options h3 {
        font-size: 1.1rem;
        margin-bottom: 1rem;
        color: #333;
    }

    .file-type-options {
        display: flex;
        gap: 1rem;
    }

    .format-option {
        display: flex;
        align-items: center;
    }

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 0.5rem;
    }
}

.flexirates-title {
    margin: .5rem 0 1rem 0;
    padding-bottom: 1.5rem;
    border-bottom: 2px solid #545454;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-text {
        font-size: 2rem;
        font-weight: 800;
    }
}

.flexirates-wrap {
    padding: 1.5rem;
    border-radius: 8px;
    background-color: #ffffff;
}