<script setup lang="ts">
import { useConfirm } from 'primevue/useconfirm'
import { useDialog } from 'primevue/usedialog'
import { useToast } from 'primevue/usetoast'
import { onMounted } from 'vue'

onMounted(() => {
  window.$toast = useToast()
  window.$dialog = useDialog()
  window.$confirm = useConfirm()

  // 拦截 toast.add 方法，添加默认的 life 参数
  const originalAdd = window.$toast.add
  window.$toast.add = (options) => {
    // 如果没有设置 life 参数，则添加默认值 3000
    if (typeof options === 'object' && !options.life) {
      options.life = 3000
    }
    return originalAdd(options)
  }
})
</script>

<template>
  <div class="global-wrap">
    <Toast />
    <Dialog />
    <ConfirmDialog />
  </div>
</template>
