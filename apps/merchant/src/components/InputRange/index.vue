<script setup lang="ts">
import InputText from 'primevue/inputtext'
import { computed, ref, watch } from 'vue'

const props = defineProps<{
  modelValue: string[]
  placeholder?: string
  minPlaceholder?: string
  maxPlaceholder?: string
}>()

const emit = defineEmits(['update:modelValue'])

// Initialize with empty values if not provided
const minValue = ref(props.modelValue?.[0] || '')
const maxValue = ref(props.modelValue?.[1] || '')

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    minValue.value = newValue[0] || ''
    maxValue.value = newValue[1] || ''
  }
}, { deep: true })

// Watch for internal changes and emit updates
watch([minValue, maxValue], ([newMin, newMax]) => {
  if (newMin !== props.modelValue?.[0] || newMax !== props.modelValue?.[1]) {
    emit('update:modelValue', [newMin, newMax])
  }
}, { deep: true })

const minPlaceholder = computed(() => props.minPlaceholder || 'Min')
const maxPlaceholder = computed(() => props.maxPlaceholder || 'Max')
</script>

<template>
  <div class="input-range">
    <div class="input-range-container">
      <InputText
        v-model="minValue"
        :placeholder="minPlaceholder"
        class="input-range-min"
      />
      <span class="input-range-separator">-</span>
      <InputText
        v-model="maxValue"
        :placeholder="maxPlaceholder"
        class="input-range-max"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.input-range {
  width: 100%;

  &-container {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  &-min,
  &-max {
    flex: 1;
    min-width: 80px;
  }

  &-separator {
    color: var(--text-color-secondary);
    font-weight: 600;
  }
}
</style>
