export interface Rule {
  required?: boolean
  message?: string
  validator?: (value: any, formData: Record<string, any>) => void
  [key: string]: any
}

export type RuleItem = Rule | ((value: any, formData: Record<string, any>) => void)

export interface FormProps {
  labelWidth?: string
  labelPosition?: 'left' | 'top'
  showError?: boolean
  validateFirst?: boolean
  model?: Record<string, any>
}

export interface FormContext {
  name: string
  value: any
  rules?: RuleItem | RuleItem[]
}

export interface FormExpose {
  validate: (callback?: (isValid: boolean, errors: Record<string, string[]>) => void) => boolean
  reset: () => void
  validateField: (name: string) => boolean
}

export interface FormProvide {
  model: Record<string, any>
  errors: Record<string, string[]>
  registerField: (field: FormContext) => void
  unregisterField: (name: string) => void
  validateField: (name: string) => boolean
  setFieldValue: (name: string, value: any) => void
  labelWidth: string
  labelPosition: 'left' | 'top'
  showError: boolean
}
