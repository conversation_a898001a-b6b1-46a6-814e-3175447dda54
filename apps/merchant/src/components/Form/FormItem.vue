<script lang="ts" setup>
import type { FormProvide, Rule, RuleItem } from './types'
import { computed, inject, onBeforeUnmount, onMounted, watch } from 'vue'
import { formProvideKey } from '.'

const props = defineProps<{
  label?: string
  labelPosition?: 'left' | 'top'
  name: string
  rules?: RuleItem | RuleItem[]
}>()

const form = inject<FormProvide>(formProvideKey)
if (!form) { throw new Error('FormItem must be used inside Form') }

const { model, errors, registerField, unregisterField } = form

const value = computed(() => model[props.name || ''] || '')
const hasError = computed(() => errors[props.name || '']?.length > 0)
const showError = computed(() => form.showError)
const labelStyle = computed(() => ({
  width: form.labelWidth,
}))

const isRequired = computed(() => {
  if (!props.rules) { return false }
  if (Array.isArray(props.rules)) {
    return props.rules.some((rule: Rule) => rule?.required)
  }
  return false
})

// 注册和注销表单项
onMounted(() => {
  registerField({
    name: props.name,
    value: value.value,
    rules: props.rules,
  })
})

onBeforeUnmount(() => {
  unregisterField(props.name)
})

// 监听值变化
watch(
  () => value.value,
  (newValue) => {
    form.setFieldValue(props.name, newValue)
  },
)
</script>

<template>
  <div class="form-item" :class="{ 'form-item--error': showError && hasError, 'form-item--top': labelPosition === 'top' || form?.labelPosition === 'top' }">
    <label v-if="label" class="form-item__label" :style="labelStyle">
      <slot name="label" />
      <span v-if="!$slots.label">{{ label }}</span>
      <span v-if="isRequired" class="required">*</span>
    </label>
    <div class="form-item__content">
      <slot />
      <div v-if="showError && hasError" class="form-item__error">
        {{ errors[props.name]?.[0] }}
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.form-item {
  display: flex;
  margin-bottom: 1em;
  &--top {
    flex-direction: column;
    .form-item__label {
      width: 100%;
      text-align: left;
    }
  }
}

.form-item__label {
  flex-shrink: 0;
  line-height: 32px;
  padding-right: 1em;
  text-align: right;
}

.form-item__content {
  flex: 1;
}

.form-item__error {
  color: #ff4d4f;
  font-size: 0.85em;
  margin-top: 0.25em;
}

.form-item--error :deep(input),
.form-item--error :deep(.p-inputtext) {
  border-color: #ff4d4f !important;
}

.required {
  color: #ff4d4f;
}
</style>
