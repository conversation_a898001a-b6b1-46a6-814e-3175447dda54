<script setup lang="ts">
import InputNumber from 'primevue/inputnumber'
import { computed, ref, watch } from 'vue'

const props = defineProps<{
  modelValue: number[]
  placeholder?: string
  minPlaceholder?: string
  maxPlaceholder?: string
  currency?: string
  locale?: string
  min?: number
  max?: number
  step?: number
}>()

const emit = defineEmits(['update:modelValue'])

// Initialize with empty values if not provided
const minValue = ref(props.modelValue?.[0] || null)
const maxValue = ref(props.modelValue?.[1] || null)

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    minValue.value = newValue[0] ?? null
    maxValue.value = newValue[1] ?? null
  }
}, { deep: true })

// Watch for internal changes and emit updates
watch([minValue, maxValue], ([newMin, newMax]) => {
  if (newMin !== props.modelValue?.[0] || newMax !== props.modelValue?.[1]) {
    emit('update:modelValue', [newMin, newMax])
  }
}, { deep: true })

const minPlaceholder = computed(() => props.minPlaceholder || 'Min')
const maxPlaceholder = computed(() => props.maxPlaceholder || 'Max')
</script>

<template>
  <div class="input-number-range">
    <div class="input-number-range-container">
      <InputNumber
        v-model="minValue"
        :placeholder="minPlaceholder"
        :currency="props.currency"
        :locale="props.locale"
        :min="props.min"
        :max="props.max"
        :step="props.step"
        class="input-number-range-min"
      />
      <span class="input-number-range-separator">-</span>
      <InputNumber
        v-model="maxValue"
        :placeholder="maxPlaceholder"
        :currency="props.currency"
        :locale="props.locale"
        :min="props.min"
        :max="props.max"
        :step="props.step"
        class="input-number-range-max"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.input-number-range {
  width: 100%;

  &-container {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  &-min,
  &-max {
    flex: 1;
    min-width: 80px;
  }

  &-separator {
    color: var(--text-color-secondary);
    font-weight: 600;
  }

  :deep(.p-inputnumber) {
    width: 100%;
  }

  :deep(.p-inputnumber-input) {
    width: 100%;
  }
}
</style>
