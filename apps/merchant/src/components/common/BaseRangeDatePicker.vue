<script setup lang="ts">
import type { } from 'primevue/datepicker'
import { onMounted, ref, useAttrs } from 'vue'

const emits = defineEmits(['valueChange'])

const attrs = useAttrs()

const datePickerRef = ref()

const numberOfMonths = ref(1)

const handleValueChange = (e: Date | Array<Date> | Array<Date | null> | undefined | null) => {
  if (attrs['selection-mode'] === 'range' && datePickerRef.value?.overlayVisible) {
    if (Array.isArray(e) && e.length === 2 && e[0] && e[1]) {
      datePickerRef.value.overlayVisible = false
    }
  }
  emits('valueChange', e)
}

// 解决 range 选择两个日期时，不自动关闭 popover 的问题
onMounted(() => {
  if (window.innerWidth < 768) {
    numberOfMonths.value = 1
  }
  else {
    numberOfMonths.value = 2
  }
})
</script>

<template>
  <DatePicker
    v-bind="$attrs"
    ref="datePickerRef"
    :number-of-months="numberOfMonths"
    class="base-range-date-picker"
    panel-class="base-range-date-picker-panel"
    @value-change="handleValueChange"
  >
    <template v-for="(_, slotName) in $slots" #[slotName]="slotProps">
      <slot :name="slotName" v-bind="slotProps" />
    </template>
  </DatePicker>
</template>

<style lang="scss">
.base-range-date-picker-panel {
  --p-datepicker-panel-background: var(--colors-pink);
  --p-datepicker-header-background: var(--colors-pink);
  --p-datepicker-date-selected-background: var(--color-orange-500);
  .p-datepicker-calendar-container .p-datepicker-calendar {
    --p-datepicker-group-border-color: var(--color-gray-500);
  }

  .p-datepicker-header {
    border-block-end: 1px solid var(--color-gray-500);
  }
}
</style>
