<script setup lang="ts">
import { getBankTypeImageUrl } from '@/utils/bank'

const props = withDefaults(defineProps<{
  text?: string
  cardType?: number | null
  isShowCardNumber?: boolean
}>(), {
  text: '',
  cardType: null,
  isShowCardNumber: false,
})

// 卡号掩码 只显示后 4 位
const maskCardNumber = (cardNumber: string) => {
  return cardNumber?.slice(-4)
}

const getCardTypeStyle = (cardType: number) => {
  switch (cardType) {
    case 1:
      return {
        width: '40px',
        maxWidth: '40px',
        height: '28px',
        borderRadius: '4px',
      }
    case 2:
      return {
        width: '28px',
        maxWidth: '28px',
        height: '28px',
        borderRadius: '4px',
      }
    case 3:
      return {
        width: '42px',
        maxWidth: '42px',
        height: '28px',
        borderRadius: '4px',
      }
    case 4:
      return {
        width: '34px',
        maxWidth: '34px',
        height: '32px',
        borderRadius: '4px',
      }
    case 5:
      return {
        width: '34px',
        maxWidth: '34px',
        height: '28px',
        borderRadius: '4px',
      }
    case 9:
      return {
        width: '36px',
        maxWidth: '36px',
        height: '24px',
        borderRadius: '6px',
      }
    case 0:
    case 6:
      return {
        width: '28px',
        maxWidth: '28px',
        height: '28px',
      }
  }

  return {
    width: '24px',
    height: '24px',
    borderRadius: '4px',
  }
}
</script>

<template>
  <div class="base-card-type">
    <div v-if="props.cardType !== null && getBankTypeImageUrl(props.cardType as number)" class="base-card-type__icon">
      <img :style="getCardTypeStyle(props.cardType as number)" :src="getBankTypeImageUrl(props.cardType as number)" alt="icon">
    </div>
    <template v-if="props.isShowCardNumber && props.text && maskCardNumber(props.text)">
      <span class="base-card-type__text">
        ••••
      </span>
      <div class="base-card-type__text">
        {{ maskCardNumber(props.text) }}
      </div>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.base-card-type {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
