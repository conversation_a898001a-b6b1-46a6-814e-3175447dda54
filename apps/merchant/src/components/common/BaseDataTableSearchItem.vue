<script setup lang="ts">
import { formatDate } from '@/utils/date'
import Checkbox from 'primevue/checkbox'
import DatePicker from 'primevue/datepicker'
import InputNumber from 'primevue/inputnumber'
import InputText from 'primevue/inputtext'
import Select from 'primevue/select'
import { computed, ref, watch } from 'vue'
import InputNumberRange from '../InputNumberRange/index.vue'
import InputRange from '../InputRange/index.vue'

const props = withDefaults(defineProps<{
  label: string
  component: 'Input' | 'InputNumber' | 'InputRange' | 'InputNumberRange' | 'Select' | 'Checkbox' | 'DatePicker'
  componentProps?: Record<string, any>
  value: string | number | number[] | string[] | boolean | Date | null
  isHide: boolean
  formatDate?: string
}>(), {
  label: '',
  component: 'Input',
  componentProps: () => ({}),
  value: '',
  isHide: false,
  formatDate: 'YYYY-MM-DD hh:mm:ss',
})

const emits = defineEmits(['update:value'])

const isEdit = ref(false)
const localValue = ref<any>(props.value)

watch(() => props.value, (newValue) => {
  if (newValue !== localValue.value) {
    localValue.value = newValue
  }
}, { immediate: true })

const componentMap = {
  Input: InputText,
  InputNumber,
  InputRange,
  InputNumberRange,
  Select,
  Checkbox,
  DatePicker,
}

const component = computed(() => componentMap[props.component])

const applyChanges = () => {
  if (localValue.value === props.value) {
    // 如果值没有变化，只关闭编辑模式
    isEdit.value = false
    return
  }

  // 发送更新事件
  emits('update:value', localValue.value)

  // 关闭编辑模式
  isEdit.value = false
}

// 存储父组件传入的初始值
const initialValue = ref(props.value)

// 监听 props.value 的变化，仅在首次挂载时更新初始值
watch(() => props.value, (newValue) => {
  if (JSON.stringify(initialValue.value) === JSON.stringify(undefined)) {
    initialValue.value = newValue
  }
}, { immediate: true, deep: true })

const removeItem = () => {
  // 恢复到初始值
  emits('update:value', initialValue.value)
  localValue.value = initialValue.value
  isEdit.value = false
}

const cancelEdit = () => {
  localValue.value = props.value
  isEdit.value = false
}

const formatValue = computed(() => {
  // 处理 Select 组件的值显示
  if (props.component === 'Select' && props?.componentProps?.options) {
    return props?.componentProps?.options?.find((option: { label: string, value: any }) => option.value === localValue.value)?.label || localValue.value
  }

  // 处理 Checkbox 组件的值显示
  if (props.component === 'Checkbox') {
    return localValue.value ? 'Yes' : 'No'
  }

  // 处理 InputNumber 组件的值显示
  if (props.component === 'InputNumber' && typeof localValue.value === 'number') {
    return props.componentProps?.currency
      ? new Intl.NumberFormat(props.componentProps?.locale || 'en-US', {
          style: 'currency',
          currency: props.componentProps.currency,
        }).format(localValue.value)
      : localValue.value.toString()
  }

  return localValue.value
})

const formatRangeValues = (values: any[]) => {
  if (!Array.isArray(values) || values.length !== 2) {
    return ''
  }

  // 处理日期范围
  if (props.component === 'DatePicker') {
    return values.map(value => formatDate(value)).join(' ~ ')
  }

  // 处理数字范围
  if (props.component === 'InputNumberRange') {
    const formatter = props.componentProps?.currency
      ? new Intl.NumberFormat(props.componentProps?.locale || 'en-US', {
        style: 'currency',
        currency: props.componentProps.currency,
      })
      : new Intl.NumberFormat(props.componentProps?.locale || 'en-US')

    return values.map(value => value !== null && value !== undefined ? formatter.format(value) : '-').join(' ~ ')
  }

  // 处理文本范围
  return values.map(value => value || '-').join(' ~ ')
}

const isActive = computed(() => {
  if (Array.isArray(props.value) && props.value.length === 2) {
    return true
  }
  if (!Array.isArray(props.value) && props.value !== '') {
    return true
  }
  return false
})
</script>

<template>
  <div v-if="!props.isHide" class="base-data-table-search-item" :class="{ active: isActive }">
    <button v-if="!isEdit" class="base-data-table-search-item-tag" @click="isEdit = true">
      <i class="pi" :class="props.value ? 'pi pi-pencil' : 'pi pi-plus'" />
    </button>
    <span class="base-data-table-search-item-label">
      {{ props.label }}
    </span>
    <!-- 范围类组件的值显示 -->
    <span v-if="!isEdit && Array.isArray(localValue) && localValue.length === 2" class="base-data-table-search-item-value">
      | {{ formatRangeValues(localValue) }}
    </span>

    <!-- 非范围类组件的值显示 -->
    <span v-if="!isEdit && !Array.isArray(localValue) && localValue !== null && localValue !== '' && localValue !== undefined" class="base-data-table-search-item-value">
      <!-- Checkbox 特殊显示 -->
      <template v-if="props.component === 'Checkbox'">
        | <i class="pi" :class="localValue ? 'pi-check-circle text-success' : 'pi-times-circle text-danger'" />
        {{ localValue ? 'Yes' : 'No' }}
      </template>

      <!-- 其他组件通用显示 -->
      <template v-else>
        | {{ formatValue }}
      </template>
    </span>
    <component
      :is="component" v-if="isEdit" v-model="localValue" v-bind="props.componentProps"
      :option-label="props.component === 'Select' ? 'label' : undefined"
      :option-value="props.component === 'Select' ? 'value' : undefined"
    />
    <button v-if="isEdit" class="base-data-table-search-item-action" @click="applyChanges">
      <i class="pi pi-check" />
    </button>
    <button v-if="isEdit" class="base-data-table-search-item-action" @click="removeItem">
      <i class="pi pi-trash" />
    </button>
    <button v-if="isEdit" class="base-data-table-search-item-action" @click="cancelEdit">
      <i class="pi pi-undo" />
    </button>
  </div>
</template>

<style lang="scss" scoped>
.base-data-table-search-item {
  --primary-color: #dee2e6;

  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px dashed var(--primary-color);
  transition: all 0.3s ease;

  &.active {
    --primary-color: var(--p-primary-color);
    border: 1px solid var(--primary-color);
  }

  .base-data-table-search-item-edit {
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .base-data-table-search-item-label {
    color: var(--text-color);
  }

  .base-data-table-search-item-tag {
    border: none;
    background: transparent;
    border: 1px solid var(--primary-color);
    border-radius: 50%;
    padding: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: #6c757d;

    i {
      color: var(--primary-color);
      font-size: 10px;
    }

    &:hover {
      background: #f8f9fa;
    }
  }

  .base-data-table-search-item-action {
    border: none;
    background: transparent;
    border: 1px solid var(--p-primary-color);
    border-radius: 50%;
    padding: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: #6c757d;

    i {
      color: var(--p-primary-color);
      font-size: 10px;
    }

    &:hover {
      background: #f8f9fa;
    }
  }

  .base-data-table-search-item-value {
    color: var(--primary-color);
  }

  :deep(.p-inputtext),
  :deep(.p-datepicker) {
    padding: 0.25rem 0.5rem;
  }

  :deep(.p-select-label) {
    padding: 0.25rem 0.75rem;
  }
}
</style>
