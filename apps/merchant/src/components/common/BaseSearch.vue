<script setup lang="ts">
import type { SearchField } from '@/types/search'
// 导入 PrimeVue 组件
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'
import { computed, ref, watch } from 'vue'
import { SearchFieldType } from '@/constants/search'
// 导入自定义组件
import BaseSearchItem from './BaseSearchItem.vue'

const props = withDefaults(defineProps<{
  loading?: boolean
  basicSearchFields?: SearchField[]
  advancedSearchFields?: SearchField[]
  modelValue?: Record<string, any>
}>(), {
  loading: false,
  basicSearchFields: () => [],
  advancedSearchFields: () => [],
  modelValue: () => ({}),
})

const emits = defineEmits(['search', 'update:modelValue'])

// 控制高级筛选弹窗显示
const showAdvancedFilters = ref(false)

// 存储所有字段的初始值
const initialValues = ref<Record<string, any>>({})

const searchModel = computed({
  get() {
    return props.modelValue || {}
  },
  set(value) {
    emits('update:modelValue', value)
  },
})

// 合并所有搜索字段
const allSearchFields = computed(() => {
  return [...props.basicSearchFields, ...props.advancedSearchFields]
})

// 获取字段的初始值
const getInitialValue = (field: SearchField) => {
  if (field.defaultValue !== undefined) {
    return field.type === SearchFieldType.NUMBER_RANGE && Array.isArray(field.defaultValue)
      ? [...field.defaultValue]
      : field.defaultValue
  }

  // 如果没有默认值，根据字段类型设置空值
  if (field.type === SearchFieldType.NUMBER_RANGE) {
    return [null, null]
  }
  else if (field.type === SearchFieldType.CHECKBOX || field.type === SearchFieldType.SWITCH) {
    return false
  }
  else if (field.type === SearchFieldType.SELECT || field.type === SearchFieldType.RADIO) {
    return ''
  }
  else if (field.type === SearchFieldType.DATE_RANGE) {
    return null
  }
  else {
    return ''
  }
}

// 初始化搜索字段的默认值，并保存初始状态
const initSearchFields = () => {
  if (allSearchFields.value && allSearchFields.value.length > 0) {
    const newModel = { ...searchModel.value }
    let hasChanges = false

    allSearchFields.value.forEach((field) => {
      // 先保存初始值
      initialValues.value[field.name] = getInitialValue(field)

      // 如果模型中还没有该字段的值，设置初始值
      if (newModel[field.name] === undefined) {
        newModel[field.name] = initialValues.value[field.name]
        hasChanges = true
      }
    })

    if (hasChanges) {
      emits('update:modelValue', newModel)
    }
  }
}

// 监听搜索字段变化，初始化默认值
watch(() => allSearchFields.value, () => {
  initSearchFields()
}, { immediate: true })

const handleSearch = () => {
  emits('search')
  showAdvancedFilters.value = false
}

// 清除数值范围
const clearNumberRange = (fieldName: string) => {
  if (searchModel.value[fieldName] && Array.isArray(searchModel.value[fieldName])) {
    searchModel.value[fieldName] = [null, null]
  }
}

// Helper method to check if filters are applied
const checkHasFilters = () => {
  if (!searchModel.value || !props.advancedSearchFields) { return false }

  for (const field of props.advancedSearchFields) {
    const value = searchModel.value[field.name]
    if (value === undefined || value === null) { continue }
    if (Array.isArray(value) && value.some(v => v !== null && v !== '')) { return true }
    if ((field.type === SearchFieldType.CHECKBOX || field.type === SearchFieldType.SWITCH) && value) { return true }
    if (value !== '') { return true }
  }
  return false
}

// Inline computed property that can be used directly in the template
const filtersApplied = computed(() => checkHasFilters())
const filterIcon = computed(() => checkHasFilters() ? 'pi-filter-fill' : 'pi-filter')

// 重置所有搜索字段到初始值
const resetAllFields = () => {
  // 对所有字段，从initialValues恢复初始值
  allSearchFields.value.forEach((field) => {
    const initialValue = initialValues.value[field.name]
    if (initialValue !== undefined) {
      // 对于数组类型，使用深拷贝避免引用问题
      if (Array.isArray(initialValue)) {
        searchModel.value[field.name] = [...initialValue]
      }
      else {
        searchModel.value[field.name] = initialValue
      }
    }
  })

  // 触发搜索
  handleSearch()
}

// Explicitly expose computed properties for TypeScript
defineExpose({
  hasAdvancedFilters: filtersApplied,
})
</script>

<template>
  <div class="common-search-wrap">
    <div class="common-search">
      <div class="search-container bg-white p-4">
        <div class="search-form-container gap-15">
          <!-- 基本搜索字段的自定义布局 -->
          <div v-for="field in basicSearchFields" :key="field.name" class="search-field-container">
            <div class="search-form-label">
              {{ field.label }}
            </div>
            <BaseSearchItem :field="field" :model-value="searchModel" :is-advanced="false" />
          </div>
        </div>

        <div class="search-container-actions">
          <!-- More Filters 按钮 -->
          <div v-if="advancedSearchFields.length > 0" class="more-filters-container flex items-end">
            <Button
              :icon="`pi ${filterIcon}`" label="More Filters" class="more-filters-btn"
              :class="{ 'has-filters': filtersApplied }" text @click="showAdvancedFilters = true"
            >
              <span>More Filters</span>
            </Button>
          </div>
          <!-- reset -->
          <div class="more-filters-container flex items-end">
            <Button
              label="Clear Filters" class="more-filters-btn" text :loading="loading"
              @click="resetAllFields"
            />
          </div>
          <!-- Search 按钮 -->
          <!-- Search 按钮 -->
          <div class="search-btn-container flex items-end">
            <Button
              label="SEARCH" icon="pi pi-search" class="search-btn" :loading="loading" severity="warn"
              @click="handleSearch"
            />
          </div>
        </div>
      </div>

      <!-- 高级筛选对话框 -->
      <Dialog
        v-model:visible="showAdvancedFilters" header="Advanced Filters" :modal="true" :style="{ width: '650px' }"
        :closable="true" draggable :dismissable-mask="true"
      >
        <div class="advanced-filters-content p-4">
          <div v-for="field in advancedSearchFields" :key="field.name" class="mb-6">
            <div
              v-if="field.type !== SearchFieldType.CHECKBOX && field.type !== SearchFieldType.SWITCH"
              class="text-lg font-medium mb-2"
            >
              {{ field.label }}
            </div>
            <BaseSearchItem
              :field="field" :model-value="searchModel" :is-advanced="true"
              :on-clear="() => field.type === SearchFieldType.NUMBER_RANGE ? clearNumberRange(field.name) : null"
            />
          </div>
          <div class="search-btn-container w-full flex justify-end">
            <Button
              label="SEARCH" icon="pi pi-search" class="search-btn" :loading="loading" severity="warn"
              @click="handleSearch"
            />
          </div>
        </div>
      </Dialog>
    </div>
  </div>
</template>

<style lang="scss">
@use '@/styles/mixins/breakpoints' as *;

.common-search-wrap {

  .search-btn {
    width: 140px;
    height: 50px;

    @include media-breakpoint-down(md) {
      width: 100%;
      height: 44px;
    }
  }

  .common-search {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .more-filters-btn {
    height: 50px;
    margin-right: 12px;

    @include media-breakpoint-down(md) {
      margin-right: 4px;
    }
  }

  .search-container {
    background: var(--color-white);
    padding: 16px;
    border-radius: 16px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 16px;

    @include media-breakpoint-down(xl) {
      flex-direction: column;
      padding: 12px;
    }

    @include media-breakpoint-down(md) {
      padding: 10px;
      border-radius: 12px;
      gap: 12px;
    }

    .search-form-container {
      display: flex;
      align-items: start;
      padding: 8px;
      margin-bottom: 16px;
      flex: 1;
      flex-wrap: nowrap;
      width: 100%;
      gap: 4rem;

      @include media-breakpoint-down(xxxl) {
        gap: 1rem;
      }

      @include media-breakpoint-down(xl) {
        flex-wrap: wrap;
        flex-direction: column;
        gap: 16px;
        padding: 6px;
        margin-bottom: 12px;
      }

      @include media-breakpoint-down(sm) {
        flex-direction: column;
        gap: 8px;
        padding: 4px;
        margin-bottom: 8px;
      }
    }

    .search-field-container {
      @include media-breakpoint-down(xl) {
        width: 100%;
      }
    }

    .search-container-actions {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 16px;

      @include media-breakpoint-down(md) {
        justify-content: center;
        align-items: stretch;
        gap: 4px;

        .more-filters-container,
        .search-btn-container {
          width: 100%;
        }
      }
    }

    .search-form-label {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 16px;
      color: var(--color-gray-500);

      @include media-breakpoint-down(md) {
        font-size: 18px;
        margin-bottom: 12px;
      }

      @include media-breakpoint-down(sm) {
        font-size: 16px;
        margin-bottom: 8px;
      }
    }

    .search-form-input-group {
      position: relative;
    }

    .search-form-date-range {
      .p-inputtext {
        height: 44px;
      }
    }

    .search-form-select {
      height: 44px;
      width: 200px;
      font-size: 16px;
      line-height: 28px;
      border-radius: 12px;

      @include media-breakpoint-down(xl) {
        width: 100%;
      }

      @include media-breakpoint-down(sm) {
        height: 40px;
        font-size: 14px;
        line-height: 24px;
        border-radius: 8px;
      }
    }

    .search-form-select-arrow {
      display: inline-block;
      width: 0;
      height: 0;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-top: 8px solid var(--color-orange-500);
      border-radius: 4px;
    }

    .p-select-label {
      font-size: 13px;
      color: var(--color-orange-500);
      font-weight: 600;
    }

    .search-form-input-icon {
      position: absolute;
      top: 50%;
      left: 18px;
      transform: translateY(-50%);
      z-index: 2;
      font-size: 16px;
      color: var(--p-button-warn-background);

      @include media-breakpoint-down(sm) {
        font-size: 14px;
        left: 14px;
      }
    }

    .search-form-input {
      height: 44px;
      width: 420px;
      position: relative;
      border-radius: 12px;
      font-size: 16px;
      padding-left: 38px;

      @include media-breakpoint-down(xl) {
        width: 100%;
      }

      @include media-breakpoint-down(sm) {
        height: 40px;
        font-size: 14px;
        padding-left: 34px;
        border-radius: 8px;
      }
    }

    .p-datepicker {
      height: 44px;
      width: 200px;
      position: relative;
      font-size: 16px;
      --p-inputtext-background: var(--color-white);

      @include media-breakpoint-down(xl) {
        width: 100%;
      }

      @include media-breakpoint-down(sm) {
        height: 40px;
        font-size: 14px;
      }

      .p-datepicker-input {
        border-radius: 12px;

        @include media-breakpoint-down(sm) {
          border-radius: 8px;
        }
      }
    }
  }

  // 高级筛选对话框响应式调整
  :deep(.p-dialog) {
    @include media-breakpoint-down(md) {
      width: 90vw !important;
      max-width: 90vw !important;
    }

    .advanced-filters-content {
      @include media-breakpoint-down(sm) {
        padding: 12px !important;

        .mb-6 {
          margin-bottom: 16px !important;
        }

        .text-lg {
          font-size: 16px !important;
        }
      }
    }
  }
}
</style>
