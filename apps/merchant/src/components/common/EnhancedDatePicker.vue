<script setup lang="ts">
import dayjs from 'dayjs'
import But<PERSON> from 'primevue/button'
import DatePicker from 'primevue/datepicker'
import { computed, ref, watch } from 'vue'

defineOptions({
  name: 'EnhancedDatePicker',
})

const props = defineProps({
  modelValue: {
    type: [Date, Date],
    default: null,
  },
  inline: {
    type: Boolean,
    default: false,
  },
  showIcon: {
    type: Boolean,
    default: true,
  },
  placeholder: {
    type: String,
    default: 'Select Date',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  class: {
    type: String,
    default: '',
  },
  showPresets: {
    type: Boolean,
    default: true,
  },
  minDate: {
    type: Date,
    default: undefined,
  },
  maxDate: {
    type: Date,
    default: undefined,
  },
  selectionMode: {
    type: String as () => 'single' | 'multiple' | 'range' | undefined,
    default: 'single',
    validator: (value: string) => ['single', 'multiple', 'range'].includes(value),
  },
  dateFormat: {
    type: String,
    default: 'dd/mm/yy',
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

const datepicker = ref()

export interface DatePreset {
  label: string
  value: Date
}

// Generate date presets
const getDatePresets = (baseDate: Date = new Date()): DatePreset[] => {
  const base = dayjs(baseDate)

  return [
    { label: 'Today', value: base.toDate() },
    { label: 'Tomorrow', value: base.add(1, 'day').toDate() },
    { label: 'In 1 week', value: base.add(1, 'week').toDate() },
    { label: 'In 1 month', value: base.add(1, 'month').toDate() },
    { label: 'In 3 months', value: base.add(3, 'month').toDate() },
    { label: 'In 6 months', value: base.add(6, 'month').toDate() },
    { label: 'In 1 year', value: base.add(1, 'year').toDate() },
  ]
}

const datePresets = computed(() => getDatePresets())
const selectedDate = ref(props.modelValue)

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  selectedDate.value = newValue
})

// Watch for internal changes
watch(selectedDate, (newValue) => {
  emit('update:modelValue', newValue)
})

// Handle preset selection
const selectPreset = (preset: DatePreset) => {
  if (datepicker.value && datepicker.value?.overlayVisible) {
    datepicker.value.overlayVisible = false
  }
  selectedDate.value = preset.value
  emit('change', preset.value)
}
</script>

<template>
  <div class="enhanced-date-picker">
    <DatePicker
      ref="datepicker"
      v-model="selectedDate"
      :inline="inline"
      :show-icon="showIcon"
      :placeholder="placeholder"
      :disabled="disabled"
      :class="props.class"
      :min-date="minDate"
      :max-date="maxDate"
      :selection-mode="selectionMode"
      :date-format="dateFormat"
      pt:panel="{ class: 'calendar-panel' }"
      pt:content="{ class: 'calendar-content' }"
    >
      <template #footer>
        <div v-if="showPresets" class="date-presets-panel">
          <div class="date-presets-title">
            Quick Select
          </div>
          <div class="date-presets-buttons">
            <Button
              v-for="preset in datePresets"
              :key="preset.label"
              size="small"
              :label="preset.label"
              class="preset-button"
              rounded
              @click="selectPreset(preset)"
            />
          </div>
        </div>
      </template>
    </DatePicker>
  </div>
</template>

<style lang="scss" scoped>
.enhanced-date-picker {
  width: 100%;
  :deep(.p-datepicker) {
    padding: 0;
  }
}

.date-presets-panel {
  padding: 0.25rem 0.75rem;
}

.date-presets-title {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  text-align: center;
}

.date-presets-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
}

.preset-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

:deep(.calendar-panel) {
  min-width: 300px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

:deep(.calendar-content) {
  padding-bottom: 0;
}

:deep(.p-datepicker) {
  border-radius: 8px;
  padding: 0.5rem;
}

:deep(.p-datepicker-header) {
  border-bottom: 1px solid var(--surface-border, #dee2e6);
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
}

:deep(.p-datepicker-calendar) {
  margin: 0.5rem 0;
}

:deep(.p-datepicker-calendar th) {
  font-weight: 600;
}

:deep(.p-datepicker-calendar td) {
  padding: 0.25rem;
}

:deep(.p-highlight) {
  border-radius: 50%;
}
</style>
