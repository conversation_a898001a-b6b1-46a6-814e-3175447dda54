<script setup lang="ts">
interface Props {
  visible: boolean
  title?: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  visible: false,
})

const emit = defineEmits<Emits>()

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

defineExpose({
  handleClose,
})
</script>

<template>
  <Dialog
    :visible="visible" :modal="true" :closable="true" class="custom-flexirates-dialog"
    @update:visible="(val: boolean) => emit('update:visible', val)" @hide="handleClose"
  >
    <template #container>
      <div class="dialog-container">
        <header v-if="props.title" class="dialog-header">
          <h2 class="dialog-title">
            {{ props.title }}
          </h2>
          <div class="dialog-controls">
            <slot name="header-status" />
            <i class="pi pi-times close-icon" @click="handleClose" />
          </div>
        </header>
        <div class="dialog-content">
          <slot name="content" />
        </div>
      </div>
    </template>
  </Dialog>
</template>

<style lang="scss">
.custom-flexirates-dialog.p-dialog {

  background-color: (--color-white-100);

  .dialog-container {
    padding: 1.5rem 2rem;
  }

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #545454;
  }

  .dialog-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #031f73;
  }

  .dialog-controls {
    display: flex;
    align-items: center;
    gap: 1.5rem;
  }

  .close-icon {
    cursor: pointer;
    color: #0073cf;
    font-weight: 700;
    font-size: 1rem;
  }

  .dialog-content {
    color: #545454;
  }
}
</style>
