import { definePreset, palette } from '@primeuix/themes'
import Aura from '@primeuix/themes/aura'

const primaryColors: Record<number, string> = palette('#181349')

const infoColors: Record<number, string> = palette('#09DEFF')

const warnColors: Record<number, string> = palette('#FE4C1C')

const successColors: Record<number, string> = palette('#00C49F')

const Noir = definePreset(Aura, {
  semantic: {
    primary: {
      ...primaryColors,
    },
    sky: {
      ...infoColors,
    },
    orange: {
      ...warnColors,
    },
    green: {
      ...successColors,
    },
  },
})

export default Noir
