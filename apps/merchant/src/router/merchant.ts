import { publicRouterName } from './publicRouterName'

// merchant 静态路由
export const merchantRoutes = [
  {
    path: 'login',
    name: publicRouterName.LOGIN,
    component: () => import('@/views/login/loginView.vue'),
  },
  {
    path: 'register',
    name: publicRouterName.REGISTER,
    component: () => import('@/views/login/registerView.vue'),
  },
  {
    path: 'forgot-password',
    name: publicRouterName.FORGOT_PASSWORD,
    component: () => import('@/views/login/forgotPasswordView.vue'),
  },
  {
    path: 'new-password/:token',
    name: publicRouterName.NEW_PASSWORD,
    component: () => import('@/views/login/newPasswordView.vue'),
  },
  {
    path: 'two-factor-auth',
    name: publicRouterName.TWO_FACTOR_AUTH,
    component: () => import('@/views/user/two-factor-auth.vue'),
    meta: {
      title: 'Two-Factor Authentication',
      requiresAuth: true,
    },
  },
  {
    path: 'invite/:id(.*)',
    component: () => import('@/views/customers/invite.vue'),
    name: publicRouterName.INVITE,
    meta: {
      title: 'Invite',
    },
  },
  {
    path: 'accounting/payment/:id(.*)',
    name: publicRouterName.ACCOUNTING_PAYMENT,
    component: () => import('@/views/accounting/payment.vue'),
    meta: {
      title: 'accountingPayment',
    },
  },
  {
    path: 'payMyInvoice/payment/:id(.*)',
    name: publicRouterName.PAY_MY_INVOICE,
    component: () => import('@/views/payMyInvoice/payment.vue'),
  },
]
