import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import { useUserStore } from '@/store/modules/user'

// 白名单路由，不需要登录即可访问
const whitelist = [
  // merchant
  'login',
  'register',
  'forgotPassword',
  'twoFactorAuth',
  'invite',
  'invoicePayment',
  'newPassword',
  'merchantAgreement',
  'payMyInvoice',

]

export const merchantGuard = async (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
  const isWhiteList = whitelist.includes(to.name as string)

  const userStore = useUserStore()

  if (isWhiteList) {
    // 如果用户已登录，并且访问的是登录页面，则重定向到首页
    if (userStore.isLoggedIn && to.name === 'login') {
      return next({ path: '/' })
    }
    return next()
  }
  else {
    if (!userStore.isLoggedIn && from.name !== 'login') {
      const redirect = to?.path || '/'
      if (redirect === '/' || redirect === '/home') {
        return next({ name: 'login', query: { ...to.query } })
      }

      return next({
        name: 'login',
        query: {
          redirect,
          ...to.query,
        },
      })
    }
    else if (userStore.isLoggedIn && userStore.user === null) {
      await userStore.initializeFromStorage()
      return next({ path: to.path, query: to.query })
    }
    if (userStore.isNeed2FA && to.name !== 'twoFactorAuth') {
      return next({
        name: 'twoFactorAuth',
      })
    }
    else {
      return next()
    }
  }
}
