<script setup lang="ts">
import { getTransactionPayout } from '@/services/api/report'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'echarts/charts'
import { GraphicComponent, GridComponent, LegendComponent, TitleComponent, ToolboxComponent, TooltipComponent } from 'echarts/components'
import * as echarts from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { onMounted, ref, watch } from 'vue'

echarts.use([TitleComponent, GridComponent, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, CanvasRenderer, GraphicComponent, LegendComponent, ToolboxComponent, LineChart, TooltipComponent])

const countChart = ref()
const countOption = ref({
  title: {
    text: 'Transactions & Payout',
    left: 'center',
    top: '10px',
    textStyle: {
      color: '#888',
      fontWeight: 'normal',
      fontSize: 18,
    },
  },
  tooltip: {
    trigger: 'axis',
  },
  legend: {
    data: ['Transactions', 'Payout'],
    left: 'center',
    bottom: '1px',
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '7%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: [''],
  },

  yAxis: {
    type: 'value',
    position: 'right',
  },
  series: [
    {
      name: 'Transactions',
      type: 'line',
      stack: 'Total',
      data: [1],
    },
    {
      name: 'Payout',
      type: 'line',
      stack: 'Total',
      data: [1],
    },
  ],
})
const breakdownChart = ref()
const breakdownOption = ref({
  title: {
    text: 'Transactions type breakdown',
    left: 'center',
    top: '10px',
    textStyle: {
      color: '#888',
      fontWeight: 'normal',
      fontSize: 18,
    },
  },
  tooltip: {
    trigger: 'item',
  },
  legend: {
    left: 'center',
    bottom: '1px',
  },

  series: [
    {
      name: 'Access From',
      type: 'pie',
      radius: '50%',
      avoidLabelOverlap: false,
      padAngle: 1,
      itemStyle: {
        borderRadius: 5,
      },
      label: {
        show: true,
        position: 'inner',
        formatter: '{b}\n{d}%',
      },
      labelLine: {
        show: false,
      },
      data: [{}],
    },
  ],
})

const sales = ref([{}])
const periodOptions = ref([
  { name: '3 days', code: '3' },
  { name: '7 days', code: '7' },
  { name: '14 days', code: '14' },
  { name: '30 days', code: '30' },
])
const selectedPeriod = ref({ name: '3 days', code: '3' })
const periodMonthOptions = ref([
  { name: 'Last 1 month', code: '1' },
  { name: 'Last 3 month', code: '3' },
  { name: 'Last a year', code: '12' },
])
const selectedPeriodMonth = ref({ name: 'Last 1 month', code: '1' })

const timeframe = ref('')
const fetchData = () => {
  const params = {
    trCountLast: selectedPeriod.value.code,
    costLast: selectedPeriodMonth.value.code,
  }
  getTransactionPayout(params).then((res) => {
    const data = res.data
    // 在数据赋值前清空数组
    breakdownOption.value.series[0].data = [] // 清空饼图数据
    sales.value = [] // 清空表格数据

    timeframe.value = `${data.cost_date_range.start} - ${data.cost_date_range.end}`

    countOption.value.series[0].data = Object.values(data.daily).map((item) => {
      return Number(item)
    })

    countOption.value.series[1].data = Object.values(data.payout_daily).map((item) => {
      return Number(item)
    })

    countOption.value.xAxis.data = Object.keys(data.daily).map((item) => {
      return item
    })

    Object.keys(data.by_brand).forEach((key: string) => {
      return breakdownOption.value.series[0].data.push(
        {
          name: key,
          value: data.by_brand[key],
        },
      )
    })

    sales.value = []
    Object.keys(data.cost_by_brand).forEach((key: string) => {
      return sales.value.push(
        {
          monthly: key,
          netSales: data.cost_by_brand[key].net_amount,
          percent: data.cost_by_brand[key].fee_amount,
          dollar: data.cost_by_brand[key].fee_ratio,
        },
      )
    })

    if (countOption.value) {
      countChart.value.setOption(countOption.value)
    }

    if (breakdownOption.value) {
      breakdownChart.value.setOption(breakdownOption.value)
    }
  })
}

// 监听日期间隔变化
watch(selectedPeriod, () => {
  fetchData()
})

// 监听月份间隔变化
watch(selectedPeriodMonth, () => {
  fetchData()
})

onMounted(() => {
  const countChartDom = document.getElementById('count')
  const breakdownChartDom = document.getElementById('breakdown')
  if (countChartDom) {
    countChart.value = echarts.init(countChartDom)
    countOption.value && countChart.value.setOption(countOption.value)
  }
  if (breakdownChartDom) {
    breakdownChart.value = echarts.init(breakdownChartDom)
    breakdownOption.value && breakdownChart.value.setOption(breakdownOption.value)
  }

  Promise.all([
    fetchData(),
  ])
})
</script>

<template>
  <div>
    <div class="text-xl font-semibold">
      Transactions count for last
    </div>

    <div>
      <div class="w-2xl h-[350px] relative">
        <div class=" absolute top-0 right-0 z-100">
          <Select v-model="selectedPeriod" :options="periodOptions" option-label="name" class=" bg-transparent " />
        </div>
        <div id="count" style="width: 100%;height: 100%;" />
      </div>
      <div class="breakdown mt-10">
        <div class="title font-semibold text-lg">
          Transactions type break down
        </div>
        <div class="w-2xl h-[350px]">
          <div id="breakdown" style="width: 100%;height: 100%;" />
        </div>
      </div>
      <div class="acceptance mt-10">
        <div class="title font-semibold text-lg">
          Cost of acceptance for last
        </div>
        <div class="mt-4 relative">
          <div class=" absolute top-[-40px] right-0 z-100">
            <Select v-model="selectedPeriodMonth" :options="periodMonthOptions" option-label="name" class=" bg-transparent " />
          </div>
          <div>
            <DataTable :value="sales" table-style="min-width: 50rem" show-gridlines>
              <ColumnGroup type="header">
                <Row>
                  <Column :rowspan="3">
                    <template #header>
                      <div class="flex flex-col gap-y-2">
                        <span>Calculated timeframe</span>
                        <!-- <span class="text-[#333] font-semibold text-xl">Monthly</span> -->
                        <span class="text-[#333] font-semibold text-xl">{{ timeframe }}</span>
                      </div>
                    </template>
                  </Column>
                  <Column header="Net Sales" :rowspan="3" />
                  <Column header="Cost of Acceptance" :colspan="4" />
                </Row>
                <Row>
                  <Column header="($)" />
                  <Column header="(%) of net sales" />
                </Row>
              </ColumnGroup>
              <Column field="monthly" />
              <Column field="netSales">
                <template #body="slotProps">
                  ${{ slotProps.data.netSales }}
                </template>
              </Column>
              <Column field="percent">
                <template #body="slotProps">
                  ${{ slotProps.data.percent }}
                </template>
              </Column>
              <Column field="dollar">
                <template #body="slotProps">
                  {{ slotProps.data.dollar }}%
                </template>
              </Column>
            </DataTable>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
