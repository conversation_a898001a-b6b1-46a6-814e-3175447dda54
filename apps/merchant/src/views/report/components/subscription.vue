<script setup lang="ts">
import { getSubscription } from '@/services/api/report'
import { LineChart } from 'echarts/charts'
import { GridComponent, LegendComponent, TitleComponent } from 'echarts/components'
import * as echarts from 'echarts/core'
import { <PERSON>vasRenderer } from 'echarts/renderers'

import { onMounted, ref, watch } from 'vue'

echarts.use([TitleComponent, GridComponent, CanvasRenderer, LegendComponent, LineChart])

const overview = ref<any[]>([])

const AChart = ref()
const AOption = ref({
  xAxis: {
    type: 'category',
    data: ['1', '2', '3', '4'],
  },
  yAxis: {
    type: 'value',
  },
  title: {
    text: '',
    left: 'center',
    top: '10px',
    textStyle: {
      color: '#888',
      fontWeight: 'normal',
      fontSize: 18,
    },
  },
  series: [
    {
      data: [30, 20, 35, 15],
      type: 'line',
    },
  ],
})
const BChart = ref()
const BOption = ref({
  xAxis: {
    type: 'category',
    data: ['1', '2', '3', '4'],
  },
  yAxis: {
    type: 'value',
  },
  title: {
    text: 'B Subscription count ',
    left: 'center',
    top: '10px',
    textStyle: {
      color: '#888',
      fontWeight: 'normal',
      fontSize: 18,
    },
  },
  series: [
    {
      data: [30, 20, 35, 15],
      type: 'line',
    },
  ],
})
const CChart = ref()
const COption = ref({
  xAxis: {
    type: 'category',
    data: ['1', '2', '3', '4'],
  },
  yAxis: {
    type: 'value',
  },
  title: {
    text: 'B Subscription count ',
    left: 'center',
    top: '10px',
    textStyle: {
      color: '#888',
      fontWeight: 'normal',
      fontSize: 18,
    },
  },
  series: [
    {
      data: [30, 20, 35, 15],
      type: 'line',
    },
  ],
})
const periodOptions = ref([
  { name: '3 days', code: '3' },
  { name: '7 days', code: '7' },
  { name: '14 days', code: '14' },
  { name: '30 days', code: '30' },
])
const selectedPeriod = ref({ name: '3 days', code: '3' })

const fetchData = () => {
  const params = {
    days: selectedPeriod.value.code,
  }
  getSubscription(params).then((res) => {
    const data = res.data
    console.log(data)
    // 在数据赋值前清空数组
    overview.value = []
    AOption.value.xAxis.data = []
    AOption.value.series[0].data = []

    BOption.value.xAxis.data = []
    BOption.value.series[0].data = []

    COption.value.xAxis.data = []
    COption.value.series[0].data = []

    Object.keys(data.summary).forEach((key: string) => {
      return overview.value.push(
        {
          plan: data?.summary[Number(key)]?.plan_name,
          customer: data.summary[Number(key)].active_plans,
          trend: data.summary[Number(key)].trend_plans,
          churnRate: data.summary[Number(key)].churned_plans / data.summary[Number(key)].total_plans,
          monthly: data.summary[Number(key)].mrr,
          annual: data.summary[Number(key)].arr,
        },
      )
    })

    const aText = data.trends.top_plans[0] ?? 'No Data'
    const bText = data.trends.top_plans[1] ?? 'No Data'
    const cText = data.trends.top_plans[2] ?? 'No Data'
    AOption.value.title.text = aText
    BOption.value.title.text = bText
    COption.value.title.text = cText

    if (aText !== 'No Data') {
      Object.keys(data.trends.daily_changes[aText]).forEach((key: string) => {
        AOption.value.xAxis.data.push(key)
        return AOption.value.series[0].data.push(data.trends.daily_changes[aText][key].active_plans)
      })
    }

    if (bText !== 'No Data') {
      Object.keys(data.trends.daily_changes[bText]).forEach((key: string) => {
        BOption.value.xAxis.data.push(key)
        return BOption.value.series[0].data.push(data.trends.daily_changes[bText][key].active_plans)
      })
    }

    if (cText !== 'No Data') {
      Object.keys(data.trends.daily_changes[cText]).forEach((key: string) => {
        COption.value.xAxis.data.push(key)
        return COption.value.series[0].data.push(data.trends.daily_changes[cText][key].active_plans)
      })
    }

    if (AOption.value) {
      AChart.value.setOption(AOption.value)
    }

    if (BOption.value) {
      BChart.value.setOption(BOption.value)
    }

    if (COption.value) {
      CChart.value.setOption(COption.value)
    }
  })
}

// 监听日期间隔变化
watch(selectedPeriod, () => {
  fetchData()
})

onMounted(() => {
  const AChartDom = document.getElementById('overviewA')
  const BChartDom = document.getElementById('overviewB')
  const CChartDom = document.getElementById('overviewC')
  if (AChartDom) {
    AChart.value = echarts.init(AChartDom)
    AOption.value && AChart.value.setOption(AOption.value)
  }
  if (BChartDom) {
    BChart.value = echarts.init(BChartDom)
    BOption.value && BChart.value.setOption(BOption.value)
  }
  if (CChartDom) {
    CChart.value = echarts.init(CChartDom)
    COption.value && CChart.value.setOption(COption.value)
  }

  Promise.all([
    fetchData(),
  ])
})
</script>

<template>
  <div>
    <div class="text-xl font-semibold">
      Subscription
    </div>
    <div>
      <div class="overview mt-4">
        <div class="title font-semibold text-lg mb-2">
          Subscription overview
        </div>
        <div>
          <DataTable :value="overview" show-gridlines table-style="min-width: 50rem">
            <Column field="plan" header="Active sub & plans" />
            <Column field="customer" header="customer" />
            <Column field="trend" header="Trend" />
            <Column field="churnRate" header="Churn Rate" />
            <Column field="monthly" header="Monthly Recurring Revenue" />
            <Column field="annual" header="Annual Recurring Revenue" />
          </DataTable>
        </div>
      </div>
      <div class="ranking mt-10">
        <div class="title font-semibold text-lg mb-2 relative">
          Top 3 subscription for last
          <div class=" absolute top-[-8px] left-70 z-100">
            <Select v-model="selectedPeriod" :options="periodOptions" option-label="name" class=" bg-transparent " />
          </div>
        </div>
        <div class="w-2xl h-[350px]">
          <div class="title font-semibold text-lg">
            {{ AOption.title.text }}
          </div>
          <div id="overviewA" style="width: 100%;height: 100%;" />
        </div>
        <div class="w-2xl h-[350px]">
          <div class="title font-semibold text-lg">
            {{ BOption.title.text }}
          </div>
          <div id="overviewB" style="width: 100%;height: 100%;" />
        </div>
        <div class="w-2xl h-[350px]">
          <div class="title font-semibold text-lg">
            {{ COption.title.text }}
          </div>
          <div id="overviewC" style="width: 100%;height: 100%;" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>
