<script setup lang="ts">
import EnhancedDatePicker from '@/components/common/EnhancedDatePicker.vue'
import { getRevenueDetail } from '@/services/api/report'
import { formatNumber, formatYAxis } from '@/utils/format'
import dayjs from 'dayjs'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from 'echarts/charts'
import { GraphicComponent, GridComponent, LegendComponent, TitleComponent, ToolboxComponent, TooltipComponent } from 'echarts/components'
import * as echarts from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { onMounted, ref } from 'vue'

echarts.use([TitleComponent, GridComponent, Bar<PERSON>hart, CanvasRenderer, GraphicComponent, LegendComponent, ToolboxComponent, LineChart, TooltipComponent])

const grossChart = ref()
const grossOption = ref({
  grid: {
    bottom: '50px',
    left: '80px',
    right: '80px',
    top: '15px',
  },
  color: ['#9373f6', '#dc9256'],
  legend: {
    left: '45px',
    bottom: 5,

  },
  xAxis: [
    {
      type: 'category',
      data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
      axisPointer: {
        type: 'shadow',
      },

    },
  ],
  yAxis: [
    {
      type: 'value',
      max: 250,
      interval: 50,
      axisLabel: {
        formatter: formatYAxis,
      },
    },
    {
      type: 'value',
      min: 0,
      max: 25,
      interval: 5,
      axisLabel: {
        formatter: '{value} %',
      },
    },
  ],
  series: [
    {
      name: 'Gross volume',
      type: 'bar',
      data: [
        2.0,
        4.9,
        7.0,
        23.2,
        25.6,
        76.7,
        135.6,
        162.2,
        32.6,
        20.0,
        6.4,
        3.3,
      ],
    },
    {
      name: 'Year on year',
      type: 'line',
      yAxisIndex: 1,

      data: [2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5, 12.0, 6.2],
    },
  ],

})

const monthlyList = ref([
  {
    header: 'Month',
    grossVolume: 'Gross volume',
    yearOnYear: 'Year on year',
  },
])

const fetchData = () => {
  getRevenueDetail().then((res) => {
    const data = res.data
    volume.value = `$${formatNumber(data.total.payment_amount)}`
    grossOption.value.series[0].data = Object.values(data.monthly).map((item) => {
      return Number(item.payment_amount)
    })
    grossOption.value.series[1].data = Object.values(data.yoy.monthly).map((item) => {
      return Number(item.payment_amount)
    })

    grossOption.value.xAxis[0].data = Object.keys(data.monthly).map((item) => {
      return item
    })

    const maxPayment = Math.max(...Object.values(data.monthly).map(item => item.payment_amount))
    const maxYoy = Math.max(...Object.values(data.yoy.monthly).map(item => item.payment_amount))
    const { max, interval } = calculateAxisRange(maxPayment)
    grossOption.value.yAxis[0] = {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => formatYAxis(value),
      },
      max,
      interval,
    }

    grossOption.value.yAxis[1] = {
      type: 'value',
      min: 0,
      max: calculateAxisRange(maxYoy).max,
      interval: calculateAxisRange(maxYoy).interval,
      axisLabel: {
        formatter: '{value} %',
      },
    }

    Object.keys(data.monthly).forEach((key: string) => {
      monthlyList.value.push(
        {
          header: key,
          grossVolume: `$${formatNumber(data.monthly[key].payment_amount)}`,
          yearOnYear: `${data.yoy.monthly[key].payment_amount}%`,
        },
      )
    })

    if (grossChart.value) {
      grossChart.value.setOption(grossOption.value)
    }
  })
}

function calculateAxisRange(maxValue: number) {
  const exponent = Math.floor(Math.log10(maxValue))
  const magnitude = 10 ** exponent
  const max = Math.ceil(maxValue / magnitude) * magnitude
  const interval = max / 5

  return { max, interval }
}

onMounted(() => {
  const amount = document.getElementById('amount')
  if (amount) {
    grossChart.value = echarts.init(amount)
    grossOption.value && grossChart.value.setOption(grossOption.value)
  }

  Promise.all([
    fetchData(),
  ])
})
const selectDate = ref(new Date())
const checked = ref()
const comment = ref('')
const volume = ref('$0')
const update = ref(0)
</script>

<template>
  <div>
    <div class="m-2 w-4/5 flex justify-end px-10">
      <!-- <Button label="Export" variant="text" raised /> -->
    </div>
    <div class="px-6 rounded-lg bg-white flex">
      <div class="chart w-4/5 border-r-1 border-gray-300">
        <div class="px-10 py-8 text-2xl flex justify-between items-center">
          <div>
            <span class="font-semibold mr-4">Gross Transaction Value ($)</span>
            <span>{{ volume }}</span>
          </div>
          <div class="text-base">
            Last updated {{ update }} hours ago
          </div>
        </div>
        <div class="chart-content w-full h-[500px]">
          <div id="amount" style="width: 100%;height: 100%;" />
        </div>
        <div class="mt-6 px-10">
          <DataTable :value="monthlyList">
            <Column field="header" style="min-width: 200px" />
            <Column field="grossVolume" style="min-width: 200px" />
            <Column field="yearOnYear" style="min-width: 200px" />
          </DataTable>
        </div>
        <div class="mt-10 px-10">
          <div class="font-semibold mb-2">
            How is this calculated?
          </div>
          <div class="text-[#777] mb-2 leading-6">
            Gross volume is the total amount of money received from all payment transactions, The data here
            is based on your default currency AUD.
          </div>
        </div>
      </div>
      <div class="explore w-1/5 p-8" hidden>
        <div class="font-semibold mb-8 text-lg">
          Explore
        </div>
        <div class="filter">
          <div class="filter-item">
            <div class="filter-name">
              Time period
            </div>
            <div>
              <EnhancedDatePicker v-model="selectDate" :min-date="dayjs().toDate()" />
            </div>
          </div>
          <div class="filter-item">
            <div class="filter-name">
              Time interval
            </div>
            <div>
              <EnhancedDatePicker v-model="selectDate" :min-date="dayjs().toDate()" />
            </div>
          </div>
          <div class="filter-item">
            <div class="filter-name">
              Group by
            </div>
            <div>
              <Button label="Add group" icon="pi pi-plus" variant="text" />
            </div>
          </div>
          <div class="filter-item">
            <div class="filter-name">
              Filter by
            </div>
            <div>
              <Button label="Add filter" icon="pi pi-plus" variant="text" />
            </div>
          </div>
          <div class="filter-item">
            <div class="filter-name">
              Percent change
            </div>
            <div>
              <Checkbox v-model="checked" input-id="year" name="checked" value="Year-over-year" />
              <label for="year"> Year-over-year </label>
            </div>
          </div>
        </div>
        <div class="feedback mt-32 ">
          <div class="p-2 mb-4">
            Do you have any feedback on this metric?
          </div>
          <div>
            <Textarea v-model="comment" rows="5" cols="25" />
            <Button label="Share feedback" severity="help" variant="text" icon="pi pi-send" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.filter-item{
    margin-bottom: 20px;
}
.filter-name {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
}
:deep(.p-checkbox-checked .p-checkbox-box){
    background-color: #0d6efd;
    border-color: #0d6efd;
}
.feedback{
    border-top: 1px solid #ccc;
}
</style>
