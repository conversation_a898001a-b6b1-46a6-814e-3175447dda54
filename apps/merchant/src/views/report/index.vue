<script setup lang="ts">
import { ref, shallowRef } from 'vue'
import revenueOverview from './components/revenue-overview.vue'
import subscription from './components/subscription.vue'
import transactionPayout from './components/transaction-payout.vue'

const selectedReport = ref('revenue-overview')

const changeReport = (report: string) => {
  selectedReport.value = report
}

const tabList = ref([
  {
    keyName: 'revenue-overview',
    label: 'Revenue Overview',
    component: shallowRef(revenueOverview),
  },
  {
    keyName: 'transaction-payout',
    label: 'Transaction & Payout',
    component: shallowRef(transactionPayout),
  },
  {
    keyName: 'subscription',
    label: 'Subscription',
    component: shallowRef(subscription),
  },
])
const getSelectedComponent = () => {
  const selectedTab = tabList.value.find(tab => tab.keyName === selectedReport.value)
  return selectedTab ? selectedTab.component : null
}
</script>

<template>
  <div class="report-container px-6 rounded-lg bg-white">
    <!-- 左侧报告侧边栏 -->
    <div class="report-sidebar w-2xs">
      <div class="text-3xl m-4">
        Report side bard
      </div>
      <!-- {{ selectedReport }} -->
      <div class="m-6 text-xl">
        <div v-for="tab in tabList" :key="tab.keyName" class="mb-6">
          <div class="cursor-pointer hover:text-[#aaa] " :class="{ 'text-[#a17eff]': tab.keyName === selectedReport }" @click="changeReport(tab.keyName)">
            {{ tab.label }}
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧视图区域 -->
    <div class="blank-page p-4 flex-1">
      <component :is="getSelectedComponent()" />
    </div>
  </div>
</template>

<style scoped>
.report-container {
  display: flex;

}
</style>
