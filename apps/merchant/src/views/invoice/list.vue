<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import { Format } from '@shared'
import { useToast } from 'primevue/usetoast'
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useListRefresh } from '@/composables/useListRefresh'
import { useRequestList } from '@/composables/useRequestList'
import { SearchFieldType } from '@/constants/search'
import { invoice as invoiceApi } from '@/services/api'
import { formatDate } from '@/utils/date'

defineOptions({
  name: 'invoiceList',
})

const { t } = useI18n()
const toast = useToast()

// Column configuration
const columns = ref<TableColumnItem[]>([
  { field: 'id', header: 'ID', style: { width: '60px' } },
  { field: 'invoice_number', header: 'Invoice Number', style: { minWidth: '140px' } },
  { field: 'date', header: 'Issue Date', style: { minWidth: '120px' }, template: 'issue_date' },
  { field: 'due_date', header: 'Due Date', style: { minWidth: '120px' }, template: 'due_date' },
  { field: 'sub_total', header: 'Subtotal', style: { minWidth: '100px' }, template: 'sub_total' },
  { field: 'total_tax', header: 'Tax', style: { minWidth: '100px' }, template: 'total_tax' },
  { field: 'amount_due', header: 'Amount Due', style: { minWidth: '120px' }, template: 'amount_due' },
  { field: 'amount_paid', header: 'Amount Paid', style: { minWidth: '120px' }, template: 'amount_paid' },
  { field: 'status', header: 'Status', style: { minWidth: '120px' }, template: 'status' },
  { field: 'created_at', header: t('common.created', 'Created Date'), template: 'created_at', sortable: true, style: { minWidth: '150px' } },
  { field: 'action', header: '', template: 'action', style: { width: '50px' }, alignFrozen: 'right', frozen: true },
])

const {
  list,
  loading,
  total,
  refresh,
  search,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
  setSearchParams,
} = useRequestList<Invoice.Info[], Api.InvoiceListReq>({
  requestFn: invoiceApi.getInvoiceList,
})

// Use common list refresh logic
useListRefresh('invoiceList', refresh)

// Get status severity
const getStatusSeverity = (status: number | string | undefined) => {
  if (!status) { return 'upcoming' }

  const severityMap: Record<string, string> = {
    AUTHORISED: 'info',
    PAID: 'paid',
    VOIDED: 'failed',
    DRAFT: 'upcoming',
  }

  return severityMap[status] || 'upcoming'
}

const handleSort = (event: any) => {
  const { sortField, sortOrder } = event
  setSearchParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  search()
}

// Search model
const searchModel = ref<Partial<Api.InvoiceListReq>>({
  invoice_number: '',
})

// Search fields configuration
const searchFields = computed(() => [
  {
    name: 'invoice_number',
    label: 'Invoice Number',
    type: SearchFieldType.TEXT,
    placeholder: 'Search by invoice number',
    maxlength: 50,
    defaultValue: '',
  },
])

// View invoice details dialog control
const invoiceDetailDialog = ref(false)

// Invoice detail data
const invoiceDetail = ref<Invoice.Info | null>(null)

// Open invoice detail dialog
const openInvoiceDetailDialog = (data: Invoice.Info) => {
  invoiceApi.getInvoiceDetail({ id: data.id }).then((res: any) => {
    if (res.code === 0) {
      invoiceDetail.value = res.data
      invoiceDetailDialog.value = true
    }
  }).catch(() => {
    toast.add({ severity: 'error', summary: 'Error', detail: 'Failed to load invoice details', life: 3000 })
  })
}

// Search handler
const handleSearch = () => {
  setSearchParams(searchModel.value)
  search()
}
</script>

<template>
  <div class="invoice-page">
    <!-- Search bar -->
    <BaseSearch
      v-model="searchModel"
      :loading="loading"
      :basic-search-fields="searchFields"
      @search="handleSearch"
    />

    <!-- Invoices table -->
    <BaseDataTable
      :show-search-bar="false"
      :value="list" :columns="columns" :scrollable="true" :show-multiple-column="false"
      :loading="loading" :paginator="true" :rows="20" :total-records="total" :lazy="true" data-key="id"
      :failed="failed" :failure-message="failureMessage" :striped-rows="true"
      search-placeholder="Search invoices..."
      @page="(e: DataTablePageEvent) => handlePageChange(e)"
      @sort="handleSort"
    >
      <template #issue_date="{ data }">
        {{ formatDate(data.date) }}
      </template>
      <template #due_date="{ data }">
        {{ formatDate(data.due_date) }}
      </template>
      <template #sub_total="{ data }">
        {{ Format.formatAmount(data.sub_total) }}
      </template>
      <template #total_tax="{ data }">
        {{ Format.formatAmount(data.total_tax) }}
      </template>
      <template #amount_due="{ data }">
        {{ Format.formatAmount(data.amount_due) }}
      </template>
      <template #amount_paid="{ data }">
        {{ Format.formatAmount(data.amount_paid) }}
      </template>
      <template #status="{ data }">
        <BaseTag :text="data.status" :type="getStatusSeverity(data.status)" />
      </template>
      <template #created_at="{ data }">
        {{ formatDate(data.created_at) }}
      </template>
      <template #action="{ data }">
        <BaseDataTableActions content-width="50px">
          <template #default>
            <Button icon="pi pi-eye" text title="View Details" @click="openInvoiceDetailDialog(data)" />
          </template>
        </BaseDataTableActions>
      </template>
    </BaseDataTable>

    <!-- Invoice details dialog -->
    <Dialog
      v-model:visible="invoiceDetailDialog"
      modal
      header="Invoice Details"
      :style="{ width: '800px' }"
      :closable="true"
    >
      <div v-if="invoiceDetail" class="flex flex-col gap-4">
        <div class="border-b pb-4 mb-4">
          <h2 class="text-xl font-bold mb-3">
            Invoice {{ invoiceDetail.invoice_number }}
          </h2>
          <div class="flex justify-between items-start">
            <div class="flex items-center">
              <span class="mr-2">Status:</span>
              <BaseTag :text="invoiceDetail.status" :type="getStatusSeverity(invoiceDetail.status)" />
            </div>
            <div class="text-right">
              <div class="mb-1">
                <span class="text-gray-600 mr-2">Issue Date:</span>
                <!-- <span class="font-medium">{{ formatDate(invoiceDetail.date) }}</span> -->
              </div>
              <div>
                <span class="text-gray-600 mr-2">Due Date:</span>
                <span class="font-medium">{{ formatDate(invoiceDetail.due_date) }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-2 gap-6 mb-6">
          <div>
            <h3 class="text-gray-600 text-sm mb-1">
              Invoice Number
            </h3>
            <p class="font-medium">
              {{ invoiceDetail.invoice_number }}
            </p>
          </div>
          <div>
            <h3 class="text-gray-600 text-sm mb-1">
              Contact ID
            </h3>
            <p class="font-mono text-sm break-all">
              <!-- {{ invoiceDetail.contact_id }} -->
            </p>
          </div>
          <div>
            <h3 class="text-gray-600 text-sm mb-1">
              Type
            </h3>
            <p class="font-medium">
              <!-- {{ invoiceDetail.type }} -->
            </p>
          </div>
          <div>
            <h3 class="text-gray-600 text-sm mb-1">
              Currency
            </h3>
            <p class="font-medium">
              {{ invoiceDetail.currency }}
            </p>
          </div>
        </div>

        <div class="border-t border-gray-200 pt-4 mb-6">
          <h3 class="font-bold text-lg mb-3">
            Financial Details
          </h3>
          <div class="grid grid-cols-2 gap-6">
            <div>
              <h3 class="text-gray-600 text-sm mb-1">
                Subtotal
              </h3>
              <p class="font-medium">
                {{ Format.formatAmount(invoiceDetail.sub_total) }}
              </p>
            </div>
            <div>
              <h3 class="text-gray-600 text-sm mb-1">
                Total Tax
              </h3>
              <p class="font-medium">
                <!-- {{ formatAmount(invoiceDetail.total_tax, invoiceDetail.currency) }} -->
              </p>
            </div>
            <div>
              <h3 class="text-gray-600 text-sm mb-1">
                Amount Due
              </h3>
              <p class="text-red-500 font-bold">
                <!-- {{ formatAmount(invoiceDetail.amount_due, invoiceDetail.currency) }} -->
              </p>
            </div>
            <div>
              <h3 class="text-gray-600 text-sm mb-1">
                Amount Paid
              </h3>
              <p class="text-green-500 font-bold">
                <!-- {{ formatAmount(invoiceDetail.amount_paid, invoiceDetail.currency) }} -->
              </p>
            </div>
          </div>
        </div>

        <div class="border-t border-gray-200 pt-4">
          <h3 class="font-bold text-lg mb-3">
            System Information
          </h3>
          <div class="grid grid-cols-2 gap-6 mb-3">
            <div>
              <h3 class="text-gray-600 text-sm mb-1">
                Invoice ID
              </h3>
              <p class="font-mono text-sm break-all">
                <!-- {{ invoiceDetail.invoice_id }} -->
              </p>
            </div>
            <div>
              <h3 class="text-gray-600 text-sm mb-1">
                Tenant ID
              </h3>
              <p class="font-mono text-sm break-all">
                <!-- {{ invoiceDetail.tenant_id }} -->
              </p>
            </div>
          </div>
          <div class="grid grid-cols-2 gap-6">
            <div>
              <h3 class="text-gray-600 text-sm mb-1">
                Created Date
              </h3>
              <div>{{ formatDate(invoiceDetail.created_at) }}</div>
            </div>
            <div>
              <h3 class="text-gray-600 text-sm mb-1">
                Updated Date
              </h3>
              <!-- <div>{{ formatDate(invoiceDetail.updated_at) }}</div> -->
            </div>
          </div>
        </div>

        <div class="flex justify-end gap-2 mt-6 pt-4 border-t">
          <Button type="button" label="Close" class="p-button-primary" @click="invoiceDetailDialog = false" />
        </div>
      </div>
    </Dialog>
  </div>
</template>

<style scoped>
.invoice-page {
  padding: 1rem;
}
</style>
