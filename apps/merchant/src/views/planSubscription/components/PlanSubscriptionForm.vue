<script setup lang="ts">
import dayjs from 'dayjs'
import { ref, watch } from 'vue'
import EnhancedDatePicker from '@/components/common/EnhancedDatePicker.vue'
import { INFINITE } from '@/constants/customer'
import { BillingPeriodCustomType, BillingPeriodType, PlanEndDateType, ScheduleType, UnitBasedModelUnitType, UnitBasedPricingModel } from '@/constants/plan'
import { useUserStore } from '@/store/modules/user'

defineOptions({
  name: 'PlanSubscriptionForm',
})

// 定义组件属性
const props = defineProps({
  formData: {
    type: Object,
    required: true,
  },
  formErrors: {
    type: Object,
    required: true,
  },
  options: {
    type: Object,
    default: () => ({
      status: [],
      process_type: [],
      currency: [],
      billing_period: [],
      billing_period_custom_unit: [],
      one_off_pricing_model: [],
      recurringTieredPaymentMethod: [],
    }),
  },
  optionsLoading: {
    type: Function,
    default: () => {
      return () => false
    },
  },
  submitting: {
    type: Boolean,
    default: false,
  },
  mode: {
    type: String,
    default: 'add',
    validator: (value: string) => ['add', 'edit', 'view'].includes(value),
  },
})

// 定义事件
const emit = defineEmits(['submit', 'cancel'])

const formRef = ref<HTMLFormElement | null>(null)

const { user } = useUserStore()

// 提交表单
const handleSubmit = (event: Event) => {
  emit('submit', event)
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
}

// 添加层级
const handleAddTier = () => {
  const lastIndex = props.formData.unitBasedTieredPricing.length - 1

  if (lastIndex === 1) {
    props.formData.unitBasedTieredPricing[1].lastUnit = Number(props.formData.unitBasedTieredPricing[1].firstUnit + 1)
    props.formData.unitBasedTieredPricing.push({
      firstUnit: props.formData.unitBasedTieredPricing[1].lastUnit + 1,
      lastUnit: INFINITE,
      perUnit: 0,
      flatFee: 0,
    })
  }
  else {
    props.formData.unitBasedTieredPricing[lastIndex].lastUnit = Number(props.formData.unitBasedTieredPricing[lastIndex].firstUnit + 1)
    props.formData.unitBasedTieredPricing.push({
      firstUnit: props.formData.unitBasedTieredPricing[lastIndex].lastUnit + 1,
      lastUnit: INFINITE,
      perUnit: 0,
      flatFee: 0,
    })
  }
}

// 处理最后一个单位变化
const handleLastUnitChange = (index: number, value: number | null) => {
  if (index < props.formData.unitBasedTieredPricing.length - 1 && value !== null) {
    // 验证输入值不能小于 firstUnit
    const currentFirstUnit = props.formData.unitBasedTieredPricing[index].firstUnit
    if (value < currentFirstUnit) {
      props.formData.unitBasedTieredPricing[index].lastUnit = currentFirstUnit
      return
    }

    // 更新下一行的 firstUnit,设置为当前行的 lastUnit + 1
    props.formData.unitBasedTieredPricing[index + 1].firstUnit = value + 1
  }
}

// 处理删除层级
const handleRemoveTier = (index: number) => {
  if (index > 0 && index < props.formData.unitBasedTieredPricing.length) {
    props.formData.unitBasedTieredPricing.splice(index, 1)
    // 更新后续行的 firstUnit
    for (let i = index; i < props.formData.unitBasedTieredPricing.length; i++) {
      if (i === 0) {
        props.formData.unitBasedTieredPricing[i].firstUnit = 1
      }
      else {
        if (!Number.isNaN(Number(props.formData.unitBasedTieredPricing[i - 1].lastUnit) + 1)) {
          props.formData.unitBasedTieredPricing[i].firstUnit = Number(props.formData.unitBasedTieredPricing[i - 1].lastUnit) + 1
        }
      }
    }

    // 确保删除后的最后一行 lastUnit 为 INFINITE
    const lastIndex = props.formData.unitBasedTieredPricing.length - 1
    if (lastIndex >= 0 && props.formData.unitBasedTieredPricing[lastIndex].lastUnit) {
      props.formData.unitBasedTieredPricing[lastIndex].lastUnit = INFINITE
    }
  }
}

// 监听 moreRecurringTieredPricing 变化
watch(() => props.formData.moreBasedTieredTieredPricing, (newValue) => {
  if (newValue.length > 0) {
    // 确保第一行的 firstUnit 始终为 1
    newValue[0].firstUnit = 1

    // 确保最后一行的 lastUnit 始终为 INFINITE
    const lastIndex = newValue.length - 1
    if (lastIndex >= 0) {
      newValue[lastIndex].lastUnit = INFINITE
    }
  }
}, { deep: true })
</script>

<template>
  <div class="plan-subscription-form flex-1" :class="{ 'p-6': mode !== 'view' }">
    <form ref="formRef" class="flex flex-col min-h-48 max-w-260" @submit.prevent="handleSubmit">
      <!-- 计划名称 -->
      <div class="field mb-4">
        <label for="plan_name" class="block">Plan Name(Required)</label>
        <p class="field-description">
          Name of the product or service, visible to customers.
        </p>
        <InputText
          v-model="props.formData.plan_name" type="text" class="w-full"
          :class="{ 'p-invalid': props.formErrors.plan_name }" :disabled="props.submitting" maxlength="100"
        />
        <Message v-if="props.formErrors.plan_name" class="mt-2" severity="error" size="small" variant="simple">
          {{ props.formErrors.plan_name }}
        </Message>
      </div>

      <!-- 计划描述 -->
      <div class="field mb-4">
        <label for="description" class="block">Plan Description</label>
        <p class="field-description">
          Appears at checkout, on the customer portal, and in quotes.
        </p>
        <Textarea
          v-model="props.formData.description" class="w-full"
          :class="{ 'p-invalid': props.formErrors.description }" :disabled="props.submitting" rows="3" cols="30"
          maxlength="500" auto-resize
        />
      </div>

      <div class="field mb-4">
        <div class="flex gap-2">
          <Checkbox v-model="props.formData.is_inclusive_gst" binary :disabled="props.submitting" />
          <label for="is_inclusive_gst" class="block mb-2">GST Inclusive</label>
        </div>
        <Message v-if="props.formErrors.is_inclusive_gst" class="mt-2" severity="error" size="small" variant="simple">
          {{ props.formErrors.is_inclusive_gst }}
        </Message>
      </div>

      <div class="field mb-4">
        <div class="flex gap-2">
          <Checkbox v-model="props.formData.is_surcharge" binary :disabled="props.submitting" />
          <label for="is_surcharge" class="block mb-2">Surcharge</label>
        </div>
      </div>

      <!-- 费用类型 -->
      <div class="field mb-4 mt-4">
        <div class="flex gap-4">
          <div
            class="fee-type-option flex-1 p-4 border rounded-lg cursor-pointer transition-all duration-200"
            :class="{ selected: props.formData.schedule_type === ScheduleType.Recurring }"
            @click="props.formData.schedule_type = ScheduleType.Recurring"
          >
            Recurring
          </div>
          <!-- BBP15-177 Disable Unit-based for now. It has some logic issue. Just hide it instead of deleting this, we will improve this later. -->
          <!-- <div
            class="fee-type-option flex-1 p-4 border rounded-lg cursor-pointer transition-all duration-200"
            :class="{ selected: props.formData.schedule_type === ScheduleType.UnitBased }"
            @click="props.formData.schedule_type = ScheduleType.UnitBased"
          >
            Unit-based
          </div> -->
          <div
            class="fee-type-option flex-1 p-4 border rounded-lg cursor-pointer transition-all duration-200"
            :class="{ selected: props.formData.schedule_type === ScheduleType.OneOff }"
            @click="props.formData.schedule_type = ScheduleType.OneOff"
          >
            One-off
          </div>
        </div>
      </div>

      <!-- 规则 1 Recurring -->
      <template v-if="props.formData.schedule_type === ScheduleType.Recurring">
        <div class="field">
          <label class="block mb-2">Amount</label>
          <InputGroup>
            <InputNumber
              v-model="props.formData.recurringAmount" class="w-full" :disabled="props.submitting"
              :min-fraction-digits="2" :max-fraction-digits="5"
              mode="currency" currency="AUD" locale="en-AU"
            />
          </InputGroup>
          <Message v-if="props.formErrors.recurringAmount" class="mt-2" severity="error" size="small" variant="simple">
            {{ props.formErrors.recurringAmount }}
          </Message>
        </div>
        <div class="field ">
          <label class="block mb-2">Billing period</label>
          <Select
            v-model="props.formData.recurringBillingPeriod" :options="props.options.billing_period"
            option-label="label" option-value="value" class="w-full" :disabled="props.submitting"
          />
          <Message
            v-if="props.formErrors.recurringBillingPeriod" class="mt-2" severity="error" size="small"
            variant="simple"
          >
            {{ props.formErrors.recurringBillingPeriod }}
          </Message>
        </div>
        <template v-if="props.formData.recurringBillingPeriod === BillingPeriodType.Custom">
          <div class="field mb-4">
            <div class="flex items-center gap-2 ">
              <span class="w-10">
                every
              </span>
              <div class="flex-1 flex flex-col">
                <InputNumber
                  v-model="props.formData.recurringBillingPeriodCustom" v-tooltip.top="props.formData.recurringBillingPeriodCustomUnit === BillingPeriodCustomType.Day ? 'Enter a number between 7 and 365.' : ''"
                  class="w-full"
                  :disabled="props.submitting"
                  :input-class="{
                    'p-invalid': props.formErrors.recurringBillingPeriodCustom,
                  }"
                />
              </div>
              <div class="flex-1">
                <Select
                  v-model="props.formData.recurringBillingPeriodCustomUnit"
                  :options="props.options.billing_period_custom_unit" option-label="label" option-value="value"
                  class="w-full" :disabled="props.submitting"
                  :invalid="!!props.formErrors.recurringBillingPeriodCustom"
                />
              </div>
            </div>
            <Message
              v-if="props.formErrors.recurringBillingPeriodCustom" class="mt-2" severity="error" size="small"
              variant="simple"
            >
              {{ props.formErrors.recurringBillingPeriodCustom }}
            </Message>
          </div>
        </template>

        <div class="field mb-4">
          <label for="end_date" class="block mb-4">Recurrence Settings</label>
          <div class="flex gap-6 items-center mb-4">
            <div v-for="category in props.options.endDateType" :key="category.value" class="flex items-center gap-2">
              <RadioButton
                v-model="props.formData.end_date_type" :input-id="String(category.value)"
                :disabled="props.submitting" name="end_date_type" :value="category.value"
              />
              <label :for="category.value">{{ category.label }}</label>
            </div>
          </div>
        </div>

        <div v-if="props.formData.end_date_type === PlanEndDateType.SpecifyByEndDate" class="field mb-4">
          <label for="end_date" class="block mb-4">End date(Required)</label>
          <div class="flex gap-2 items-center">
            <EnhancedDatePicker
              v-model="props.formData.end_date" :disabled="props.submitting"
              :min-date="dayjs().toDate()"
            />
          </div>
          <Message v-if="props.formErrors.end_date" class="mt-2" severity="error" size="small" variant="simple">
            {{ props.formErrors.end_date }}
          </Message>
        </div>

        <div v-if="props.formData.end_date_type === PlanEndDateType.SpecifyByTerm" class="field mb-4">
          <label for="end_terms" class="block mb-4">End terms (Required)</label>
          <div class="flex gap-2 items-center">
            <InputNumber v-model="props.formData.end_terms" :disabled="props.submitting" :min="1" :max="9999" />
          </div>
          <Message v-if="props.formErrors.end_terms" class="mt-2" severity="error" size="small" variant="simple">
            {{ props.formErrors.end_terms }}
          </Message>
        </div>
      </template>

      <!-- 规则 2 Unit-based -->
      <template v-if="props.formData.schedule_type === ScheduleType.UnitBased">
        <div class="field mb-2">
          <label class="block mb-2">Usage Type</label>
          <Select
            v-model="props.formData.unitBasedModel" :options="props.options.unit_based_pricing_model"
            option-label="label" option-value="value" class="w-full" :disabled="props.submitting"
          />
          <Message v-if="props.formErrors.unitBasedModel" class="mt-2" severity="error" size="small" variant="simple">
            {{ props.formErrors.unitBasedModel }}
          </Message>
        </div>
        <div v-if="props.formData.unitBasedModel === UnitBasedPricingModel.StandardPricing" class="field mb-2">
          <label class="block mb-2">Unit type</label>
          <Select
            v-model="props.formData.unitBasedModelType" :options="props.options.unit_based_model_type"
            option-label="label" option-value="value" class="w-full" :disabled="props.submitting"
          />
          <InputText
            v-if="props.formData.unitBasedModelType === UnitBasedModelUnitType.Custom"
            v-model="props.formData.unitBasedModelTypeCustom" class="w-full !mt-4" placeholder="Enter custom unit type"
            :maxlength="50"
          />
          <Message
            v-if="props.formErrors.unitBasedModelTypeCustom" class="mt-2" severity="error" size="small"
            variant="simple"
          >
            {{ props.formErrors.unitBasedModelTypeCustom }}
          </Message>
        </div>
        <div v-if="props.formData.unitBasedModel === UnitBasedPricingModel.TieredPricing" class="field mb-2">
          <label class="block mb-2">Unit type</label>
          <Select
            v-model="props.formData.unitBasedTieredModelType" :options="props.options.unit_based_model_type"
            option-label="label" option-value="value" class="w-full" :disabled="props.submitting"
          />
          <InputText
            v-if="props.formData.unitBasedTieredModelType === UnitBasedModelUnitType.Custom"
            v-model="props.formData.unitBasedTieredModelTypeCustom" class="w-full !mt-4"
            placeholder="Enter custom unit type" :maxlength="50"
          />
          <Message
            v-if="props.formErrors.unitBasedTieredModelTypeCustom" class="mt-2" severity="error" size="small"
            variant="simple"
          >
            {{ props.formErrors.unitBasedTieredModelTypeCustom }}
          </Message>
        </div>
        <template v-if="props.formData.unitBasedModel === UnitBasedPricingModel.StandardPricing">
          <div class="field">
            <label class="block mb-2">Flat Rate per Unit:</label>
            <InputGroup>
              <InputNumber
                v-model="props.formData.unitBasedAmount" class="w-full" :disabled="props.submitting"
                :min-fraction-digits="2" :max-fraction-digits="5"
                mode="currency" currency="AUD" locale="en-AU"
              />
              <!-- <InputGroupAddon>
                <Select
                  v-model="props.formData.unitBasedCurrency" class="!border-0 !shadow-none w-56"
                  :options="props.options.currency" option-label="label" option-value="value"
                />
              </InputGroupAddon> -->
            </InputGroup>
            <Message
              v-if="props.formErrors.unitBasedAmount" class="mt-2" severity="error" size="small"
              variant="simple"
            >
              {{ props.formErrors.unitBasedAmount }}
            </Message>
          </div>
          <div class="field">
            <label class="block mb-2">Increment:</label>
            <InputGroup class="increment-group">
              <InputNumber
                v-model="props.formData.unitBasedIncrement" :min="0.1" :max="9999" :min-fraction-digits="1"
                :max-fraction-digits="1" class="w-full" :disabled="props.submitting"
              />
              <InputGroupAddon
                v-if="(props.formData.unitBasedModelType !== UnitBasedModelUnitType.Custom
                  || props.formData.unitBasedModelTypeCustom !== ''
                )"
              >
                {{ props.formData.unitBasedModelType === UnitBasedModelUnitType.Custom ? props.formData.unitBasedModelTypeCustom : props.formData.unitBasedModelType }}
              </InputGroupAddon>
            </InputGroup>
            <Message
              v-if="props.formErrors.unitBasedIncrement" class="mt-2" severity="error" size="small"
              variant="simple"
            >
              {{ props.formErrors.unitBasedIncrement }}
            </Message>
          </div>
        </template>
        <template v-if="props.formData.unitBasedModel === UnitBasedPricingModel.TieredPricing">
          <!-- <div class="field">
            <Select
              v-model="props.formData.unitBasedTieredCurrency" class="w-full"
              :options="props.options.currency" option-label="label" option-value="value"
            />
            <Message
              v-if="props.formErrors.unitBasedTieredCurrency" class="mt-2" severity="error" size="small"
              variant="simple"
            >
              {{ props.formErrors.unitBasedTieredCurrency }}
            </Message>
          </div> -->

          <div class="field">
            <Select
              v-model="props.formData.unitBasedTieredCurrencyPaymentMethod" class="w-full"
              :options="props.options.unit_based_tiered_payment_method" option-label="label" option-value="value"
            />
            <Message
              v-if="props.formErrors.unitBasedTieredCurrencyPaymentMethod" class="mt-2" severity="error"
              size="small" variant="simple"
            >
              {{ props.formErrors.unitBasedTieredCurrencyPaymentMethod }}
            </Message>
          </div>

          <!-- Tiered Pricing Table -->
          <div class="field mt-4">
            <div class="tiered-pricing-table">
              <table class="w-full table-fixed">
                <thead>
                  <tr>
                    <th class="text-left font-medium pb-2" style="width: 20%">
                      {{ $t('planSubscription.tieredPricing.firstUnit') }}
                    </th>
                    <th class="text-left font-medium pb-2" style="width: 20%">
                      {{ $t('planSubscription.tieredPricing.lastUnit') }}
                    </th>
                    <th class="text-left font-medium pb-2" style="width: 25%">
                      {{ $t('planSubscription.tieredPricing.perUnit') }}
                    </th>
                    <th class="text-left font-medium pb-2" style="width: 25%">
                      {{ $t('planSubscription.tieredPricing.flatFee') }}
                    </th>
                    <th style="width: 10%" />
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(tier, index) in props.formData.unitBasedTieredPricing" :key="index" class="mb-3">
                    <td class="pr-2 pb-3">
                      <InputNumber
                        v-model="tier.firstUnit" class="w-full" :min="0" :disabled="true"
                        :show-buttons="false"
                        :value="index === 0 ? 1 : Number(props.formData.unitBasedTieredPricing[index - 1].lastUnit) + 1"
                        readonly :input-style="{ width: '100%' }"
                      />
                    </td>
                    <td class="pr-2 pb-3">
                      <template v-if="index === props.formData.unitBasedTieredPricing.length - 1">
                        <InputText
                          v-model="tier.lastUnit" class="w-full infinity-input" :placeholder="INFINITE"
                          disabled readonly
                        />
                      </template>
                      <template v-else>
                        <InputNumber
                          v-model="tier.lastUnit" class="w-full" :min="tier.firstUnit"
                          :disabled="props.submitting" :show-buttons="false" :input-style="{ width: '100%' }"
                          @update:model-value="handleLastUnitChange(index, $event)"
                        />
                      </template>
                    </td>
                    <td class="pr-2 pb-3">
                      <InputNumber
                        v-model="tier.perUnit" class="w-full" :min="0" :disabled="props.submitting"
                        :show-buttons="false" :input-style="{ width: '100%' }" :min-fraction-digits="2"
                        :max-fraction-digits="5"
                        mode="currency" currency="AUD" locale="en-AU"
                      />
                    </td>
                    <td class="pb-3">
                      <InputNumber
                        v-model="tier.flatFee" class="w-full" :min="0" :disabled="props.submitting"
                        :show-buttons="false" :input-style="{ width: '100%' }" :min-fraction-digits="2"
                        :max-fraction-digits="5"
                        mode="currency" currency="AUD" locale="en-AU"
                      />
                    </td>
                    <td class="pb-3 text-center">
                      <Button
                        v-if="index !== 0" icon="pi pi-times" class="p-button-text p-button-danger"
                        :disabled="props.submitting" @click="handleRemoveTier(index)"
                      />
                    </td>
                  </tr>
                </tbody>
              </table>
              <div class="flex">
                <Button
                  :label="$t('planSubscription.tieredPricing.addAnotherTier')" icon="pi pi-plus"
                  class="p-button-text p-button-primary mt-2" :disabled="props.submitting" @click="handleAddTier"
                />
              </div>
            </div>
            <!-- 显示错误消息 -->
            <Message
              v-if="props.formErrors.moreRecurringTieredPricing" class="mt-2" severity="error" size="small"
              variant="simple"
            >
              {{ props.formErrors.moreRecurringTieredPricing }}
            </Message>
          </div>
        </template>
        <template v-if="props.formData.recurringBillingPeriod === BillingPeriodType.Custom">
          <div class="field mb-4">
            <div class="flex items-center gap-2 ">
              <span class="w-10">
                every
              </span>
              <InputNumber
                v-model="props.formData.recurringBillingPeriodCustom" class="flex-1"
                :disabled="props.submitting"
                mode="currency" currency="AUD" locale="en-AU"
              />
              <Select
                v-model="props.formData.recurringBillingPeriodCustomUnit"
                :options="props.options.billing_period_custom_unit" option-label="label" option-value="value"
                class="flex-1" :disabled="props.submitting"
              />
            </div>
            <Message
              v-if="props.formErrors.recurringBillingPeriodCustom" class="mt-2" severity="error" size="small"
              variant="simple"
            >
              {{ props.formErrors.recurringBillingPeriodCustom }}
            </Message>
          </div>
        </template>

        <div class="field">
          <label class="block mb-2">Billing period</label>
          <Select
            v-model="props.formData.unitBasedBillingPeriod" :options="props.options.billing_period"
            option-label="label" option-value="value" class="w-full" :disabled="props.submitting"
          />
          <Message
            v-if="props.formErrors.unitBasedBillingPeriod" class="mt-2" severity="error" size="small"
            variant="simple"
          >
            {{ props.formErrors.unitBasedBillingPeriod }}
          </Message>
        </div>
        <template v-if="props.formData.unitBasedBillingPeriod === BillingPeriodType.Custom">
          <div class="field mb-4">
            <div class="flex items-center gap-2 ">
              <span class="w-10">
                every
              </span>
              <InputNumber
                v-model="props.formData.unitBasedBillingPeriodCustom" class="flex-1"
                :disabled="props.submitting"
                mode="currency" currency="AUD" locale="en-AU"
              />
              <Select
                v-model="props.formData.unitBasedBillingPeriodCustomUnit"
                :options="props.options.billing_period_custom_unit" option-label="label" option-value="value"
                class="flex-1" :disabled="props.submitting"
              />
            </div>
            <Message
              v-if="props.formErrors.unitBasedBillingPeriodCustom" class="mt-2" severity="error" size="small"
              variant="simple"
            >
              {{ props.formErrors.unitBasedBillingPeriodCustom }}
            </Message>
          </div>
        </template>

        <div class="field mb-4">
          <label for="end_date" class="block mb-4">Unit-based Settings</label>
          <div class="flex gap-6 items-center mb-4">
            <div v-for="category in props.options.endDateType" :key="category.value" class="flex items-center gap-2">
              <RadioButton
                v-model="props.formData.end_date_type" :input-id="String(category.value)"
                :disabled="props.submitting" name="end_date_type" :value="category.value"
              />
              <label :for="category.value">{{ category.label }}</label>
            </div>
          </div>
        </div>

        <div v-if="props.formData.end_date_type === PlanEndDateType.SpecifyByEndDate" class="field mb-4">
          <label for="end_date" class="block mb-4">End date(Required)</label>
          <div class="flex gap-2 items-center">
            <EnhancedDatePicker
              v-model="props.formData.end_date" :disabled="props.submitting"
              :min-date="dayjs().toDate()"
            />
          </div>
          <Message v-if="props.formErrors.end_date" class="mt-2" severity="error" size="small" variant="simple">
            {{ props.formErrors.end_date }}
          </Message>
        </div>

        <div v-if="props.formData.end_date_type === PlanEndDateType.SpecifyByTerm" class="field mb-4">
          <label for="end_terms" class="block mb-4">End terms (Required)</label>
          <div class="flex gap-2 items-center">
            <InputNumber v-model="props.formData.end_terms" :disabled="props.submitting" :min="1" :max="9999" />
          </div>
          <Message v-if="props.formErrors.end_terms" class="mt-2" severity="error" size="small" variant="simple">
            {{ props.formErrors.end_terms }}
          </Message>
        </div>
      </template>

      <!-- 规则 3 One-off -->
      <template v-if="props.formData.schedule_type === ScheduleType.OneOff">
        <div class="field mb-2">
          <label for="pricing_model" class="block mb-2">Choose your pricing model</label>
          <Select
            v-model="props.formData.oneOffPricingModel" :options="props.options.one_off_pricing_model"
            option-label="label" option-value="value" class="w-full" :disabled="props.submitting"
          />
          <Message
            v-if="props.formErrors.oneOffPricingModel" class="mt-2" severity="error" size="small"
            variant="simple"
          >
            {{ props.formErrors.oneOffPricingModel }}
          </Message>
        </div>
        <div class="field">
          <label for="amount" class="block mb-2">Amount</label>
          <InputGroup>
            <InputNumber
              v-model="props.formData.oneOffAmount" class="w-full" :disabled="props.submitting"
              :min-fraction-digits="2" :max-fraction-digits="5"
              mode="currency" currency="AUD" locale="en-AU"
            />
            <!-- <InputGroupAddon>
              <Select
                v-model="props.formData.oneOffCurrency" class="!border-0 !shadow-none w-56"
                :options="props.options.currency" option-label="label" option-value="value"
              />
            </InputGroupAddon> -->
          </InputGroup>
          <Message v-if="props.formErrors.oneOffAmount" class="mt-2" severity="error" size="small" variant="simple">
            {{ props.formErrors.oneOffAmount }}
          </Message>
        </div>
      </template>

      <!-- 注意事项 -->
      <template v-if="user?.xero_link">
        <div class="field mb-2">
          <label for="pricing_model" class="block mb-2">Invoice Template</label>
          <p class="text-(--color-gray-500) mb-2">
            This invoice template will be used when we generate invoices.
          </p>
          <Select
            v-model="props.formData.invoice_theme_id" :options="props.options.invoiceTemplate"
            option-label="label" option-value="value" class="w-full" :class="{ 'p-invalid': props.formErrors.invoice_theme_id }" :disabled="props.submitting"
          />
          <Message
            v-if="props.formErrors.invoice_theme_id" class="mt-2" severity="error" size="small"
            variant="simple"
          >
            {{ props.formErrors.invoice_theme_id }}
          </Message>
        </div>
        <div class="field mb-2">
          <label for="pricing_model" class="block mb-2">Chart of Account</label>
          <p class="text-(--color-gray-500) mb-2">
            The chart of account we'll use when generating invoice line items.
          </p>
          <Select
            v-model="props.formData.invoice_account_code" :options="props.options.invoiceChartOfAccounts"
            option-label="label" option-value="value" class="w-full" :class="{ 'p-invalid': props.formErrors.invoice_account_code }" :disabled="props.submitting"
          />
          <Message
            v-if="props.formErrors.invoice_account_code" class="mt-2" severity="error" size="small"
            variant="simple"
          >
            {{ props.formErrors.invoice_account_code }}
          </Message>
        </div>
      </template>
      <!-- <template v-else>
        <div class="notice-section">
          <span>
            <strong>NOTE:</strong> Your invoice will be generated upon completion and can be found in your Invoice List. If you’ve connected your Xero account, the invoice will also appear in Xero for easy reconciliation.
          </span>
          <span class="mb-0">
            Not Integrated?
            <router-link class="font-bold" :to="{ name: 'integrations' }">
              Connect your Xero account here
            </router-link>
            to get started.
          </span>
        </div>
      </template> -->

      <!-- 状态选项（仅编辑模式显示） -->
      <div v-if="mode === 'edit' && props.options.status.length > 0" class="field my-4">
        <label for="status" class="block mb-2">Status:</label>
        <Select
          v-model="props.formData.status" :options="props.options.status" option-label="label"
          option-value="value" class="w-full" :disabled="props.submitting"
        />
      </div>

      <!-- 表单操作 -->
      <div v-if="mode !== 'view'" class="flex justify-end gap-3 lg:gap-6 mt-4">
        <Button
          type="button" severity="secondary" class="w-40 h-14" label="CANCEL" :disabled="submitting"
          @click="handleCancel"
        />
        <Button type="submit" :label="mode === 'add' ? 'CREATE' : 'UPDATE'" severity="warn" class="w-40 h-14" :loading="submitting" />
      </div>
    </form>
  </div>
</template>

<style lang="scss" scoped>
.plan-subscription-form {

  .field {
    margin-bottom: 1rem;

    .block {
      font-size: 16px;
      color: #181349;
      font-weight: 600;
    }
  }

  .fee-type-option {
    border: 1px solid var(--color-gray-500);
    transition: all 0.2s ease;
    color: var(--p-primary-color);
    font-size: 16px;
  }

  .fee-type-option:hover {
    color: var(--color-orange-500);
    border-color: var(--color-orange-500);
  }

  .fee-type-option.selected {
    color: var(--color-orange-500);
    font-weight: 600;
    border-color: var(--color-orange-500);
  }

  :deep(.p-inputgroup .p-select-label) {
    padding-top: 0;
    padding-bottom: 0;
  }

  .infinity-input {
    color: var(--text-color);
  }

  .infinity-input :deep(input) {
    color: var(--text-color);
  }

  .field-description {
    color: #a6a6a6;
    font-size: 12px;
    margin-top: 2px;
    margin-bottom: 8px;
  }

  /* 控制表格宽度 */
  .tiered-pricing-table {
    overflow-x: auto;
    max-width: 100%;
  }

  :deep(.p-textarea) {
    border-radius: 10px;
  }

  :deep(.p-inputnumber) {
    .p-inputtext {
      height: 44px;
      border-radius: 10px !important;
    }
  }

  :deep(.p-inputtext) {
    height: 44px;
    border-radius: 10px;
  }

  :deep(.p-select) {
    border-radius: 10px;
  }

  :deep(.p-select-label) {
    height: 44px;

    line-height: 30px;
  }

  :deep(.p-button) {
    font-size: 16px;
    padding: 8px 16px;
  }

  /* 确保输入框不会超出单元格 */
  .tiered-pricing-table :deep(.p-inputnumber),
  .tiered-pricing-table :deep(.p-inputtext) {
    width: 100%;
    max-width: 100%;
  }

  .tiered-pricing-table :deep(.p-inputnumber-input) {
    width: 100% !important;
    max-width: 100% !important;
  }

  .increment-group {
    :deep(.p-inputtext) {
      border-bottom-right-radius: 0 !important;
      border-top-right-radius: 0 !important;
    }
  }

  .notice-section {
    border: 2px solid #FE4C1C;
    padding: 4px 8px;
    font-size: 12px;
    color: var(--color-gray-500);
    a {
      color: var(--colors-primary);
    }
  }
}
</style>
