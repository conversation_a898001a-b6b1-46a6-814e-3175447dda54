<script setup lang="ts">
import { Format } from '@shared'
import Decimal from 'decimal.js'
import { useI18n } from 'vue-i18n'
import { BillingPeriodType, RecurringPricingModel, RecurringTieredPaymentMethod, ScheduleType } from '@/constants/plan'
import { formatDate } from '@/utils/date'

defineOptions({
  name: 'PlanSubscriptionInfo',
})

// 定义组件属性
defineProps({
  planDetails: {
    type: Object,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  options: {
    type: Object,
    default: () => ({
      process_type: [],
      status: [],
      pricing_model: [],
      tiered_type: [],
    }),
  },
  optionsLoading: {
    type: Function,
    default: () => {
      return () => false
    },
  },
  getProcessTypeLabel: {
    type: Function,
    default: (val: string | number | null) => val,
  },
  getStatusLabel: {
    type: Function,
    default: (val: string | number | null) => val,
  },
  getPricingModelLabel: {
    type: Function,
    default: (val: string | number | null) => val,
  },
  getTieredTypeLabel: {
    type: Function,
    default: (val: string | number | null) => val,
  },
})

// 定义事件
const emit = defineEmits(['edit', 'back'])

const { t } = useI18n()

// 编辑计划
const handleEdit = () => {
  emit('edit')
}

// 返回列表
const handleBack = () => {
  emit('back')
}

// 获取货币符号
const getCurrencySymbol = (currencyCode: string | null) => {
  if (!currencyCode) { return 'A$' }

  const currencyMap: Record<string, string> = {
    USD: '$',
    EUR: '€',
    GBP: '£',
    CNY: '¥',
    JPY: '¥',
    AUD: 'A$',
  }

  return currencyMap[currencyCode] || 'A$'
}

// 格式化金额
const formatAmount = (amount: number | string | null, currency: string | null = 'AUD') => {
  if (!amount || amount === '') { return '0.00' }
  const symbol = getCurrencySymbol(currency)
  const decimalAmount = new Decimal(amount).toFixed(2)
  return `${symbol}${decimalAmount}`
}

// 获取计费周期文本
const getBillingPeriodText = (period: BillingPeriodType | null) => {
  if (!period) { return '' }

  const periodMap: Record<string, string> = {
    [BillingPeriodType.Daily]: 'Daily',
    [BillingPeriodType.Weekly]: 'Weekly',
    [BillingPeriodType.Fortnightly]: 'Fortnightly',
    [BillingPeriodType.Monthly]: 'Monthly',
    [BillingPeriodType.Yearly]: 'Yearly',
    [BillingPeriodType.Every3Months]: 'Every 3 Months',
    [BillingPeriodType.Every6Months]: 'Every 6 Months',
    [BillingPeriodType.Custom]: 'Custom',
  }

  return periodMap[period] || period
}

// 不需要额外的计算属性，直接在模板中使用条件判断

const getStatusSeverity = (status: number) => {
  if (status === 1) {
    return 'success'
  }
  if (status === 2) {
    return 'warning'
  }
  return 'danger'
}
</script>

<template>
  <div class="plan-subscription-info p-6 bg-white rounded-2xl">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 左侧信息 -->
      <div>
        <div class="mb-6">
          <h3 class="text-xl font-semibold mb-4">
            Plan Information
          </h3>

          <div class="grid grid-cols-1 gap-4">
            <div class="field">
              <label class="block text-gray-500 mb-1">Plan Name:</label>
              <div class="font-medium">
                {{ planDetails.plan_name }}
              </div>
            </div>

            <div class="field">
              <label class="block text-gray-500 mb-1">Description:</label>
              <div class="font-medium">
                {{ planDetails.description || 'No description' }}
              </div>
            </div>

            <div class="field">
              <label class="block text-gray-500 mb-1">Schedule Type:</label>
              <div class="font-medium">
                {{ planDetails.schedule_type === ScheduleType.Recurring ? 'Recurring' : 'One-off' }}
              </div>
            </div>

            <div class="field">
              <label class="block text-gray-500 mb-1">Billing Period:</label>
              <div class="font-medium">
                {{ getBillingPeriodText(planDetails.process_type) }}
                <span v-if="planDetails.custom_cycle && planDetails.custom_cycle_type">
                  (Every {{ planDetails.custom_cycle }} {{ planDetails.custom_cycle_type === 1 ? 'days' : planDetails.custom_cycle_type === 2 ? 'weeks' : planDetails.custom_cycle_type === 3 ? 'months' : 'years' }})
                </span>
              </div>
            </div>

            <div class="field">
              <label class="block text-gray-500 mb-1">GST:</label>
              <div class="font-medium">
                {{ planDetails.is_inclusive_gst ? 'Inclusive' : 'Exclusive' }}
              </div>
            </div>
          </div>
        </div>

        <div class="mb-6">
          <h3 class="text-xl font-semibold mb-4">
            Pricing Information
          </h3>

          <div class="grid grid-cols-1 gap-4">
            <div v-if="planDetails.pricing_model" class="field">
              <label class="block text-gray-500 mb-1">Pricing Model:</label>
              <div class="font-medium">
                {{ planDetails.pricing_model === RecurringPricingModel.StandardPricing ? 'Package Pricing' : 'Tiered Pricing' }}
              </div>
            </div>

            <div v-if="planDetails.prices && planDetails.prices.length > 0">
              <div v-if="planDetails.pricing_model === RecurringPricingModel.StandardPricing || !planDetails.pricing_model" class="field">
                <label class="block text-gray-500 mb-1">Price:</label>
                <div class="font-medium">
                  {{ Format.formatAmount(planDetails.prices[0].amount_per_unit) }}
                  <span v-if="planDetails.prices[0].first_unit && planDetails.prices[0].first_unit > 1">
                    per {{ planDetails.prices[0].first_unit }} units
                  </span>
                </div>
              </div>

              <div v-else-if="planDetails.pricing_model === RecurringPricingModel.TieredPricing" class="field">
                <label class="block text-gray-500 mb-1">Tiered Pricing:</label>
                <div class="font-medium mb-2">
                  Payment Method: {{ planDetails.tiered_type === RecurringTieredPaymentMethod.Volume ? 'Volume' : 'Graduated' }}
                </div>
                <div v-for="(price, index) in planDetails.prices" :key="index" class="pl-4 mb-2 border-l-2 border-gray-200">
                  <div class="font-medium">
                    {{ price.first_unit }} - {{ price.last_unit === '∞' ? 'Unlimited' : price.last_unit }} units:
                    {{ formatAmount(price.amount_per_unit, price.currency) }} per unit
                    <span v-if="price.amount_flat_fee && Number(price.amount_flat_fee) > 0">
                      + {{ formatAmount(price.amount_flat_fee, price.currency) }} flat fee
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div>
          <h3 class="text-xl font-semibold mb-4">
            Timing
          </h3>

          <div class="grid grid-cols-1 gap-4">
            <div class="field">
              <label class="block text-gray-500 mb-1">End Date:</label>
              <div class="font-medium">
                {{ planDetails.is_good_till_cancel ? 'Good till cancel' : formatDate(planDetails.end_date) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧信息 -->
      <div>
        <div class="mb-6">
          <h3 class="text-xl font-semibold mb-4">
            Status Information
          </h3>

          <div class="grid grid-cols-1 gap-4">
            <div class="field">
              <label class="block text-gray-500 mb-1">Status:</label>
              <Tag
                :severity="getStatusSeverity(planDetails.status)"
                :value="getStatusLabel(planDetails.status)"
              />
            </div>

            <div class="field">
              <label class="block text-gray-500 mb-1">Created Date:</label>
              <div class="font-medium">
                {{ formatDate(planDetails.created_at) }}
              </div>
            </div>

            <div class="field">
              <label class="block text-gray-500 mb-1">Last Updated:</label>
              <div class="font-medium">
                {{ formatDate(planDetails.updated_at) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex justify-end gap-3 mt-6">
      <Button type="button" severity="secondary" :label="t('common.back')" @click="handleBack" />
      <Button type="button" :label="t('common.edit')" @click="handleEdit" />
    </div>
  </div>
</template>

<style scoped>
.field {
  margin-bottom: 1rem;
}

.field:last-child {
  margin-bottom: 0;
}

.font-medium {
  line-height: 1.5;
}
</style>
