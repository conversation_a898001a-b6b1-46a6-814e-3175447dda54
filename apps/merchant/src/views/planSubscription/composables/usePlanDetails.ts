import type { DictItem } from '@/services/api/dict'
import { onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { usePermissions } from '@/composables/usePermissions'
import { Permissions } from '@/constants/permissions'
import { plan as planApi } from '@/services/api'
import { useDictStore } from '@/store/modules/dict'

export function usePlanDetails(planId: string) {
  const router = useRouter()
  const { hasPermission } = usePermissions()
  const { getDictByType, isTypeLoading } = useDictStore()

  // 加载状态
  const loading = ref(false)

  // 计划详情数据
  const planDetails = reactive<Plan.Info>({
    plan_id: '',
    plan_name: '',
    amount: '',
    process_type: null,
    end_date: new Date(),
    status: null,
    description: null,
    schedule_type: 1,
    currency: null,
    billing_period: null,
    prices: [],
    tiered_type: null,
    surcharge_rate: {
      fee_rate: '',
      fee_value: '',
    },
  })

  // 字典选项
  const options = ref<Record<string, DictItem[]>>({
    status: [],
    process_type: [],
  })

  // 获取处理类型标签
  const getProcessTypeLabel = (value: string | number | null) => {
    if (value === null || value === undefined) { return 'N/A' }
    const option = options.value.process_type.find(opt => opt.value === value)
    return option ? option.label : value
  }

  // 获取状态标签
  const getStatusLabel = (value: string | number | null) => {
    if (value === null || value === undefined) { return 'N/A' }
    const option = options.value.status.find(opt => opt.value === value)
    return option ? option.label : value
  }

  // 获取费用类型标签
  const getFeeTypeLabel = (value: string | number | null) => {
    if (value === null || value === undefined) { return 'N/A' }
    const option = options.value.fee_type.find(opt => opt.value === value)
    return option ? option.label : value
  }

  // 获取计划详情
  const fetchPlanDetails = async () => {
    try {
      const { code, data } = await planApi.getDetail(planId)
      if (code === 0 && data) {
        // 更新计划详情数据
        Object.assign(planDetails, {
          ...data,
        })
      }
      else {
        window.$toast.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to fetch plan details',
          life: 3000,
        })
        router.push({ name: 'planSubscriptionList' })
      }
    }
    catch (error) {
      console.error('Error fetching plan details:', error)
      window.$toast.add({
        severity: 'error',
        summary: 'Error',
        detail: 'An error occurred while fetching plan details',
        life: 3000,
      })
      router.push({ name: 'planSubscriptionList' })
    }
  }

  // 跳转到编辑页面
  const goToEdit = () => {
    if (!hasPermission(Permissions.PLAN_UPDATE)) {
      return
    }
    router.push({
      name: 'planSubscriptionEdit',
      params: { id: planId },
    })
  }

  // 返回列表页面
  const goBack = () => {
    router.push({ name: 'planSubscriptionList' })
  }

  // 组件挂载时获取计划详情和字典数据
  onMounted(async () => {
    loading.value = true
    try {
      await Promise.all([
        getDictByType('plan_process_type').then((res) => {
          options.value.process_type = res
        }),
        getDictByType('plan_fee_type').then((res) => {
          options.value.fee_type = res
        }),
        getDictByType('plan_status').then((res) => {
          options.value.status = res
        }),
      ])
      await fetchPlanDetails()
    }
    catch (error) {
      console.error('Error loading plan details and dictionaries:', error)
      window.$toast.add({
        severity: 'error',
        summary: 'Error',
        detail: 'An error occurred while loading plan details',
        life: 3000,
      })
    }
    finally {
      loading.value = false
    }
  })

  return {
    planDetails,
    loading,
    options,
    optionsLoading: isTypeLoading,
    getProcessTypeLabel,
    getStatusLabel,
    getFeeTypeLabel,
    goToEdit,
    goBack,
  }
}
