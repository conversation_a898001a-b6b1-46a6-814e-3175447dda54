<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import { usePermissions } from '@/composables/usePermissions'
import { Permissions } from '@/constants/permissions'
import PlanSubscriptionForm from './components/PlanSubscriptionForm.vue'
import { usePlanForm } from './composables/usePlanForm'

// 定义组件选项
defineOptions({
  name: 'planSubscriptionEdit',
})

const { hasPermission } = usePermissions()

const { t } = useI18n()
const route = useRoute()

// 获取路由参数中的计划ID
const planId = route.params.id as string

// 使用 usePlanForm 组合式函数
const {
  formData,
  formErrors,
  loading,
  submitting,
  options,
  optionsLoading,
  submitForm,
} = usePlanForm('edit', planId)
</script>

<template>
  <div v-if="hasPermission(Permissions.PLAN_UPDATE)" class="plan-subscription-edit p-4">
    <!-- 头部 -->
    <div class="flex justify-between items-center mb-4">
      <div class="text-3xl">
        {{ t('planSubscription.dialogs.editPlan') }}
      </div>
    </div>

    <!-- 加载中 -->
    <div v-if="loading" class="flex justify-center items-center h-64">
      <ProgressSpinner />
    </div>

    <div class="form-wrap bg-white rounded-2xl">
      <PlanSubscriptionForm
        :form-data="formData"
        :form-errors="formErrors"
        :options="options"
        :options-loading="optionsLoading"
        :submitting="submitting"
        mode="edit"
        @submit="submitForm"
        @cancel="$router.back()"
      />
    </div>
  </div>
  <div v-else class="flex justify-center items-center h-64">
    <p class="text-gray-500">
      You don't have permission to edit plans.
    </p>
  </div>
</template>

<style scoped>
.plan-subscription-edit {
  min-height: 100%;
}
</style>
