<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import type { DictItem } from '@/services/api/dict'
import Decimal from 'decimal.js'
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import BaseDataTable from '@/components/common/BaseDataTable.vue'
import BaseDataTableActions from '@/components/common/BaseDataTableActions.vue'
import { useDict } from '@/composables/useDict'
import { useExport } from '@/composables/useExport'
import { useListRefresh } from '@/composables/useListRefresh'
import { usePermissions } from '@/composables/usePermissions'
import { useRequestList } from '@/composables/useRequestList'
import { Permissions } from '@/constants/permissions'
import { SearchFieldType } from '@/constants/search'
import { plan as planApi } from '@/services/api'
import { useUserStore } from '@/store/modules/user'
import { formatDate } from '@/utils/date'
import { addAllToDict } from '@/utils/dict'
import { formatFrequencyTime } from '@/utils/format'

defineOptions({
  name: 'planSubscriptionList',
})

const { hasPermission, hasAnyPermission } = usePermissions()

const { t } = useI18n()

const router = useRouter()

const userStore = useUserStore()

// 使用 useRequestList 处理客户列表数据
const requestList = useRequestList<Plan.Info[], Api.PlanListReq>({
  requestFn: planApi.getList,
  immediate: hasPermission(Permissions.PLAN_LIST),
})

const {
  list,
  loading: isListLoading,
  total,
  refresh,
  reset,
  search,
  onPageChange: handlePageChange,
  failed,
  other,
  failureMessage,
  setSearchParams,
} = requestList

// 使用通用的列表刷新逻辑
useListRefresh('planSubscriptionList', refresh)

// 列配置
const columns = ref<TableColumnItem[]>([
  { field: 'plan_name', header: t('planSubscription.columns.name'), style: { minWidth: '110px' } },
  { field: 'pricing', header: t('planSubscription.columns.pricing'), template: 'pricing', style: { minWidth: '160px' } },
  {
    field: 'created_at',
    header: t('planSubscription.columns.created'),
    style: { minWidth: '110px' },
    sortable: true,
    template: 'created',
    sortField: 'created_at',
  },
  {
    field: 'updated_at',
    header: t('planSubscription.columns.updated'),
    style: { minWidth: '200px' },
    sortable: true,
    template: 'updated',
    sortField: 'updated_at',
  },
  { field: 'action', header: '', template: 'action', style: { width: '50px' }, alignFrozen: 'right', frozen: true },
])

const statusOptions = ref<DictItem[]>([])

const tableSelection = ref([])

const isRemoveLoading = ref(false)

const searchModel = ref<Api.PlanListReq>({
  'status': null,
  'name': '',
  'created_at[]': [],
})

// 搜索处理
const handleSearch = () => {
  setSearchParams(searchModel.value)
  search()
}

// Setup export functionality
const { isExporting, handleExport } = useExport({
  exportFn: planApi.exportPlans,
  getParams: () => {
    return setSearchParams(searchModel.value)
  },
  onExportStart: () => {
    window.$toast.add({
      severity: 'info',
      summary: 'Export Started',
      detail: 'Preparing your export file...',
      life: 3000,
    })
  },
})

// 重置所有过滤条件
const resetAllFilters = () => {
  reset()
}

// 手动刷新数据
const refreshData = () => {
  refresh()
}

// 修改导航方法，添加来源标记
const navigateToAddCustomer = () => {
  if (!hasPermission(Permissions.PLAN_CREATE)) {
    return
  }
  userStore.showSelectBid(() => {
    router.push({
      name: 'planSubscriptionAdd',
    })
  })
}

// 查看计划详情
const viewPlanDetails = ({ data: row }: { data: Plan.Info }) => {
  if (!hasPermission(Permissions.PLAN_DETAIL)) {
    return
  }
  router.push({ name: 'planSubscriptionDetails', params: { id: row.plan_id } })
}

// 编辑计划
const editPlan = (row: Plan.Info) => {
  if (!hasPermission(Permissions.PLAN_UPDATE)) {
    return
  }
  router.push({
    name: 'planSubscriptionEdit',
    params: { id: row.plan_id },
  })
}

// 删除计划
const deletePlan = (row?: Plan.Info | null) => {
  if (!hasPermission(Permissions.PLAN_DELETE)) {
    return
  }

  const message = row ? 'Are you sure you want to delete this plan?' : 'Are you sure you want to delete these plans?'
  const header = row ? 'Delete Plan' : 'Delete Plans'
  const ids = row ? [row.plan_id] : tableSelection.value.map((item: Plan.Info) => item.plan_id)
  window.$confirm.require({
    header,
    message,
    icon: 'pi pi-exclamation-triangle',
    accept: () => {
      isRemoveLoading.value = true
      planApi.remove(ids as string[]).then(({ code = 1 }) => {
        if (code === 0) {
          window.$toast.add({ severity: 'success', summary: 'Successful', detail: 'Deleted', life: 3000 })
          refresh()
        }
      }).catch((error) => {
        console.error('Failed to delete plan(s):', error)
        window.$toast.add({ severity: 'error', summary: 'Error', detail: 'Failed to delete plan(s)', life: 3000 })
      }).finally(() => {
        isRemoveLoading.value = false
      })
    },
  })
}

// 格式化金额
const formatAmount = (plan: {
  amount_flat_fee: string
  amount_per_unit: string
  currency: string
}[]) => {
  if (Array.isArray(plan)) {
    if (!Decimal(plan[0].amount_flat_fee || 0).eq(0)) {
      return `${plan[0].currency} $${plan[0].amount_per_unit} + ${plan[0].amount_flat_fee}`
    }
    return `${plan[0].currency} $${plan[0].amount_per_unit}`
  }
  return ''
}

const handleSort = (event: Record<string, any>) => {
  const { sortField, sortOrder } = event
  setSearchParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  search()
}

const { loading: isPlanStatusLoading } = useDict('plan_status', (res) => {
  statusOptions.value = addAllToDict(res, { label: 'All', value: null })
})

const handleStatCardClick = (item: { status: number, status_text: string, count: number }) => {
  if (searchModel.value.status === item.status) {
    return
  }
  searchModel.value.status = item.status
  setSearchParams(searchModel.value)
  refresh()
}

// 配置搜索字段
const searchFields = computed(() => [
  {
    name: 'name',
    label: 'What are you looking for?',
    type: SearchFieldType.TEXT,
    placeholder: 'Search for Plan name',
    maxlength: 50,
    defaultValue: '',
  },
  {
    name: 'status',
    label: 'Category',
    type: SearchFieldType.SELECT,
    placeholder: 'All',
    options: statusOptions.value,
    loading: isPlanStatusLoading,
    defaultValue: '',
  },
])

const moreSearchFields = computed(() => [
  {
    name: 'created_at[]',
    label: 'Created',
    type: SearchFieldType.DATE_RANGE,
    placeholder: 'Please select date range',
    defaultValue: [],
  },
])
</script>

<template>
  <div class="plan-subscription-page">
    <div v-if="other?.stat" class="common-stat-card">
      <div
        v-for="(item, index) in other?.stat" :key="index" class="common-stat-card__item"
        :class="{ active: searchModel.status === item.status }" @click="handleStatCardClick(item)"
      >
        <div class="common-stat-card__title">
          {{ item.status_text }}
        </div>
        <div class="common-stat-card__count">
          {{ item.count }}
        </div>
      </div>
    </div>

    <BaseSearch
      v-model="searchModel" :loading="isListLoading" :basic-search-fields="searchFields"
      :advanced-search-fields="moreSearchFields" @search="handleSearch"
    />

    <div class="flex items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8">
      <BaseExportDialog
        v-if="hasPermission(Permissions.PLAN_EXPORT)" :loading="isListLoading"
        :export-loading="isExporting" @export="handleExport"
      />
      <Button
        v-if="hasPermission(Permissions.PLAN_CREATE)" severity="warn"
        :label="t('planSubscription.actions.createPlan')" icon="pi pi-plus" @click="navigateToAddCustomer"
      />
      <Divider v-if="hasAnyPermission([Permissions.PLAN_CREATE, Permissions.PLAN_DELETE])" layout="vertical" />
      <Button
        v-if="hasPermission(Permissions.PLAN_DELETE)" icon="pi pi-trash" :loading="isRemoveLoading" variant="text"
        :disabled="tableSelection.length === 0" rounded @click="deletePlan(null)"
      />
    </div>

    <BaseDataTable
      v-if="hasPermission(Permissions.PLAN_LIST)" v-model:selection="tableSelection"
      :row-hover="hasPermission(Permissions.PLAN_DETAIL)" :value="list" :columns="columns" :scrollable="true"
      :loading="isListLoading" :paginator="true" :rows="50" :total-records="total" :lazy="true" data-key="id"
      sort-mode="single" :sort-field="($route.query.sort_by as string)"
      :sort-order="$route.query.sort_order === 'desc' ? -1 : 1" :show-search-bar="false"
      :search-placeholder="t('common.filters.search')" :type-placeholder="t('common.filters.filterBy')" :failed="failed"
      :failure-message="failureMessage" :striped-rows="true"
      :show-multiple-column="hasPermission(Permissions.PLAN_DELETE)" @change-search="handleSearch"
      @page="(e: DataTablePageEvent) => handlePageChange(e)" @sort="handleSort" @refresh="refreshData"
      @reset="resetAllFilters" @row-click="viewPlanDetails"
    >
      <template #pricing="{ data }">
        <div class="flex flex-col">
          <span>
            {{ formatAmount(data?.prices) }}
          </span>
          <div class="flex items-center gap-2 mt-1">
            <i class="pi pi-sync" /> <span>{{ formatFrequencyTime(data) }}</span>
          </div>
        </div>
      </template>
      <template #created="{ data }">
        {{ formatDate(data?.created_at) }}
      </template>
      <template #updated="{ data }">
        {{ data?.updated_at && formatDate(data?.updated_at) }}
      </template>
      <template #action="{ data }">
        <BaseDataTableActions
          v-if="hasAnyPermission([Permissions.PLAN_UPDATE, Permissions.PLAN_DELETE])"
          :is-show-detail="false" :is-show-edit="hasPermission(Permissions.PLAN_UPDATE)"
          :is-show-delete="hasPermission(Permissions.PLAN_DELETE)" :loading="data.__loading"
          @detail="viewPlanDetails(data)" @edit="editPlan(data)" @delete="deletePlan(data)"
        />
      </template>
    </BaseDataTable>

    <!-- 无权限提示 -->
    <div v-else class="flex justify-center items-center h-64 bg-white rounded-lg">
      <p class="text-gray-500">
        You don't have permission to view the plan list.
      </p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// 对话框样式
.p-dialog {
  .p-dialog-header {
    border-bottom: 1px solid #dee2e6;
    padding: 1.5rem;
  }

  .p-dialog-content {
    padding: 2rem;
  }

  .p-dialog-footer {
    border-top: 1px solid #dee2e6;
    padding: 1.5rem;
    text-align: right;
  }
}

// 表单字段
.field {
  margin-bottom: 1.5rem;
}

.confirmation-content {
  display: flex;
  align-items: center;
}

.top-search {
  &.active {
    border-color: var(--p-primary-color);
  }
}

.search-vee-form {
  &-label {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--color-gray-500);
  }

  &-input-group {
    position: relative;
  }

  &-select {
    height: 44px;
    width: 200px;
    font-size: 16px;
    line-height: 28px;
    border-radius: 12px;
  }

  &-input-icon {
    position: absolute;
    top: 50%;
    left: 18px;
    transform: translateY(-50%);
    z-index: 2;
    font-size: 16px;
    color: var(--p-button-warn-background);
  }

  &-input {
    height: 44px;
    width: 420px;
    background-color: var(--bg-colors-white);
    position: relative;
    border-radius: 12px;
    font-size: 16px;
    padding-left: 38px;
  }
}

.data-table-container {
  background-color: white;
  border-radius: 1rem;
  padding: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.actions-toolbar {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;

  @media (min-width: 768px) {
    gap: 1rem;
  }

}
</style>
