<script setup lang="ts">
import { usePermissions } from '@/composables/usePermissions'
import { Permissions } from '@/constants/permissions'
import PlanSubscriptionForm from './components/PlanSubscriptionForm.vue'
import { usePlanForm } from './composables/usePlanForm'

// 定义组件选项
defineOptions({
  name: 'planSubscriptionAdd',
})

const { hasPermission } = usePermissions()

const {
  formData,
  formErrors,
  submitting,
  submitForm,
  options,
  optionsLoading,
} = usePlanForm('add')
</script>

<template>
  <div v-if="hasPermission(Permissions.PLAN_CREATE)" class="plan-subscription-add">
    <div class="form-wrap bg-white rounded-2xl">
      <PlanSubscriptionForm
        :form-data="formData"
        :form-errors="formErrors"
        :options="options"
        :options-loading="optionsLoading"
        :submitting="submitting"
        mode="add"
        @submit="submitForm"
        @cancel="$router.back()"
      />
    </div>
  </div>
  <div v-else class="flex justify-center items-center h-64">
    <p class="text-gray-500">
      You don't have permission to create plans.
    </p>
  </div>
</template>

<style lang="scss" scoped>
.form-wrap {
  display: flex;
  justify-content: space-between;
}
.plan-subscription-add {
  min-height: 100%;
}
:deep(.enhanced-date-picker) {
  .p-inputtext {
    border-bottom-right-radius: 0 !important;
    border-top-right-radius: 0 !important;
  }
}
</style>
