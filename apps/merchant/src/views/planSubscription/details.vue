<script setup lang="ts">
import ProgressSpinner from 'primevue/progressspinner'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import { usePermissions } from '@/composables/usePermissions'
import { Permissions } from '@/constants/permissions'
import { plan as planApi } from '@/services/api'
import PlanSubscriptionInfo from './components/PlanSubscriptionInfo.vue'
import { usePlanDetails } from './composables/usePlanDetails'

// 定义组件选项
defineOptions({
  name: 'planSubscriptionDetails',
})

const { hasPermission } = usePermissions()

const { t } = useI18n()
const route = useRoute()

// 获取路由参数中的计划ID
const planId = route.params.id as string

// 使用 usePlanDetails 组合式函数
const {
  planDetails,
  loading,
  options,
  optionsLoading,
  getProcessTypeLabel,
  getStatusLabel,
  getFeeTypeLabel,
  goToEdit,
  goBack,
} = usePlanDetails(planId)

// 删除计划
const deletePlan = () => {
  if (!hasPermission(Permissions.PLAN_DELETE)) {
    return
  }

  window.$confirm.require({
    message: 'Are you sure you want to delete this plan?',
    header: 'Confirmation',
    icon: 'pi pi-exclamation-triangle',
    accept: async () => {
      try {
        const { code } = await planApi.remove([planId])
        if (code === 0) {
          window.$toast.add({
            severity: 'success',
            summary: 'Successful',
            detail: 'Plan deleted successfully',
            life: 3000,
          })
          goBack()
        }
      }
      catch (error) {
        console.error('Error deleting plan:', error)
        window.$toast.add({
          severity: 'error',
          summary: 'Error',
          detail: 'An error occurred while deleting the plan',
          life: 3000,
        })
      }
    },
  })
}
</script>

<template>
  <div v-if="hasPermission(Permissions.PLAN_DETAIL)" class="plan-subscription-details py-4">
    <!-- 头部 -->
    <div class="flex justify-between items-center mb-4">
      <div class="text-3xl">
        {{ t('planSubscription.dialogs.planDetails') }}
      </div>
      <div class="flex gap-2">
        <Button icon="pi pi-arrow-left" :label="t('common.back')" severity="secondary" @click="goBack" />
        <Button
          v-if="hasPermission(Permissions.PLAN_UPDATE)"
          icon="pi pi-pencil"
          :label="t('common.edit')"
          @click="goToEdit"
        />
        <Button
          v-if="hasPermission(Permissions.PLAN_DELETE)"
          icon="pi pi-trash"
          :label="t('common.delete')"
          severity="danger"
          @click="deletePlan"
        />
      </div>
    </div>

    <div v-if="loading" class="bg-white flex justify-center items-center h-64 rounded-xl">
      <ProgressSpinner />
    </div>

    <!-- 使用共享详情组件 -->
    <PlanSubscriptionInfo
      v-else
      :plan-details="planDetails"
      :loading="loading"
      :options="options"
      :options-loading="optionsLoading"
      :get-process-type-label="getProcessTypeLabel"
      :get-status-label="getStatusLabel"
      :get-fee-type-label="getFeeTypeLabel"
      @edit="goToEdit"
      @back="goBack"
    />
  </div>
  <div v-else class="flex justify-center items-center h-64">
    <p class="text-gray-500">
      You don't have permission to view plan details.
    </p>
  </div>
</template>

<style scoped>
.plan-subscription-details {
  min-height: 100%;
}
</style>
