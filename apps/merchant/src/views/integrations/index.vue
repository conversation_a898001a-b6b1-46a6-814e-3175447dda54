<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { integrations as integrationsApi } from '@/services/api'
import { useUserStore } from '@/store/modules/user'
import HubSpot from './components/hubSpot.vue'
import MYOB from './components/myob.vue'
import QuickBooks from './components/quickBooks.vue'
import Xreo from './components/xreo.vue'

interface ErrorInfoType {
  statusCode: string | number
  errorMessageForHumans: string
  whatShouldIDo: string
  moreThanOnceMessage: string
  supportEmail: string
}

const userStore = useUserStore()

const loadings = ref({
  xero: false,
  xeroSync: false,
  xeroRefresh: false,
  quickbooks: false,
  hubspot: false,
  myob: false,
})

const xeroInfo = ref<Api.XeroInfoRes['xero']>({
  email: '',
  file_name: '',
  id: 0,
  merchant_id: '',
  status: 0,
  sync_records: [],
  business_id: '',
})

const showErrorDialog = ref(false)
const errorInfo = ref<ErrorInfoType>({
  statusCode: '500',
  errorMessageForHumans: 'BillBuddy has encountered an unexpected error. Something out of the ordinary has happened.',
  whatShouldIDo: 'This error is usually our fault. Something has gone wrong with the system or we don\'t quite understand what you\'re trying to do. Your best bet is double check that the data you\'re providing us, or the action you\'re taking is correct and try again. If you receive the error again, please see below.',
  moreThanOnceMessage: 'We log all errors that occur, but to help us out please send an email to support with what you\'re trying to do and we\'ll take a look for you.',
  supportEmail: '<EMAIL>',
})

const route = useRoute()

const fetchData = async () => {
  try {
    loadings.value.xero = true
    const { code, data } = await integrationsApi.getXeroInfo()
    if (code === 0) {
      const obj = {
        email: data.xero?.email || '',
        file_name: data.xero?.file_name || '',
        id: data.xero?.id || 0,
        merchant_id: data.xero?.merchant_id || '',
        status: data.xero?.status || 0,
        sync_records: data.xero?.sync_records || [],
        business_id: data.xero?.business_id || '',
      }
      xeroInfo.value = obj
    }
  }
  finally {
    loadings.value.xero = false
  }
}

const handleDisconnectXero = async () => {
  try {
    loadings.value.xero = true
    const { code } = await integrationsApi.xeroDisconnect({
      headers: {
        'Business-Id': xeroInfo.value.business_id,
      },
    })
    if (code === 0) {
      fetchData()
      await userStore.getUserInfo()
      userStore.getMenus()
    }
  }
  finally {
    loadings.value.xero = false
  }
}

const handleSyncXero = async () => {
  loadings.value.xeroSync = true
  try {
    const { code } = await integrationsApi.syncXeroData({}, {
      headers: {
        'Business-Id': xeroInfo.value.business_id,
      },
    })
    if (code === 0) {
      window.$toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Sync Xero data successfully',
      })
    }
  }
  finally {
    loadings.value.xeroSync = false
  }
}

const refreshXeroStatus = async () => {
  loadings.value.xeroRefresh = true
  try {
    const { code } = await integrationsApi.refreshXeroStatus({
      business_id: xeroInfo.value.business_id,
    })
    if (code === 0) {
      window.$toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Xero status refreshed successfully',
      })
    }
  }
  finally {
    loadings.value.xeroRefresh = false
  }
}

const handleConnectXero = () => {
  userStore.showSelectBid(async (bId: string) => {
    const { code, data } = await integrationsApi.xeroSubmitPayment({ redirect_uri: `${window.location.origin}${window.location.pathname}` }, {
      headers: {
        'Business-Id': bId,
      },
    })
    if (code === 0) {
      window.location.href = data.url
    }
  })
}

onMounted(() => {
  fetchData()

  if (route.query.status === '0') {
    showErrorDialog.value = true
    errorInfo.value.statusCode = route.query.code as string
    errorInfo.value.errorMessageForHumans = route.query.message as string
  }
})
</script>

<template>
  <div class="integrations__wrapper" :class="{ 'not-connected': xeroInfo.status === 0 }">
    <Xreo
      :info="xeroInfo" :loading="loadings.xero" :sync-loading="loadings.xeroSync"
      :refresh-loading="loadings.xeroRefresh" @disconnect="handleDisconnectXero" @sync="handleSyncXero"
      @refresh="refreshXeroStatus" @connect="handleConnectXero"
    />
    <div v-if="xeroInfo.status !== 0" class="integrations__right flex-1 flex flex-col gap-4">
      <QuickBooks :loading="loadings.quickbooks" />
      <HubSpot :loading="loadings.hubspot" />
      <MYOB :loading="loadings.myob" />
    </div>
    <template v-else>
      <QuickBooks :loading="loadings.quickbooks" />
      <HubSpot :loading="loadings.hubspot" />
      <MYOB :loading="loadings.myob" />
    </template>

    <!-- Error Dialog -->
    <Dialog
      v-model:visible="showErrorDialog" modal :closable="true" :draggable="false"
      class="w-11/12 md:w-3/5 lg:w-1/2 xl:w-2/5" :pt="{
        root: { class: 'p-0 border-none shadow-xl' },
        header: { class: 'hidden' }, // Hide default header
        content: { class: 'p-6 sm:p-8 bg-sky-50 rounded-lg' },
      }" @after-hide="() => {
        $router.replace({ query: {} })
      }"
    >
      <div class="text-left">
        <h1 class="text-3xl sm:text-4xl font-bold text-indigo-700 mb-2">
          System Error
        </h1>
        <p class="text-lg sm:text-xl text-indigo-600 mb-6">
          Status Code: {{ errorInfo.statusCode }}
        </p>
        <h2 class="text-base sm:text-lg font-semibold text-indigo-700 mb-2">
          Error Message for Humans:
        </h2>
        <p class="text-sm sm:text-base text-gray-700 mb-6">
          {{ errorInfo.errorMessageForHumans }}
        </p>

        <h2 class="text-base sm:text-lg font-semibold text-indigo-700 mb-2">
          What should I do?:
        </h2>
        <p class="text-sm sm:text-base text-gray-700 mb-6">
          {{ errorInfo.whatShouldIDo }}
        </p>

        <h2 class="text-base sm:text-lg font-semibold text-indigo-700 mb-2">
          This is happening more than just once:
        </h2>
        <p class="text-sm sm:text-base text-gray-700">
          {{ errorInfo.moreThanOnceMessage }}
        </p>
        <p class="text-sm sm:text-base text-gray-700 mt-1">
          Support: <a
            :href="`mailto:${errorInfo.supportEmail}`"
            class="text-red-500 hover:text-red-700 underline font-medium"
          >{{ errorInfo.supportEmail }}</a>
        </p>
      </div>
    </Dialog>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints' as *;

.integrations__wrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 24px;
  height: 100vh;

  &.not-connected {
    flex-direction: column;

    :deep(.not-connected__wrapper) {
      width: 580px;
    }
  }

  :deep(.not-connected__wrapper) {
    max-width: 580px;
    padding: 50px 32px;
    border-radius: 20px;
    box-sizing: border-box;

    &.bg-white {
      background: var(--color-white);
    }

    .title {
      font-size: 24px;
      font-weight: 600;
      color: var(--color-gray-500);
    }

    .sub-title {
      font-size: 14px;
      color: #FF5F00;
      cursor: pointer;
      font-style: italic;
    }
  }

  :deep(.connected__wrapper) {
    display: flex;
    flex-direction: column;
    max-width: 580px;

    @include media-breakpoint-down(md) {
      max-width: 100%;
    }

    .connected-header {
      display: flex;
      flex-direction: column;
      background: var(--color-white);
      padding: 32px;
      border-radius: 20px;
      color: var(--color-gray-500);
    }

    .connected-content {
      display: flex;
      flex-direction: column;
      background: var(--color-white);
      padding: 32px;
      border-radius: 20px;
      color: var(--color-gray-500);
      box-sizing: border-box;
    }

    .connected-title__wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .connected-title {
        font-size: 24px;
        font-weight: 600;
      }
    }

    .connected-description {
      margin-top: 24px;
      padding: 16px 0;
    }
  }
}
</style>
