<script setup lang="ts">
import Skeleton from 'primevue/skeleton'
import { ref } from 'vue'
import xeroLogoConnect from '@/assets/merchant/integrations/connect-white.svg'
import xeroLogoDisconnect from '@/assets/merchant/integrations/disconnect-white.svg'
import { SyncRecordStatus } from '@/constants/invoice'
import { formatDate } from '@/utils/date'

const props = withDefaults(defineProps<{
  loading: boolean
  syncLoading: boolean
  refreshLoading: boolean
  info: Api.XeroInfoRes['xero']
}>(), {
  loading: false,
  syncLoading: false,
  refreshLoading: false,
  info: () => ({
    email: '',
    file_name: '',
    id: 0,
    merchant_id: '',
    status: 0,
    sync_records: [],
    business_id: '',
  }),
})

const emits = defineEmits(['disconnect', 'sync', 'refresh', 'connect'])

const isShowMoreInformation = ref(false)

// const handleMoreInformation = () => {
//   isShowMoreInformation.value = true
// }

const handleDisconnect = () => {
  window.$confirm.require({
    header: 'Disconnect',
    message: 'Are you sure you want to disconnect?',
    accept: () => {
      emits('disconnect')
    },
  })
}

const linkTo = () => {
  window.open('https://apps.xero.com/au/function/payments', '_blank')
}

const handleSync = () => {
  emits('sync')
}

const handleRefresh = () => {
  emits('refresh')
}

const handleConnect = () => {
  emits('connect')
}
</script>

<template>
  <div :class="{ 'bg-white not-connected__wrapper': props.info.status === 0 }">
    <template v-if="props.loading">
      <div class="flex items-center">
        <div class="flex flex-col space-y-2 w-2/3">
          <Skeleton width="80%" height="24px" class="mb-2" />
          <Skeleton width="60%" height="16px" class="mb-2" />
        </div>
        <div class="flex-1 flex items-center w-2/3">
          <Skeleton width="100%" height="43px" />
        </div>
      </div>
    </template>
    <template v-else>
      <div v-if="props.info.status === 0" class="flex items-center">
        <div class="flex flex-col space-y-2 w-2/3">
          <div class="title">
            Xero
          </div>
          <div class="sub-title" @click="linkTo">
            More information
          </div>
        </div>
        <div class="flex-1 flex items-center w-2/3">
          <Image class="w-full cursor-pointer" :src="xeroLogoConnect" alt="Xero Logo" @click.stop="handleConnect" />
        </div>
      </div>
      <div v-else class="connected__wrapper">
        <!-- 连接成功后显示 -->
        <div class="connected-header">
          <div class="connected-title__wrapper">
            <div class="connected-title">
              Synchronizing with Xero
            </div>
            <div class="connected-sub-title">
              <BaseTag type="paid" class="!font-bold w-30">
                Queued
              </BaseTag>
            </div>
          </div>
          <p class="connected-description">
            We're getting BillBuddy up to date with your Xero account. This process will run in the background and may take
            some time depending on the size of your account.
          </p>
          <p>
            You will be notified when we've completed your integration.
          </p>
        </div>

        <div class="connected-content mt-4">
          <h2 class="mb-4 text-2xl" style="font-weight: 800;">
            Integration Status
          </h2>
          <div class="flex items-center gap-4 mb-4">
            <div style="font-weight: 600;">
              Connected to:
            </div>
            <div>
              Xero
            </div>
          </div>
          <div class="flex items-center gap-4 mb-4">
            <div style="font-weight: 600;">
              Status:
            </div>
            <div>
              <BaseTag type="paid" class="!font-bold w-30" :severity="props.info?.status === 1 ? 'paid' : 'upcoming'">
                {{ props.info?.status === 1 ? 'Connected' : 'Disconnected' }}
              </BaseTag>
            </div>
          </div>
          <div class="flex items-center gap-4 mb-4">
            <div style="font-weight: 600;">
              Connected by:
            </div>
            <div>
              {{ props.info.email }}
            </div>
          </div>

          <div class="flex border-b pb-4 border-gray-200 justify-between items-center gap-4 mb-4">
            <Button
              severity="warn" class="refresh-btn" label="Refresh Status" :loading="refreshLoading"
              @click="handleRefresh"
            />
            <Image
              class="cursor-pointer" :src="xeroLogoDisconnect" alt="Xero Logo" style="width: 180px;"
              @click.stop="handleDisconnect"
            />
          </div>

          <div class="text-xl" style="font-weight: 800;">
            Recent Daily Syncs
          </div>

          <div class="flex flex-col w-full gap-4 mb-4 mt-4">
            <div v-for="item in props.info.sync_records" :key="item.id" class="flex items-center">
              <div class="flex mr-4 items-center justify-center w-6 h-6 rounded-full bg-gray-200" :class="item.status === SyncRecordStatus.Fail ? 'bg-red-500' : item.status === SyncRecordStatus.Pending ? 'bg-yellow-500' : 'bg-green-500'">
                <i class="pi" :class="item.status === SyncRecordStatus.Fail ? 'pi-times' : item.status === SyncRecordStatus.Pending ? 'pi-clock' : 'pi-check'" />
              </div>
              <div>
                {{ formatDate(item.created_at) }}
              </div>
            </div>
          </div>

          <div class="flex flex-col gap-4 mb-4 bg-(--color-white-100) p-6 rounded-lg" style="font-size: 15px;">
            <strong>Do not press this button if you are experiencing Payment or Settlement sync issues to your Xero
              file. <span class="contact-support cursor-pointer" @click="$router.push('/support')">Contact Support</span> instead. </strong>

            <div class="flex items-center gap-4 mb-4 justify-center">
              <Button class="sync-now-btn" label="Sync Now" :loading="syncLoading" @click="handleSync" />
            </div>
          </div>
        </div>
      </div>
    </template>

    <Dialog
      v-model:visible="isShowMoreInformation" :modal="true" :closable="false" :draggable="false"
      :resizable="false" :dismissable-mask="true" :close-on-escape="true" :close-on-backdrop="true" :show-header="false"
      :show-footer="false" :style="{ width: '578px' }"
    >
      <div class="flex flex-col p-6 more-content">
        <div class="bg-white rounded-lg py-6">
          <div class="flex justify-between items-center mb-4">
            <div class="flex items-center">
              <div class="font-bold text-xl">
                Connect to Xero
              </div>
            </div>
            <div class="flex items-center">
              <Image
                class="cursor-pointer" :src="xeroLogoConnect" alt="Xero Logo"
                style="width: 180px; height: 43px;"
              />
            </div>
          </div>
          <hr class="my-4">
          <div class="text-base mb-4" style="font-size: 15px;">
            When you connect to Xero, the data flows between the tool – so changes you make in the app are reflected in
            Xero
            and vice-versa. This connection means you only need to complete tasks once, in one place, so it's easier to
            see
            the exact state of your finances.
          </div>
          <ul class="list-disc pl-6 space-y-2">
            <li>Speed up processes with apps that automate and simplify everything – from cash flow to CRM</li>
            <li>Integrate with your Xero account to get a more complete view of your business</li>
            <li>Reduce manual data entry: your numbers are accurate and you save time</li>
          </ul>
        </div>
        <div class="text-right mt-4">
          <Button class="close-btn underline" text @click="linkTo">
            Close
          </Button>
        </div>
      </div>
    </Dialog>
  </div>
</template>

<style scoped lang="scss">
.more-content {
  color: var(--color-gray-500);

  .close-btn {
    color: var(--colors-primary);
  }

}

.refresh-btn,
.sync-now-btn {
  color: var(--color-white);
  background: #09DEFF;
  border-radius: 8px;
  padding: 12px 48px;
  font-size: 16px;
  font-weight: 600;
  border: none;

  &:not(:disabled):hover {
    border: none;
    background: hsl(180, 100%, 40%);
  }
}

.contact-support {
  color: #FE4C1C;
}
</style>
