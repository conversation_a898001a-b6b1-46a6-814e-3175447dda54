<script setup lang="ts">
// import CardChart from './components/cardChart.vue'
import { Format } from '@shared'
import dayjs from 'dayjs'
import { LineChart } from 'echarts/charts'
import { GraphicComponent, GridComponent, LegendComponent, TitleComponent } from 'echarts/components'
import * as echarts from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import Skeleton from 'primevue/skeleton'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import EnhancedDatePicker from '@/components/common/EnhancedDatePicker.vue'
import { usePermissions } from '@/composables/usePermissions'
import { Permissions } from '@/constants/permissions'
import { getBusinessSummary, getCollectedPayment, getSumData } from '@/services/api/home'

echarts.use([TitleComponent, GridComponent, CanvasRenderer, LineChart, LegendComponent, GraphicComponent])

const { hasPermission, hasAnyPermission } = usePermissions()

export interface homeData {
  active_customer: number
  failed_amount: string
  pending_amount: string
  succeeded_amount: string
  risk_customer: number
  pending_payout: string
  average_daily_transaction_count: string
  /**
   * 平均每日交易计算增长比例
   */
  average_daily_transaction_count_rate: string
  /**
   * 平均交易金额
   */
  average_transaction_amount: string
  /**
   * 平均交易金额增长比例
   */
  average_transaction_amount_rate: string
  /**
   * 日均收人
   */
  daily_average_revenue: string
  /**
   * 日均收人增长比例
   */
  daily_average_revenue_rate: string
  /**
   * 成功付款率
   */
  successful_payment: string
  /**
   * 成功付款率增长比例
   */
  successful_payment_rate: string
}

interface chartData {
  day: string
  succeeded_amount: string
}

const router = useRouter()
const isPageLoaded = ref(false)
const loading = ref(true)
const Data = ref<homeData>()

const summaryData = ref()
const dates = ref([dayjs().subtract(1, 'month').toDate(), dayjs().toDate()])
const renderDom = ref()

// 简单的页面加载动画
onMounted(() => {
  setTimeout(() => {
    isPageLoaded.value = true
  }, 300)

  const promises: Promise<any>[] = []

  // 获取汇总数据 - 需要 HOME_GET_SUM_DATA 权限
  if (hasPermission(Permissions.HOME_GET_SUM_DATA)) {
    promises.push(
      getSumData().then((res) => {
        Data.value = res.data
        updateHomeData(res.data)
      }).catch((error) => {
        console.error('Failed to fetch sum data:', error)
      }),
    )
  }

  // 获取支付数据 - 需要 HOME_GET_PAYMENT_DATA 权限
  if (hasPermission(Permissions.HOME_GET_PAYMENT_DATA)) {
    promises.push(
      getCollectedPayment({ days: selectedPeriod.value.code }).then((res) => {
        const amountXAxis = res.data.map((item: chartData) => {
          return item?.succeeded_amount
        })
        const series = res.data.map((item: chartData) => {
          return item.day
        })
        const chartDom = document.getElementById('chart')
        if (!chartDom) {
          return
        }
        renderDom.value = echarts.init(chartDom)
        const option = {
          graphic: [{
            type: 'text',
            left: '0',
            top: 'middle',
            rotation: Math.PI / 2,
            style: {
              text: 'Payout Transaction',
              fontSize: 14,
              fill: '#464646',
            },
          }],
          xAxis: {
            type: 'category',
            data: series,
            name: 'Transaction Count',
            nameLocation: 'center',
            nameGap: 30,
          },
          yAxis: [
            {
              type: 'value',
              min: 0,
              max: 40,
              interval: 10,
            },
          ],
          legend: {
            data: ['Series 1'],
            icon: 'circle',
          },
          color: ['#ff5f00', '#09deff'],
          grid: {
            left: '45px',
            right: '10%',
            bottom: '15%',
            top: '11%',
          },
          series: [
            {
              name: 'Series 1',
              data: amountXAxis,
              type: 'line',
              lineStyle: { color: '#ff5f00' },
            },
          ],

        }
        renderDom.value && renderDom.value.setOption(option)
      }).catch((error) => {
        console.error('Failed to fetch payment data:', error)
      }),
    )
  }

  // 获取业务绩效汇总 - 需要 HOME_GET_BUSINESS_PERFORMANCE_SUMMARY 权限
  if (hasPermission(Permissions.HOME_GET_BUSINESS_PERFORMANCE_SUMMARY)) {
    promises.push(
      getBusinessSummary(dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')).then((res) => {
        summaryData.value = res.data
      }).catch((error) => {
        console.error('Failed to fetch business summary:', error)
      }),
    )
  }

  // 执行所有有权限的请求
  if (promises.length > 0) {
    Promise.all(promises).finally(() => {
      loading.value = false
    })
  }
  else {
    // 如果没有任何权限，直接停止加载
    loading.value = false
  }
})

const fundList = ref([
  {
    name: 'Pending payments',
    amount: '0.00',
    currency: 'AUD',
    detail: 'View all payments',
    to: {
      name: 'transactionsList',
      query: {
        status: 3,
      },
    },
  },
  {
    name: 'Confirmed funds',
    amount: '0.00',
    currency: 'AUD',
    detail: 'View confirmed payments',
    to: {
      name: 'transactionsList',
      query: {
        status: 1,
      },
    },
  },
  {
    name: 'Pending payouts',
    amount: '0.00',
    currency: 'AUD',
    detail: 'View payouts',
    to: {
      name: 'payoutList',
      query: {
        status: 0,
      },
    },
  },
])

const selectedPeriod = ref({ name: 'Last 7 days', code: '7' })
const periodOptions = ref([
  { name: 'Last 7 days', code: '7' },
  { name: 'Last 30 days', code: '30' },
  { name: 'Last 60 days', code: '60' },
])
const selectDate = ref<Date>(new Date())
const accountList = ref([
  {
    name: 'Active customers',
    amount: '8',
    detail: 'View customers',
    to: {
      name: 'customersList',
      query: {
        category: 1,
      },
    },
  },
  {
    name: 'Customers at risk',
    amount: '2',
    detail: 'View customers',
    to: {
      name: 'customersList',
      query: {
        category: 5,
      },
    },
  },
  {
    name: 'Failed payments',
    amount: '$0.00',
    detail: 'View failed payments',
    to: {
      name: 'transactionsList',
      query: {
        status: 2,
      },
    },
  },
])
const summaryDatePicker = ref()

const viewTransition = (to: { name: string, query: { status: number } }) => {
  // 检查是否有相应的权限访问目标页面
  if (to.name === 'transactionsList' && !hasAnyPermission([Permissions.TRANS_LIST])) {
    return
  }
  if (to.name === 'payoutList' && !hasAnyPermission([Permissions.PAYOUT_LIST])) {
    return
  }

  router.push({
    name: to.name,
    query: to.query,
  })
}

const handleViewAction = (to: { name: string, query: { status?: number } }) => {
  // 检查是否有相应的权限访问目标页面
  if (to.name === 'customersList' && !hasAnyPermission([Permissions.CUSTOMER_LIST])) {
    return
  }
  if (to.name === 'transactionsList' && !hasAnyPermission([Permissions.TRANS_LIST])) {
    return
  }

  router.push({
    name: to.name,
    query: to.query,
  })
}

const fundAmountMapping: Record<string, keyof homeData> = {
  'Pending payments': 'pending_amount',
  'Confirmed funds': 'succeeded_amount',
  'Pending payouts': 'pending_payout',
}

const accountAmountMapping: Record<string, keyof homeData> = {
  'Active customers': 'active_customer',
  'Failed payments': 'failed_amount',
  'Customers at risk': 'risk_customer',
}

const updateHomeData = (data: homeData) => {
  fundList.value.forEach((item) => {
    const key = fundAmountMapping[item.name]
    if (key) {
      item.amount = Format.formatAmount(String(data[key]))
    }
  })

  accountList.value.forEach((item) => {
    const key = accountAmountMapping[item.name]
    if (key) {
      // Format different metrics appropriately based on data key
      if (key === 'active_customer' || key === 'risk_customer') {
        // Display as plain number for customer counts
        item.amount = String(data[key])
      }
      else if (key === 'failed_amount') {
        // Display as currency amount for failed payment total
        item.amount = Format.formatAmount(String(data[key]))
      }
      else {
        // Default to currency formatting
        item.amount = Format.formatAmount(String(data[key]))
      }
    }
  })
}

const handleConfirm = async () => {
  if (!hasPermission(Permissions.HOME_GET_BUSINESS_PERFORMANCE_SUMMARY)) {
    return
  }

  if (summaryDatePicker.value && summaryDatePicker.value?.overlayVisible) {
    summaryDatePicker.value.overlayVisible = false
  }
  if (dates.value[1] === null) {
    return
  }

  const sendData = {
    start_date: dayjs(dates.value[0]).format('YYYY-MM-DD') || null,
    end_date: dayjs(dates.value[1]).format('YYYY-MM-DD') || null,
  }

  try {
    const res = await getBusinessSummary(sendData.start_date, sendData.end_date)
    if (res.code === 0) {
      summaryData.value = res.data
    }
  }
  catch (error) {
    console.error('Failed to fetch business summary:', error)
  }
}

const handleChart = async (e: any) => {
  if (!hasPermission(Permissions.HOME_GET_PAYMENT_DATA)) {
    return
  }

  try {
    const res = await getCollectedPayment({ days: e.value.code })
    const amountXAxis = res.data.map((item: chartData) => {
      return item?.succeeded_amount
    })
    const series = res.data.map((item: chartData) => {
      return item.day
    })

    const option = {
      graphic: [{
        type: 'text',
        left: '0', // 左侧边距
        top: 'middle', // 垂直居中
        rotation: Math.PI / 2, // 旋转90度（竖向）
        style: {
          text: 'Payout Transaction', // 标题内容
          fontSize: 14,
          fill: '#464646',
        },
      }],
      xAxis: {
        type: 'category',
        data: series,
        name: 'Transaction Count', // 底部标题文本
        nameLocation: 'center', // 标题居中
        nameGap: 30, // 标题与轴的距离
      },
      yAxis: [
        {
          type: 'value',

        },
      ],
      legend: {
        data: ['Series 1'],
        icon: 'circle',
      },
      color: ['#ff5f00', '#09deff'],
      grid: {
        left: '45px',
        right: '10%',
        bottom: '15%',
        top: '11%',
      },
      series: [
        {
          name: 'Series 1',
          data: amountXAxis,
          type: 'line',
          lineStyle: { color: '#ff5f00' },
        },
      ],

    }
    if (renderDom.value) {
      renderDom.value && renderDom.value.setOption(option)
    }
  }
  catch (error) {
    console.error('Failed to fetch chart data:', error)
  }
}

const handleDateChart = async (e: any) => {
  if (!hasPermission(Permissions.HOME_GET_PAYMENT_DATA)) {
    return
  }

  try {
    const res = await getCollectedPayment({ date: dayjs(e).format('YYYY-MM-DD') })
    const amountXAxis = res.data.map((item: chartData) => {
      return item?.succeeded_amount
    })
    const series = res.data.map((item: chartData) => {
      return item.day
    })

    const option = {
      graphic: [{
        type: 'text',
        left: '0', // 左侧边距
        top: 'middle', // 垂直居中
        rotation: Math.PI / 2, // 旋转90度（竖向）
        style: {
          text: 'Payout Transaction', // 标题内容
          fontSize: 14,
          fill: '#464646',
        },
      }],
      xAxis: {
        type: 'category',
        data: series,
        name: 'Transaction Count', // 底部标题文本
        nameLocation: 'center', // 标题居中
        nameGap: 30, // 标题与轴的距离
      },
      yAxis: [
        {
          type: 'value',

        },
      ],
      legend: {
        data: ['Series 1'],
        icon: 'circle',
      },
      color: ['#ff5f00', '#09deff'],
      grid: {
        left: '45px',
        right: '10%',
        bottom: '15%',
        top: '11%',
      },
      series: [
        {
          name: 'Series 1',
          data: amountXAxis,
          type: 'line',
          lineStyle: { color: '#ff5f00' },
        },
      ],

    }
    if (renderDom.value) {
      renderDom.value && renderDom.value.setOption(option)
    }
  }
  catch (error) {
    console.error('Failed to fetch date chart data:', error)
  }
}
</script>

<template>
  <div class="home">
    <div class="welcome px-6 py-6 pt-4">
      <div class="welcome-title">
        Welcome to Bill Buddy
      </div>
      <!-- <div class="tips">
        <i class="pi pi-info-circle mr-4" />Customise this message with your name in Setting.
      </div> -->
    </div>
    <div class="merchant-common-page">
      <transition name="fade" appear>
        <div class="w-full max-w-8xl">
          <div>
            <!-- <h1 class="text-2xl sm:text-3xl font-semibold mb-8">
            One Tao Kung Fu Academy
          </h1> -->
            <section v-if="hasPermission(Permissions.HOME_GET_BUSINESS_PERFORMANCE_SUMMARY) && loading" class="summary mb-6">
              <div class="summary-title flex justify-between items-center mb-6">
                <div>
                  <div class="font-bold text-[16px] mb-2">
                    Business Performance Summary
                  </div>
                  <Skeleton width="300px" height="40px" />
                </div>
                <div>
                  <Skeleton width="120px" height="40px" />
                </div>
              </div>
              <div class="summary-content grid grid-cols-1 gap-y-4 lg:gap-y-0 lg:grid-cols-2 gap-x-28">
                <div class="summary-item border-style px-8 py-4">
                  <Skeleton width="150px" height="36px" />
                  <Skeleton width="120px" height="24px" class="mb-2" />
                  <Skeleton width="240px" height="28px" />
                </div>
                <div class="summary-item border-style px-8 py-4">
                  <Skeleton width="150px" height="36px" />
                  <Skeleton width="120px" height="24px" class="mb-2" />
                  <Skeleton width="240px" height="28px" />
                </div>
              </div>
            </section>
            <section v-else-if="hasPermission(Permissions.HOME_GET_BUSINESS_PERFORMANCE_SUMMARY) && summaryData" class="summary mb-6">
              <div class="summary-title flex justify-between items-center mb-6">
                <div>
                  <div class="font-bold text-[16px] mb-2">
                    Business Performance Summary
                  </div>
                  <div class="date-picker-wrapper grey-color">
                    <DatePicker
                      ref="summaryDatePicker" v-model="dates" selection-mode="range" date-format="dd/mm/yy"
                      :manual-input="false"
                    >
                      <template #footer>
                        <div class="flex justify-end mt-2">
                          <Button @click="handleConfirm">
                            confirm
                          </Button>
                        </div>
                      </template>
                    </DatePicker>
                  </div>
                </div>
                <!-- <div v-if="hasAnyPermission([Permissions.REPORT_REVENUE, Permissions.REPORT_REVENUE_DETAIL, Permissions.REPORT_TRANSACTION_PAYOUT, Permissions.REPORT_SUBSCRIPTION])">
                  <Button label="View Reports" variant="outlined" @click="$router.push({ name: 'report' })" />
                </div> -->
              </div>
              <div class="summary-content grid grid-cols-1 gap-y-4 lg:gap-y-0 lg:grid-cols-2 gap-x-28">
                <div class="summary-item border-style  px-8 py-4">
                  <div class="item-amount">
                    {{ Format.formatAmount(summaryData.total_revenue) }}
                  </div>
                  <div class="item-subtitle mb-1 grey-color font-semibold">
                    Gross Revenue
                  </div>
                  <div class="item-trend flex items-center gap-2">
                    <Tag
                      class="rounded-md p-2"
                      :style="{ backgroundColor: summaryData.total_revenue_rate.charAt(0) === '+' ? '#e1ffa9' : '#ffe3e8' }"
                    >
                      <i
                        class="pi "
                        :class="summaryData.total_revenue_rate.charAt(0) === '+' ? 'pi-arrow-up-right' : 'pi-arrow-down-right'"
                      />
                      <span>{{ summaryData.total_revenue_rate.slice(1) }}</span>
                    </Tag><span> vs. previous period selected</span>
                  </div>
                </div>
                <div class="summary-item border-style  px-8 py-4">
                  <div class="item-amount">
                    {{ Format.formatAmount(summaryData.net_revenue) }}
                  </div>
                  <div class="item-subtitle mb-1 grey-color font-semibold">
                    Net Revenue
                  </div>
                  <div class="item-trend flex items-center gap-2">
                    <Tag
                      class="rounded-md p-2"
                      :style="{ backgroundColor: summaryData.net_revenue_rate.charAt(0) === '+' ? '#e1ffa9' : '#ffe3e8' }"
                    >
                      <i
                        class="pi "
                        :class="summaryData.net_revenue_rate.charAt(0) === '+' ? 'pi-arrow-up-right' : 'pi-arrow-down-right'"
                      />
                      <span>{{ summaryData.net_revenue_rate.slice(1) }}</span>
                    </Tag><span> vs. previous period selected</span>
                  </div>
                </div>
              </div>
            </section>
            <section v-if="hasPermission(Permissions.HOME_GET_SUM_DATA)" class="mb-8">
              <h2 class="text-xl font-semibold mb-4">
                Today
              </h2>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <template v-if="loading">
                  <div v-for="i in 3" :key="i" class="bg-white rounded-2xl p-6 border border-[#545454]">
                    <Skeleton width="180px" height="28px" />
                    <Skeleton width="120px" height="28px" class="my-2" />
                    <Skeleton width="140px" height="20px" />
                  </div>
                </template>
                <div
                  v-for="(item, index) in fundList" v-else :key="index"
                  class="bg-white rounded-2xl p-6 border border-[#545454]"
                >
                  <div class="flex items-center gap-2 text-2xl font-semibold text-[#545454]">
                    {{ item.name }}
                  </div>
                  <div class="font-semibold text-lg my-2">
                    <span class="text-2xl">{{ item.amount[0] }}</span>{{ item.amount.slice(1) }}
                    {{ item.currency }}
                  </div>
                  <div class="text-primary-600 underline cursor-pointer" @click="viewTransition(item.to)">
                    {{ item.detail }}
                  </div>
                </div>
              </div>
            </section>
            <!-- <section class="mb-4">
              <h2 class="text-xl font-semibold mb-4">
                Attributed Revenue
              </h2>
              <div class="attributed-content">
                <div class="attributed-item gap-4">
                  <div>
                    <i class="pi pi-user" style="font-size: 20px;margin-right: 10px;" />
                    <span>Per Recepient</span>
                  </div>
                  <div>
                    $5.76
                  </div>
                </div>
                <div class="attributed-item gap-4">
                  <div>
                    <i class="pi pi-user" style="font-size: 20px;margin-right: 10px;" />
                    <span>Category</span>
                  </div>
                  <div>
                    $5.76
                  </div>
                </div>
                <div class="attributed-item gap-4">
                  <div>
                    <i class="pi pi-user" style="font-size: 20px;margin-right: 10px;" />
                    <span>Category</span>
                  </div>
                  <div>
                    $5.76
                  </div>
                </div>
                <div class="attributed-item gap-4">
                  <div>
                    <i class="pi pi-user" style="font-size: 20px;margin-right: 10px;" />
                    <span>Category</span>
                  </div>
                  <div>
                    $5.76
                  </div>
                </div>
              </div>
            </section> -->
          </div>
        </div>
      </transition>
    </div>
    <div v-if="hasPermission(Permissions.HOME_GET_SUM_DATA)" class="w-full max-w-8xl">
      <div class="grid lg:grid-cols-4 gap-8 mt-8">
        <template v-if="loading">
          <div v-for="i in 4" :key="i" class="display-item">
            <Skeleton width="200px" height="24px" class="mb-4" />
            <Skeleton width="170px" height="36px" class="mb-5" />
            <Skeleton width="100%" height="16px" class="mb-2" />
            <Skeleton width="100%" height="16px" />
          </div>
        </template>
        <template v-else>
          <div class="display-item">
            <div class="item-title">
              Daily Avg. Revenue <i v-tooltip="'Average revenue amount for each day since first transaction on account.'" class="pi pi-question-circle cursor-pointer" style="color: #b5b5b5;margin-left: 10px;" />
            </div>
            <div class="display-item-amount">
              {{ Format.formatAmount(Data?.daily_average_revenue) }}
              <Tag
                style="border-radius: 16px; background: #e1ffa9; color: #545454;font-weight: 500;margin-left: 5px;"
                :style="{ backgroundColor: Data?.daily_average_revenue.charAt(0) === '+' ? '#e1ffa9' : '#ffe3e8' }"
              >
                <div class="flex items-center gap-2 px-1">
                  <i
                    class="pi " style="color: #8bf160;font-weight: 700;"
                    :class="Data?.daily_average_revenue.charAt(0) === '+' ? 'pi-arrow-up' : 'pi-arrow-down'"
                    :style="{ color: Data?.daily_average_revenue_rate.charAt(0) === '+' ? '#8bf160' : '#ff5757' }"
                  />
                  <span class="text-base">{{ Data?.daily_average_revenue_rate.slice(1) }}</span>
                </div>
              </Tag>
            </div>
            <!-- <CardChart :chart-data="chartDataList[0]" chart-type="red" height="180px" /> -->
          </div>
          <div class="display-item">
            <div class="item-title">
              Successful Payment Rate <i v-tooltip="'The number of successful payments divided by all transactions.'" class="pi pi-question-circle cursor-pointer" style="color: #b5b5b5;margin-left: 10px;" />
            </div>
            <div class="display-item-amount">
              {{ Format.formatAmount(Data?.successful_payment) }}
              <Tag
                style="border-radius: 16px; background: #e1ffa9; color: #545454;font-weight: 500;margin-left: 5px;"
                :style="{ backgroundColor: Data?.successful_payment_rate.charAt(0) === '+' ? '#e1ffa9' : '#ffe3e8' }"
              >
                <div class="flex items-center gap-2 px-1">
                  <i
                    class="pi " style="color: #8bf160;font-weight: 700;"
                    :style="{ color: Data?.successful_payment_rate.charAt(0) === '+' ? '#8bf160' : '#ff5757' }"
                    :class="Data?.successful_payment_rate.charAt(0) === '+' ? 'pi-arrow-up' : 'pi-arrow-down'"
                  />
                  <span class="text-base">{{ Data?.successful_payment_rate.slice(1) }}</span>
                </div>
              </Tag>
            </div>
            <!-- <CardChart :chart-data="chartDataList[1]" chart-type="blue" height="180px" /> -->
          </div>
          <div class="display-item">
            <div class="item-title">
              Avg. Daily Transaction Count <i v-tooltip="'Total Transaction count divided by number of days since first transaction.'" class="pi pi-question-circle cursor-pointer" style="color: #b5b5b5;margin-left: 10px;" />
            </div>
            <div class="display-item-amount">
              {{ Format.formatAmount(Data?.average_daily_transaction_count) }}
              <Tag
                style="border-radius: 16px; background: #e1ffa9; color: #545454;font-weight: 500;margin-left: 5px;"
                :style="{ backgroundColor: Data?.average_daily_transaction_count_rate.charAt(0) === '+' ? '#e1ffa9' : '#ffe3e8' }"
              >
                <div class="flex items-center gap-2 px-1">
                  <i
                    class="pi" style="color: #8bf160;font-weight: 700;"
                    :class="Data?.average_daily_transaction_count_rate.charAt(0) === '+' ? 'pi-arrow-up' : 'pi-arrow-down'"
                    :style="{ color: Data?.average_daily_transaction_count_rate.charAt(0) === '+' ? '#8bf160' : '#ff5757' }"
                  />

                  <span class="text-base">{{ Data?.average_daily_transaction_count_rate.slice(1) }}</span>
                </div>
              </Tag>
            </div>
            <!-- <CardChart :chart-data="chartDataList[2]" chart-type="green" height="180px" /> -->
          </div>
          <div class="display-item">
            <div class="item-title ">
              Avg. Transaction Amount <i v-tooltip="'Total Transaction volume divided by Total count of transactions.'" class="pi pi-question-circle cursor-pointer" style="color: #b5b5b5;margin-left: 10px;" />
            </div>
            <div class="display-item-amount">
              {{ Format.formatAmount(Data?.average_transaction_amount) }}
              <Tag
                style="border-radius: 16px; background: #e1ffa9; color: #545454;font-weight: 500;margin-left: 5px;"
                :style="{ backgroundColor: Data?.average_transaction_amount_rate.charAt(0) === '+' ? '#e1ffa9' : '#ffe3e8' }"
              >
                <div class="flex items-center gap-2 px-1">
                  <i
                    class="pi " style="color: #8bf160;font-weight: 700;"
                    :class="Data?.average_transaction_amount_rate.charAt(0) === '+' ? 'pi-arrow-up' : 'pi-arrow-down'"
                    :style="{ color: Data?.average_transaction_amount_rate.charAt(0) === '+' ? '#8bf160' : '#ff5757' }"
                  />
                  <span class="text-base">{{ Data?.average_transaction_amount_rate.slice(1) }}</span>
                </div>
              </Tag>
            </div>
            <!-- <CardChart :chart-data="chartDataList[3]" chart-type="default" height="180px" /> -->
          </div>
        </template>
      </div>
    </div>
    <div class="merchant-common-page mt-8">
      <transition name="fade" appear>
        <div class="w-full max-w-8xl">
          <section>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div v-if="hasPermission(Permissions.HOME_GET_PAYMENT_DATA)" class="lg:col-span-2 ">
                <h2 class="text-xl font-semibold mb-4">
                  Overview
                </h2>
                <!-- flex gap-2 items-center -->
                <div class="mb-4 flex gap-2 items-center">
                  <div class="mr-6">
                    <EnhancedDatePicker
                      v-model="selectDate" :min-date="dayjs().toDate()" @change="handleDateChart"
                      @update:model-value="handleDateChart"
                    />
                  </div>
                  <div class="mr-6 customer-picker">
                    <Select
                      v-model="selectedPeriod" :options="periodOptions" option-label="name"
                      class=" bg-transparent" @change="handleChart"
                    >
                      <template #dropdownicon>
                        <i class="pi pi-sort-down-fill " style="color: #ff5f01;" />
                      </template>
                    </Select>
                  </div>

                  <div class="flex-auto">
                    Last updated 9 hours ago
                  </div>
                </div>
                <div class="bg-white rounded-lg p-6 h-[376px] shadow-sm hover:shadow-md transition-shadow">
                  <div class="mr-4 text-xl font-semibold text-[#545454]">
                    Collected Payments
                  </div>
                  <div class="w-full h-full">
                    <div v-if="loading" class="flex flex-col justify-center items-center h-[90%]">
                      <Skeleton width="90%" height="200px" />
                    </div>
                    <div v-else id="chart" style="width: 100%;height: 100%;" />
                  </div>
                </div>
              </div>

              <div v-if="hasPermission(Permissions.HOME_GET_SUM_DATA)" class="space-y-4">
                <div class="text-xl font-semibold mb-6">
                  Account health
                </div>
                <template v-if="loading">
                  <div
                    v-for="i in 3" :key="i"
                    class="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow"
                  >
                    <Skeleton width="180px" height="28px" />
                    <Skeleton width="80px" height="32px" class="my-2" />
                    <Skeleton width="140px" height="20px" />
                  </div>
                </template>
                <div
                  v-for="(item, index) in accountList" v-else :key="index"
                  class="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow"
                >
                  <div class="flex items-center gap-2 font-semibold text-xl">
                    {{ item.name }}
                  </div>
                  <div
                    class="font-semibold text-xl my-2 "
                    :class="accountAmountMapping[item.name] === 'active_customer' ? 'text-[#39b54a]' : 'text-[#eb001b]'"
                  >
                    <template v-if="accountAmountMapping[item.name] === 'active_customer' || accountAmountMapping[item.name] === 'risk_customer'">
                      <!-- Display as plain number for customer counts -->
                      <span class="text-2xl">{{ item.amount }}</span>
                    </template>
                    <template v-else>
                      <!-- Display with currency symbol split for amounts -->
                      <span class="text-2xl">{{ item.amount[0] }}</span>
                      <span>{{ item.amount.slice(1) }}</span>
                    </template>
                  </div>
                  <div class="text-primary-600 underline cursor-pointer" @click="handleViewAction(item.to)">
                    {{ item.detail }}
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </transition>
    </div>
    <!-- <div class="merchant-common-page mt-8">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-xl font-semibold">
            Transaction List
          </h2>
          <div class="grey-color">
            add description or subheading here
          </div>
        </div>
        <div v-if="hasPermission(Permissions.TRANS_LIST)" class="grey-color">
          <Button label="View Reports" variant="outlined" @click="$router.push({ name: 'transactionsList' })" />
        </div>
      </div>
    </div> -->
  </div>
</template>

<style scoped lang="scss">
@use '@/styles/mixins/breakpoints' as *;

.date-picker-wrapper {
  :deep(.p-datepicker) {
    width: 100%;

    @include media-breakpoint-down(lg) {
      width: 80%;
    }
  }
}

/* 淡入动画 */
.fade-enter-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from {
  opacity: 0;
}

/* 从下方淡入动画 */
.fade-up-enter-active {
  transition: all 0.5s ease;
  transition-delay: 0.2s;
}

.fade-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

/* 下载项目悬停效果 */
.download-item {
  transition: all 0.3s ease;
}

.download-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: rgba(0, 0, 0, 0.05);
}

/* 下载按钮动画 */
.download-button {
  transition: all 0.2s ease;
}

.download-button:hover {
  transform: scale(1.05);
}

.toolbar {
  border-bottom: 1px solid #eeeeee;
}

.fund-item {
  display: flex;
  flex-direction: column;
  width: 400px;
  height: 130px;
  background-color: #fff;
  border-radius: 10px;
  border: 1px solid #eeeeee;
  margin-right: 35px;
}

.funds-detail {
  font-weight: bold;
  text-decoration: underline;
}

.custom-select {
  background-color: #f9fcf4 !important;
  font-weight: 550 !important;
  box-shadow: none !important;
  border: none !important;
}

:deep(.custom-select .p-select-label) {
  padding: 0 !important;
}

:deep(.custom-select .p-select-dropdown) {
  color: var(--p-select-color) !important;
}

.overview-content {
  width: 830px;
  height: 500px;
  background-color: #fff;
  border-radius: 10px;
  border: 1px solid #eeeeee;
  padding: 20px;
}

.overview-tools {
  margin-bottom: 30px;
}

.account-health {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  height: 500px;
}

.account-item {
  display: flex;
  flex-direction: column;
  width: 400px;
  height: 150px;
  background-color: #fff;
  border-radius: 10px;
  border: 1px solid #eeeeee;
}

.welcome-title {
  font-size: 34px;
  font-weight: 900;
}

.tips {
  color: #545454;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.grey-color {
  color: #545454;
}

.border-style {
  border: 1px solid #545454;
}

.summary-item {
  border-radius: 22px;
}

.item-amount {
  font-weight: 700;
  font-size: 24px;
  margin-bottom: 8px;
}

.attributed-content {
  display: flex;
  justify-content: flex-start;
  color: #545454;
}

.attributed-item {
  height: 70px;
  width: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.attributed-item:not(:first-child) {
  padding-left: 30px;
}

.attributed-item:last-child {
  border-left: 1px solid #ebebeb;
}

.attributed-item:first-child {
  border-right: 1px solid #ebebeb;
}

.display-item {
  background-color: #fff;
  border-radius: 16px;
  padding: 1.5rem;
}

.item-title {
  color: #545454;
  font-weight: 700;
  font-size: 16px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.display-item-amount {
  color: #181349;
  font-weight: 700;
  font-size: 24px;
  margin-bottom: 20px;
  display: flex;
  align-items: flex-end;
}

:deep(.p-select-label) {
  color: #ff5f01;
}

:deep(.p-inputtext) {
  color: #ff5f01;
}
</style>
