<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import BaseDataTable from '@/components/common/BaseDataTable.vue'
import { useListRefresh } from '@/composables/useListRefresh'
import { useRequestList } from '@/composables/useRequestList'
import { accounting as accountingApi } from '@/services/api'
import { useCopy } from '@/utils/clipboard'
import { formatDate } from '@/utils/date'
import { toTypedSchema } from '@vee-validate/zod'
import { useToast } from 'primevue/usetoast'
import { Field, Form as VeeForm } from 'vee-validate'
import { reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { z } from 'zod'

defineOptions({
  name: 'accountingList',
})

const { t } = useI18n()
const toast = useToast()
const copy = useCopy()

// 列配置
const columns = ref<TableColumnItem[]>([
  { field: 'id', header: 'Accounting Code', style: { minWidth: '160px' } },
  { field: 'merchant_id', header: 'Merchant ID', style: { minWidth: '160px' } },
  { field: 'created_at', header: t('common.created'), template: 'created_at', sortable: true, style: { minWidth: '200px' } },
  { field: 'action', header: '', template: 'action', style: { width: '50px' } },
])

const {
  list,
  loading,
  total,
  refresh,
  setParams,
  search,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
  setSearchParams,
} = useRequestList<Accounting.Info[], Api.AccountingListReq>({
  requestFn: accountingApi.getList,
})

// 使用通用的列表刷新逻辑
useListRefresh('invoicesList', refresh)

const handleSort = (event: any) => {
  const { sortField, sortOrder } = event
  setSearchParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  search()
}

// 新增发票对话框控制
const addInvoiceDialog = ref(false)

// 编辑发票对话框控制
const editInvoiceDialog = ref(false)

// 新增配置对话框控制
const addConfigDialog = ref(false)

// 表单提交状态
const isSubmitting = ref(false)

// 表单验证架构
const schema = toTypedSchema(z.object({
  account_code: z.string().min(1, { message: 'Account Code is required' }),
}))

// 配置表单验证架构
const configSchema = toTypedSchema(z.object({
  client_id: z.string().min(1, { message: 'Client ID is required' }),
  client_secret: z.string().min(1, { message: 'Client Secret is required' }),
}))

// 表单数据
const invoiceForm = reactive<{
  id: number | null
  account_code: string
}>({
  id: null,
  account_code: '',
})

// 配置表单数据
const configForm = reactive<{
  client_id: string
  client_secret: string
}>({
  client_id: '',
  client_secret: '',
})

// 打开新增发票对话框
const openAddInvoiceDialog = () => {
  // 重置表单
  invoiceForm.account_code = ''
  addInvoiceDialog.value = true
}

// 打开新增配置对话框
const openAddConfigDialog = () => {
  // 重置表单
  configForm.client_id = ''
  configForm.client_secret = ''
  addConfigDialog.value = true
}

// 打开编辑发票对话框
const openEditInvoiceDialog = (data: Accounting.Info) => {
  invoiceForm.id = data.id as number
  invoiceForm.account_code = data.config.account_code || ''
  editInvoiceDialog.value = true
}

const copyLink = (data: any) => {
  const url = `${location.origin}/merchant/accounting/payment/${data.invoice_token}`
  copy(url)
}

// 提交表单
const onSubmit = (values: Record<string, unknown>) => {
  isSubmitting.value = true
  accountingApi.create(values as unknown as Api.CreateAccountingReq).then((res) => {
    if (res.code === 0) {
      toast.add({ severity: 'success', summary: t('common.success'), detail: 'Invoice created successfully', life: 3000 })
      addInvoiceDialog.value = false
      refresh()
    }
    isSubmitting.value = false
  }).catch(() => {
    isSubmitting.value = false
  })
}

// 提交配置表单
const onConfigSubmit = (values: Record<string, unknown>) => {
  isSubmitting.value = true
  // 获取当前URL作为重定向地址
  const redirect_uri = window.location.origin + window.location.pathname

  accountingApi.createConfig({
    ...values,
    redirect_uri,
  } as Api.CreateAccountingConfigReq).then((res) => {
    if (res.code === 0) {
      if (res.data.url) {
        location.href = res.data.url
      }
      toast.add({ severity: 'success', summary: t('common.success'), detail: 'Configuration created successfully', life: 3000 })
      addConfigDialog.value = false
      refresh()
    }
    isSubmitting.value = false
  }).catch(() => {
    isSubmitting.value = false
  })
}

// 更新表单提交
const onUpdateSubmit = async () => {
  isSubmitting.value = true
  try {
    const { code } = await accountingApi.update({
      id: invoiceForm.id as number,
      account_code: invoiceForm.account_code,
      client_id: '', // 添加必要的字段
      client_secret: '', // 添加必要的字段
    })
    if (code === 0) {
      toast.add({ severity: 'success', summary: t('common.success'), detail: 'Invoice updated successfully', life: 3000 })
      editInvoiceDialog.value = false
      refresh()
    }
    isSubmitting.value = false
  }
  catch {
    isSubmitting.value = false
  }
}

// 搜索处理
const handleSearch = (params: Record<keyof Api.AccountingListReq, any>) => {
  setParams({ ...params })
  search()
}
</script>

<template>
  <div class="accounting-page">
    <div class="flex items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8">
      <Button label="Add Config" icon="pi pi-plus" @click="openAddConfigDialog" />
      <Button label="Add Accounting" icon="pi pi-plus" @click="openAddInvoiceDialog" />
    </div>

    <!-- 发票表格 -->
    <BaseDataTable
      :show-search-bar="false"
      :value="list" :columns="columns" :scrollable="true" :show-multiple-column="false"
      :loading="loading" :paginator="true" :rows="50" :total-records="total" :lazy="true" data-key="id"
      :failed="failed" :failure-message="failureMessage" :striped-rows="true"
      @change-search="handleSearch"
      @page="(e: DataTablePageEvent) => handlePageChange(e)"
      @sort="handleSort"
    >
      <template #action="{ data }">
        <BaseDataTableActions>
          <template #default>
            <div class="flex gap-2">
              <Button severity="secondary" label="Edit" @click="openEditInvoiceDialog(data)" />
              <Button severity="secondary" label="Copy Link" @click="copyLink(data)" />
            </div>
          </template>
        </BaseDataTableActions>
      </template>
      <template #created_at="{ data }">
        {{ formatDate(data.created_at) }}
      </template>
    </BaseDataTable>

    <!-- 新增发票对话框 -->
    <Dialog
      v-model:visible="addInvoiceDialog"
      modal
      header="Add Accounting"
      :style="{ width: '500px' }"
      :closable="true"
    >
      <VeeForm :validation-schema="schema" class="flex flex-col gap-4" @submit="onSubmit">
        <Field v-slot="{ field, errorMessage }" v-model="invoiceForm.account_code" as="div" class="mb-2" name="account_code">
          <label for="account_code" class="mb-2 block">Account Code</label>
          <InputText id="account_code" class="w-full" v-bind="field" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>
        <div class="flex justify-end gap-2 mt-4">
          <Button type="button" label="Cancel" severity="secondary" @click="addInvoiceDialog = false" />
          <Button type="submit" label="Submit" :loading="isSubmitting" />
        </div>
      </VeeForm>
    </Dialog>

    <!-- 新增配置对话框 -->
    <Dialog
      v-model:visible="addConfigDialog"
      modal
      header="Add Configuration"
      :style="{ width: '500px' }"
      :closable="true"
    >
      <VeeForm :validation-schema="configSchema" class="flex flex-col gap-4" @submit="onConfigSubmit">
        <Field v-slot="{ field, errorMessage }" v-model="configForm.client_id" as="div" class="mb-2" name="client_id">
          <label for="config_client_id" class="mb-2 block">Client ID</label>
          <InputText id="config_client_id" class="w-full" v-bind="field" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>
        <Field v-slot="{ field, errorMessage }" v-model="configForm.client_secret" as="div" class="mb-2" name="client_secret">
          <label for="config_client_secret" class="mb-2 block">Client Secret</label>
          <InputText id="config_client_secret" class="w-full" v-bind="field" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>
        <div class="flex justify-end gap-2 mt-4">
          <Button type="button" label="Cancel" severity="secondary" @click="addConfigDialog = false" />
          <Button type="submit" label="Submit" :loading="isSubmitting" />
        </div>
      </VeeForm>
    </Dialog>

    <!-- 编辑发票对话框 -->
    <Dialog
      v-model:visible="editInvoiceDialog"
      modal
      header="Edit Accounting"
      :style="{ width: '500px' }"
      :closable="true"
    >
      <VeeForm :validation-schema="schema" class="flex flex-col gap-4" @submit="onUpdateSubmit">
        <div class="mb-2">
          <label for="invoice_id" class="mb-2 block">Accounting ID</label>
          <InputText id="invoice_id" class="w-full" :value="invoiceForm.id" disabled />
        </div>
        <Field v-slot="{ field, errorMessage }" v-model="invoiceForm.account_code" as="div" class="mb-2" name="account_code">
          <label for="edit_account_code" class="mb-2 block">Account Code</label>
          <InputText id="edit_account_code" class="w-full" v-bind="field" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>
        <div class="flex justify-end gap-2 mt-4">
          <Button type="button" label="Cancel" severity="secondary" @click="editInvoiceDialog = false" />
          <Button type="submit" label="Update" :loading="isSubmitting" />
        </div>
      </VeeForm>
    </Dialog>
  </div>
</template>

<style scoped>
.confirmation-content {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
