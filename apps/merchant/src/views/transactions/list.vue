<script setup lang="ts">
import type { DictItem } from '@/services/api/dict'
import { Format } from '@shared'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import BaseDataTable from '@/components/common/BaseDataTable.vue'
import { useDict } from '@/composables/useDict'
import { useExport } from '@/composables/useExport'
import { usePermissions } from '@/composables/usePermissions'
import { useRequestList } from '@/composables/useRequestList'
import { Permissions } from '@/constants/permissions'
import { SearchFieldType } from '@/constants/search'
import { TransactionStatus, TransactionType } from '@/constants/transaction'
import { transactions as transactionApi } from '@/services/api'
import { formatDate } from '@/utils/date'
import { addAllToDict } from '@/utils/dict'

defineOptions({
  name: 'TransactionList',
})

const { hasPermission } = usePermissions()

// 使用 useRequestList 处理交易列表数据
const requestList = useRequestList<Transaction.Info[], Api.TransactionListReq>({
  requestFn: transactionApi.getList,
})

const router = useRouter()
const route = useRoute()

const {
  list,
  loading,
  total,
  refresh,
  search,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
  other,
  setSearchParams,
} = requestList

// 列配置
const columns = ref<TableColumnItem[]>([
  {
    field: 'payment_amount',
    header: 'Amount',
    style: { minWidth: '110px' },
    template: 'payment_amount',
    sortable: true,
  },
  {
    field: 'status',
    header: 'Status',
    style: { minWidth: '100px' },
    template: 'status',
  },
  { field: 'payment_method', header: 'Payment Method', template: 'payment_method', style: { minWidth: '120px' } },
  { field: 'trans_invoice_number', header: 'TXN Invoice Number', style: { minWidth: '120px' } },
  { field: 'remark', header: 'Description', style: { minWidth: '150px' } },
  { field: 'customer', header: 'Customer', template: 'customer', style: { minWidth: '150px' } },
  {
    field: 'date',
    header: 'Date',
    style: { minWidth: '150px' },
    sortable: true,
    template: 'date',
    sortField: 'date',
  },
  {
    field: 'refunded_date',
    header: 'Refunded date',
    style: { minWidth: '150px' },
    sortable: true,
    template: 'refunded_date',
  },
])

const searchModel = ref<Api.TransactionListReq>({
  'status': null,
  'amount': '',
  'created_at[]': [],
  'trans_type': null,
  'trans_invoice_number': '',
  'description': '',
  'credit_brand': '',
  'refunded_date[]': [],
})

const statusOptions = ref<DictItem[]>([])

const statusFilterOptions = ref<DictItem[]>([])

const paymentTypeOptions = ref<DictItem[]>([])

const dateRange = ref<string[]>([])

const searchFields = computed(() => [
  {
    name: 'keyword',
    label: 'Search Transactions',
    type: SearchFieldType.TEXT,
    placeholder: 'Search for a transaction',
    defaultValue: '',
  },
  {
    name: 'status',
    label: 'Status',
    type: SearchFieldType.SELECT,
    placeholder: 'All',
    options: statusFilterOptions.value,
    defaultValue: '',
  },
])

const moreSearchFields = computed(() => [
  {
    name: 'trans_invoice_number',
    label: 'TXN Invoice Number',
    type: SearchFieldType.TEXT,
    placeholder: 'Search for a TXN Invoice Number',
    defaultValue: '',
  },
  {
    name: 'description',
    label: 'Description',
    type: SearchFieldType.TEXT,
    placeholder: 'Search for a description',
    defaultValue: '',
  },
  {
    name: 'credit_brand',
    label: 'Payment Type',
    type: SearchFieldType.SELECT,
    placeholder: 'All',
    options: paymentTypeOptions.value,
    defaultValue: '',
  },
  {
    name: 'created_at[]',
    label: 'Date',
    type: SearchFieldType.DATE_RANGE,
    placeholder: 'Select Date Range',
    defaultValue: dateRange.value,
  },
  {
    name: 'refunded_date[]',
    label: 'Refunded Date',
    type: SearchFieldType.DATE_RANGE,
    placeholder: 'Select Refunded Date Range',
    defaultValue: dateRange.value,
  },
])

// 搜索处理
const handleSearch = () => {
  setSearchParams(searchModel.value, ['refunded_date[]' as keyof Transaction.Info[]])
  search()
}

// 手动刷新数据
const refreshData = () => {
  refresh()
}

// 排序处理
const handleSort = (event: any) => {
  const { sortField, sortOrder } = event
  setSearchParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  search()
}

// Setup export functionality
const { isExporting, handleExport } = useExport({
  exportFn: transactionApi.exportTransactions,
  getParams: () => {
    return setSearchParams(searchModel.value)
  },
  onExportStart: () => {
    window.$toast.add({
      severity: 'info',
      summary: 'Export Started',
      detail: 'Preparing your export file...',
      life: 3000,
    })
  },
})

const detail = ({ data }: { data: Transaction.Info }) => {
  if (!hasPermission(Permissions.TRANS_DETAIL)) {
    return
  }
  router.push({
    name: 'transactionsDetail',
    params: {
      id: data.id,
    },
  })
}

const { getLabel: getTransStatusLabel } = useDict('trans_status', (res) => {
  statusOptions.value = res
  statusFilterOptions.value = addAllToDict(res, { label: 'All', value: null }).filter(item => item.value !== 3)
})

useDict('credit_brand', (res) => {
  paymentTypeOptions.value = addAllToDict(res)
})

const handleStatCardClick = (item: { status: number, status_text: string, count: number }) => {
  if (searchModel.value.status === item.status) {
    return
  }
  searchModel.value.status = item.status
  setSearchParams(searchModel.value)
  refresh()
}

onMounted(() => {
  const query = route.query
  if (query && Object.keys(query).length > 0) {
    searchModel.value = {
      'status': query.status ? Number(query.status) : null,
      'amount': '',
      'created_at[]': Array.isArray(query['created_at[]']) ? query['created_at[]'] as string[] : [],
      'trans_type': query.trans_type ? Number(query.trans_type) : null,
      'trans_invoice_number': query.trans_invoice_number as string,
      'description': query.description as string,
      'credit_brand': query.credit_brand as string,
      'refunded_date[]': Array.isArray(query['refunded_date[]']) ? query['refunded_date[]'] as string[] : [],
    }
    setSearchParams(searchModel.value)
    refresh()
  }
  else {
    searchModel.value = {
      'status': null,
      'amount': '',
      'created_at[]': [],
      'trans_type': null,
      'trans_invoice_number': '',
      'description': '',
      'credit_brand': '',
      'refunded_date[]': [],
    }
    setSearchParams(searchModel.value)
    refresh()
  }
})
</script>

<template>
  <div>
    <div v-if="other?.stat" class="common-stat-card">
      <div
        v-for="(item, index) in other?.stat" :key="index" class="common-stat-card__item"
        :class="{ active: searchModel.status === item.status }" @click="handleStatCardClick(item)"
      >
        <div class="common-stat-card__title">
          {{ item.status_text }}
        </div>
        <div class="common-stat-card__count">
          {{ item.count }}
        </div>
      </div>
    </div>

    <BaseSearch
      v-model="searchModel" :loading="loading" :basic-search-fields="searchFields"
      :advanced-search-fields="moreSearchFields" @search="handleSearch"
    />

    <div class="flex items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8">
      <BaseExportDialog
        v-if="hasPermission(Permissions.TRANS_EXPORT)"
        :loading="loading"
        :export-loading="isExporting"
        @export="handleExport"
      />
    </div>

    <BaseDataTable
      v-if="hasPermission(Permissions.TRANS_LIST)"
      :row-hover="hasPermission(Permissions.TRANS_DETAIL)" :value="list" :columns="columns" :show-edit-column="false"
      :show-search-bar="false" :scrollable="true" :show-multiple-column="false" :loading="loading" :paginator="true"
      :rows="50" :total-records="total" :lazy="true" data-key="id" sort-mode="single"
      :sort-field="$route.query.sort_by as string" :sort-order="$route.query.sort_order === 'desc' ? -1 : 1"
      search-placeholder="Search" type-placeholder="Filter By" :failed="failed" :failure-message="failureMessage"
      :striped-rows="true" @page="handlePageChange" @sort="handleSort" @refresh="refreshData" @row-click="detail"
    >
      <template #payment_amount="{ data }">
        {{ Format.formatAmount(data?.payment_amount) }}
      </template>
      <template #date="{ data }">
        <span v-if="data?.trans_type === TransactionType.Payment">
          {{ formatDate(data.created_at) }}
        </span>
      </template>
      <template #refunded_date="{ data }">
        <span v-if="data?.trans_type === TransactionType.Refund">
          {{ formatDate(data.created_at) }}
        </span>
      </template>
      <template #payment_method="{ data }">
        <BaseCardType
          :text="data.customer_banking?.account_no" :card-type="data.customer_banking?.credit_brand"
          :is-show-card-number="true"
        />
      </template>
      <template #customer="{ data }">
        <span v-if="data?.customer?.name" class="w-30">
          {{ data?.customer?.name }}
        </span>
      </template>
      <template #status="{ data }">
        <BaseTag
          :type="(data.status === TransactionStatus.SUCCEEDED ? 'paid'
            : data.status === TransactionStatus.FAILED ? 'failed' : 'upcoming')"
          :text="getTransStatusLabel(data.status)" class="w-30"
        />
      </template>
    </BaseDataTable>

    <!-- 无权限提示 -->
    <div v-else class="flex justify-center items-center h-64 bg-white rounded-lg">
      <p class="text-gray-500">
        You don't have permission to view the transaction list.
      </p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.card {
  padding: 1rem;
}

.p-button {
  padding: 0.5rem 1rem;
  font-weight: 500;
}

.p-button-text {
  color: var(--text-color-secondary);
}

.p-button-text:hover {
  background: var(--surface-hover);
  color: var(--text-color);
}
</style>
