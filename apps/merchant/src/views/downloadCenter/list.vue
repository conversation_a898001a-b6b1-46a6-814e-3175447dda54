<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import type { DictItem } from '@/services/api/dict'
import { useToast } from 'primevue/usetoast'
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import BaseDataTable from '@/components/common/BaseDataTable.vue'
import { useDict } from '@/composables/useDict'
import { useListRefresh } from '@/composables/useListRefresh'
import { useRequestList } from '@/composables/useRequestList'
import { SearchFieldType } from '@/constants/search'
import { downloadCenter as downloadCenterApi } from '@/services/api'
import { formatDate } from '@/utils/date'
import { addAllToDict } from '@/utils/dict'
import { downloadFile as downloadFileUtil } from '@/utils/download'
import { getDownloadCenterTagStatus } from '@/utils/tagStatus'

defineOptions({
  name: 'downloadCenterList',
})

const { t } = useI18n()
const toast = useToast()

// 列配置
const columns = ref<TableColumnItem[]>([
  { field: 'id', header: 'ID', style: { minWidth: '100px' } },
  { field: 'file_name', header: 'File Name', style: { minWidth: '200px' } },
  { field: 'status', header: 'Status', template: 'status', style: { minWidth: '120px' } },
  { field: 'created_at', header: t('common.created'), template: 'created_at', sortable: true, style: { minWidth: '200px' } },
  {
    field: 'action',
    header: '',
    template: 'action',
    alignFrozen: 'right',
    frozen: true,
    style: { width: '50px' },
  },
])

const {
  list,
  loading,
  total,
  refresh,
  setSearchParams,
  search,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
} = useRequestList<DownloadCenter.Info[], Api.DownloadCenterListReq>({
  requestFn: downloadCenterApi.getList,
})

// 使用通用的列表刷新逻辑
useListRefresh('downloadCenterList', refresh)

const handleSort = (event: any) => {
  const { sortField, sortOrder } = event
  setSearchParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  search()
}

// 搜索模型，用于存储当前搜索值
const searchModel = ref<Partial<Api.DownloadCenterListReq>>({
  'status': '',
  'created_at[]': [],
})

const downloadStatusOptions = ref<DictItem[]>([])

// 搜索字段配置
const searchFields = computed(() => [
  {
    name: 'created_at[]',
    label: t('common.created'),
    type: SearchFieldType.DATE_RANGE,
    placeholder: 'Select Date Range',
    defaultValue: [],
  },

  {
    name: 'status',
    label: 'Status',
    type: SearchFieldType.SELECT,
    placeholder: 'All',
    options: downloadStatusOptions.value,
    defaultValue: '',
  },
])

// 确认删除对话框控制
const deleteDialog = ref(false)

// 当前选中的文件
const selectedFile = ref<Partial<DownloadCenter.Info>>({})

// 搜索处理
const handleSearch = () => {
  setSearchParams(searchModel.value)
  search()
}

// 下载文件
const downloadFile = (data: DownloadCenter.Info) => {
  downloadFileUtil(data.url, data.file_name)
}

// 确认删除文件
// const confirmDeleteFile = (file: DownloadCenter.Info) => {
//   selectedFile.value = file
//   deleteDialog.value = true
// }

// 删除文件
const deleteFile = () => {
  if (!selectedFile.value.id) { return }

  downloadCenterApi.remove(selectedFile.value.id.toString())
    .then((res) => {
      if (res.code === 0) {
        toast.add({ severity: 'success', summary: t('common.success'), detail: 'File deleted successfully', life: 3000 })
        deleteDialog.value = false
        refresh()
      }
      else {
        toast.add({ severity: 'error', summary: t('common.error'), detail: res.message || 'Failed to delete file', life: 3000 })
      }
    })
    .catch((err) => {
      toast.add({ severity: 'error', summary: t('common.error'), detail: err.message || 'Failed to delete file', life: 3000 })
    })
}

// 获取下载状态字典
const { getLabel: getDownloadStatusLabel } = useDict('download_status', (res) => {
  downloadStatusOptions.value = addAllToDict(res, { label: 'All', value: '' })
})
</script>

<template>
  <div class="download-center-page">
    <!-- 搜索表单 -->
    <BaseSearch v-model="searchModel" :loading="loading" :basic-search-fields="searchFields" @search="handleSearch" />

    <!-- 文件表格 -->
    <BaseDataTable
      :value="list" :columns="columns" :scrollable="true" :show-multiple-column="false"
      :loading="loading" :paginator="true" :rows="50" :total-records="total" :lazy="true" data-key="id"
      :show-search-bar="false" :failed="failed" :failure-message="failureMessage" :striped-rows="true"
      @page="(e: DataTablePageEvent) => handlePageChange(e)" @sort="handleSort"
    >
      <template #created_at="{ data }">
        {{ formatDate(data.created_at) }}
      </template>
      <template #status="{ data }">
        <BaseTag :text="getDownloadStatusLabel(data.status)" :type="getDownloadCenterTagStatus(data.status)" />
      </template>
      <template #action="{ data }">
        <Button severity="secondary" icon="pi pi-download" @click="downloadFile(data)" />
        <!-- <BaseDataTableActions>
            <template #default>
              <div class="flex gap-2">
                <Button severity="secondary" icon="pi pi-download" label="Download" @click="downloadFile(data)" />
                <Button severity="danger" icon="pi pi-trash" label="Delete" @click="confirmDeleteFile(data)" />
              </div>
            </template>
</BaseDataTableActions> -->
      </template>
    </BaseDataTable>

    <!-- 删除确认对话框 -->
    <Dialog v-model:visible="deleteDialog" modal header="Confirm Delete" :style="{ width: '450px' }">
      <div class="confirmation-content">
        <i class="pi pi-exclamation-triangle mr-3" style="font-size: 2rem" />
        <span>Are you sure you want to delete this file?</span>
      </div>
      <template #footer>
        <Button label="No" icon="pi pi-times" severity="secondary" @click="deleteDialog = false" />
        <Button label="Yes" icon="pi pi-check" severity="danger" @click="deleteFile" />
      </template>
    </Dialog>
  </div>
</template>

<style lang="scss" scoped>
.confirmation-content {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
