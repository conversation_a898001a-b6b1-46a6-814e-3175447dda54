<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import type { DictItem } from '@/services/api/dict'
import { toTypedSchema } from '@vee-validate/zod'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { Field, Form as VeeForm } from 'vee-validate'
import { computed, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { z } from 'zod'
import { useDict } from '@/composables/useDict'
import { useListRefresh } from '@/composables/useListRefresh'
import { useRequestList } from '@/composables/useRequestList'
import { SearchFieldType } from '@/constants/search'
import { account as accountApi } from '@/services/api'
import { formatDate } from '@/utils/date'

defineOptions({
  name: 'accountList',
})

const { t } = useI18n()
const toast = useToast()
const confirm = useConfirm()

// Column configuration
const columns = ref<TableColumnItem[]>([
  { field: 'id', header: 'ID', style: { width: '80px' } },
  { field: 'account_code', header: 'Account Code', style: { minWidth: '120px' } },
  { field: 'name', header: 'Name', style: { minWidth: '120px' } },
  { field: 'type', header: 'Type', style: { minWidth: '100px' } },
  { field: 'status', header: 'Status', style: { minWidth: '100px' }, template: 'status' },
  { field: 'bank_account_number', header: 'Bank Account Number', style: { minWidth: '150px' } },
  { field: 'bank_account_type', header: 'Bank Account Type', style: { minWidth: '120px' } },
  { field: 'created_at', header: t('common.created', 'Created Date'), template: 'created_at', sortable: true, style: { minWidth: '180px' } },
  { field: 'action', header: '', template: 'action', style: { width: '50px' }, alignFrozen: 'right', frozen: true },
])

const {
  list,
  loading,
  total,
  refresh,
  search,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
  setSearchParams,
} = useRequestList<Account.Info[], Api.AccountListReq>({
  requestFn: accountApi.getAccountList,
})

// Use common list refresh logic
useListRefresh('accountList', refresh)

// Get status severity
const getStatusSeverity = (status: string | undefined) => {
  if (!status) { return 'upcoming' }

  const severityMap: Record<string, string> = {
    ACTIVE: 'paid',
    ARCHIVED: 'failed',
    INACTIVE: 'warning',
  }

  return severityMap[status] || 'upcoming'
}

const handleSort = (event: any) => {
  const { sortField, sortOrder } = event
  setSearchParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  search()
}

// Search model
const searchModel = ref<Partial<Api.AccountListReq>>({
  name: '',
})

// Search fields configuration
const searchFields = computed(() => [
  {
    name: 'name',
    label: 'Name',
    type: SearchFieldType.TEXT,
    placeholder: 'Search by name',
    maxlength: 50,
    defaultValue: '',
  },
  {
    name: 'account_code',
    label: 'Account Code',
    type: SearchFieldType.TEXT,
    placeholder: 'Search by account code',
    maxlength: 50,
    defaultValue: '',
  },
])

// Add account dialog control
const addAccountDialog = ref(false)

// Edit account dialog control
const editAccountDialog = ref(false)

// View account details dialog control
const accountDetailDialog = ref(false)

// Form submission status
const isSubmitting = ref(false)

const xeroAccountTypeOptions = ref<DictItem[]>([])

// Add account form validation schema
const addSchema = toTypedSchema(z.object({
  account_code: z.string().min(1, { message: 'Account code is required' }),
  name: z.string().min(1, { message: 'Name is required' }),
  type: z.string().min(1, { message: 'Please select a type' }),
}))

// Edit account form validation schema
const editSchema = toTypedSchema(z.object({
  account_code: z.string().min(1, { message: 'Account code is required' }),
  name: z.string().min(1, { message: 'Name is required' }),
  type: z.string().min(1, { message: 'Please select a type' }),
}))

// Add account form data
const accountForm = reactive<{
  account_code: string
  name: string
  type: string
}>({
  account_code: '',
  name: '',
  type: '',
})

// Edit account form data
const editForm = reactive<{
  id: number | null
  name: string
  type: string
  account_code: string
}>({
  id: null,
  name: '',
  type: '',
  account_code: '',
})

// Account detail data
const accountDetail = ref<Account.Info | null>(null)

// Open add account dialog
const openAddAccountDialog = () => {
  // Reset form
  accountForm.account_code = ''
  accountForm.name = ''
  accountForm.type = ''
  addAccountDialog.value = true
}

// Open edit account dialog
const openEditAccountDialog = (data: Account.Info) => {
  editForm.id = data.id
  editForm.name = data.name
  editForm.type = data.type
  editForm.account_code = data.account_code
  editAccountDialog.value = true
}

// Open account detail dialog
const openAccountDetailDialog = (data: Account.Info) => {
  isSubmitting.value = true
  accountApi.getAccountDetail({ id: data.id }).then((res: any) => {
    if (res.code === 0) {
      accountDetail.value = res.data
      accountDetailDialog.value = true
    }
    isSubmitting.value = false
  }).catch(() => {
    isSubmitting.value = false
  })
}

// Delete account
const deleteAccount = (data: Account.Info) => {
  confirm.require({
    message: 'Are you sure you want to delete this account?',
    header: 'Delete Confirmation',
    icon: 'pi pi-exclamation-triangle',
    acceptClass: 'p-button-danger',
    accept: () => {
      isSubmitting.value = true
      accountApi.deleteAccount({ id: data.id }).then((res: any) => {
        if (res.code === 0) {
          toast.add({ severity: 'success', summary: t('common.success', 'Success'), detail: 'Account deleted successfully', life: 3000 })
          refresh()
        }
        isSubmitting.value = false
      }).catch(() => {
        isSubmitting.value = false
      })
    },
  })
}

// Submit add account form
const onSubmit = (values: Record<string, unknown>) => {
  isSubmitting.value = true
  accountApi.addAccount(values as unknown as Api.AddAccountReq).then((res: any) => {
    if (res.code === 0) {
      toast.add({ severity: 'success', summary: t('common.success', 'Success'), detail: 'Account created successfully', life: 3000 })
      addAccountDialog.value = false
      refresh()
    }
    isSubmitting.value = false
  }).catch(() => {
    isSubmitting.value = false
  })
}

// Submit update account form
const onUpdateSubmit = async () => {
  isSubmitting.value = true
  try {
    const response = await accountApi.updateAccount({
      id: editForm.id as number,
      name: editForm.name,
      type: editForm.type,
      account_code: editForm.account_code,
    })

    if (response.code === 0) {
      toast.add({ severity: 'success', summary: t('common.success', 'Success'), detail: 'Account updated successfully', life: 3000 })
      editAccountDialog.value = false
      refresh()
    }
    isSubmitting.value = false
  }
  catch {
    isSubmitting.value = false
  }
}

// Search handler
const handleSearch = () => {
  setSearchParams(searchModel.value)
  search()
}

useDict('xero_account_type', (res) => {
  xeroAccountTypeOptions.value = res
})
</script>

<template>
  <div class="account-page">
    <!-- Search bar -->
    <BaseSearch
      v-model="searchModel"
      :loading="loading"
      :basic-search-fields="searchFields"
      @search="handleSearch"
    />

    <div class="flex items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8">
      <Button label="Add Account" icon="pi pi-plus" @click="openAddAccountDialog" />
    </div>

    <!-- Accounts table -->
    <BaseDataTable
      :show-search-bar="false"
      :value="list" :columns="columns" :scrollable="true" :show-multiple-column="false"
      :loading="loading" :paginator="true" :rows="20" :total-records="total" :lazy="true" data-key="id"
      :failed="failed" :failure-message="failureMessage" :striped-rows="true"
      search-placeholder="Search accounts..."
      @page="(e: DataTablePageEvent) => handlePageChange(e)"
      @sort="handleSort"
    >
      <template #action="{ data }">
        <BaseDataTableActions content-width="120px">
          <template #default>
            <Button icon="pi pi-eye" text title="View Details" @click="openAccountDetailDialog(data)" />
            <Button icon="pi pi-pencil" text title="Edit" @click="openEditAccountDialog(data)" />
            <Button icon="pi pi-trash" text severity="danger" title="Delete" @click="deleteAccount(data)" />
          </template>
        </BaseDataTableActions>
      </template>
      <template #status="{ data }">
        <BaseTag :text="data.status" :type="getStatusSeverity(data.status)" />
      </template>
      <template #created_at="{ data }">
        {{ formatDate(data.created_at) }}
      </template>
    </BaseDataTable>

    <!-- Add account dialog -->
    <Dialog
      v-model:visible="addAccountDialog"
      modal
      header="Add Account"
      :style="{ width: '500px' }"
      :closable="true"
    >
      <VeeForm :validation-schema="addSchema" class="flex flex-col gap-4" @submit="onSubmit">
        <Field v-slot="{ field, errorMessage }" v-model="accountForm.account_code" as="div" class="mb-2" name="account_code">
          <label for="account_code" class="mb-2 block">Account Code</label>
          <InputText id="account_code" class="w-full" v-bind="field" placeholder="e.g., test001" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>
        <Field v-slot="{ field, errorMessage }" v-model="accountForm.name" as="div" class="mb-2" name="name">
          <label for="name" class="mb-2 block">Name</label>
          <InputText id="name" class="w-full" v-bind="field" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>

        <Field v-slot="{ errorMessage }" :model-value="accountForm.type" as="div" class="mb-2" name="type">
          <label for="type" class="mb-2 block">Type</label>
          <Select id="type" v-model="accountForm.type" class="w-full" :options="xeroAccountTypeOptions" option-label="label" option-value="value" placeholder="Select type" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>
        <div class="flex justify-end gap-2 mt-4">
          <Button type="button" label="Cancel" severity="secondary" @click="addAccountDialog = false" />
          <Button type="submit" label="Submit" :loading="isSubmitting" />
        </div>
      </VeeForm>
    </Dialog>

    <!-- Edit account dialog -->
    <Dialog
      v-model:visible="editAccountDialog"
      modal
      header="Edit Account"
      :style="{ width: '500px' }"
      :closable="true"
    >
      <VeeForm :validation-schema="editSchema" class="flex flex-col gap-4" @submit="onUpdateSubmit">
        <Field v-slot="{ field, errorMessage }" v-model="editForm.account_code" as="div" class="mb-2" name="account_code">
          <label for="account_code" class="mb-2 block">Account Code</label>
          <InputText id="account_code" class="w-full" v-bind="field" placeholder="e.g., test001" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>
        <Field v-slot="{ field, errorMessage }" v-model="editForm.name" as="div" class="mb-2" name="name">
          <label for="edit_name" class="mb-2 block">Name</label>
          <InputText id="edit_name" class="w-full" v-bind="field" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>
        <Field v-slot="{ errorMessage }" :model-value="editForm.type" as="div" class="mb-2" name="type">
          <label for="edit_type" class="mb-2 block">Type</label>
          <Select id="edit_type" v-model="editForm.type" class="w-full" :options="xeroAccountTypeOptions" option-label="label" option-value="value" placeholder="Select type" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>
        <div class="flex justify-end gap-2 mt-4">
          <Button type="button" label="Cancel" severity="secondary" @click="editAccountDialog = false" />
          <Button type="submit" label="Update" :loading="isSubmitting" />
        </div>
      </VeeForm>
    </Dialog>

    <!-- Account details dialog -->
    <Dialog
      v-model:visible="accountDetailDialog"
      modal
      header="Account Details"
      :style="{ width: '600px' }"
      :closable="true"
    >
      <div v-if="accountDetail" class="p-3">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-y-6 gap-x-4">
          <div>
            <h3 class="text-gray-600 text-sm font-medium mb-1">
              Account Code
            </h3>
            <div class="text-base">
              {{ accountDetail.account_code }}
            </div>
          </div>
          <div>
            <h3 class="text-gray-600 text-sm font-medium mb-1">
              Name
            </h3>
            <div class="text-base">
              {{ accountDetail.name }}
            </div>
          </div>
          <div>
            <h3 class="text-gray-600 text-sm font-medium mb-1">
              Type
            </h3>
            <div class="text-base">
              {{ accountDetail.type }}
            </div>
          </div>
          <div>
            <h3 class="text-gray-600 text-sm font-medium mb-1">
              Status
            </h3>
            <div class="mt-1">
              <BaseTag :text="accountDetail.status" :type="getStatusSeverity(accountDetail.status)" />
            </div>
          </div>
          <div>
            <h3 class="text-gray-600 text-sm font-medium mb-1">
              Account ID
            </h3>
            <div class="font-mono break-all">
              {{ accountDetail.account_id }}
            </div>
          </div>
          <div>
            <h3 class="text-gray-600 text-sm font-medium mb-1">
              Tenant ID
            </h3>
            <div class=" font-mono break-all">
              {{ accountDetail.tenant_id }}
            </div>
          </div>
          <div>
            <h3 class="text-gray-600 text-sm font-medium mb-1">
              Bank Account Number
            </h3>
            <div class="text-base">
              {{ accountDetail.bank_account_number || '-' }}
            </div>
          </div>
          <div>
            <h3 class="text-gray-600 text-sm font-medium mb-1">
              Bank Account Type
            </h3>
            <div class="text-base">
              {{ accountDetail.bank_account_type || '-' }}
            </div>
          </div>
          <div>
            <h3 class="text-gray-600 text-sm font-medium mb-1">
              Created Date
            </h3>
            <div class="text-base">
              {{ formatDate(accountDetail.created_at) }}
            </div>
          </div>
          <div>
            <h3 class="text-gray-600 text-sm font-medium mb-1">
              Updated Date
            </h3>
            <div class="text-base">
              {{ formatDate(accountDetail.updated_at) }}
            </div>
          </div>
        </div>
        <div class="flex justify-end gap-2 mt-8">
          <Button type="button" label="Close" class="p-button-primary" @click="accountDetailDialog = false" />
        </div>
      </div>
    </Dialog>
  </div>
</template>

<style scoped>
.account-page {
  padding: 1rem;
}
</style>
