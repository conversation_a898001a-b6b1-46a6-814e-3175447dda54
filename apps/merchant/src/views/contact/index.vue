<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import { toTypedSchema } from '@vee-validate/zod'
import { useToast } from 'primevue/usetoast'
import { Field, Form as VeeForm } from 'vee-validate'
import { computed, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { z } from 'zod'
import { useListRefresh } from '@/composables/useListRefresh'
import { useRequestList } from '@/composables/useRequestList'
import { SearchFieldType } from '@/constants/search'
import * as contactApi from '@/services/api/contact'
import { formatDate } from '@/utils/date'

defineOptions({
  name: 'contactList',
})

const { t } = useI18n()
const toast = useToast()

// Column configuration
const columns = ref<TableColumnItem[]>([
  { field: 'id', header: 'ID', style: { width: '80px' } },
  { field: 'name', header: 'Name', style: { minWidth: '120px' } },
  { field: 'email', header: 'Email', style: { minWidth: '180px' } },
  { field: 'contact_status', header: 'Status', style: { minWidth: '100px' }, template: 'status' },
  { field: 'contact_id', header: 'Xero Contact ID', style: { minWidth: '200px' } },
  { field: 'created_at', header: t('common.created', 'Created Date'), template: 'created_at', sortable: true, style: { minWidth: '180px' } },
  { field: 'action', header: '', template: 'action', style: { width: '50px' } },
])

const {
  list,
  loading,
  total,
  refresh,
  search,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
  setSearchParams,
} = useRequestList<Contact.Info[], Api.ContactListReq>({
  requestFn: contactApi.getList,
})

// Use common list refresh logic
useListRefresh('contactList', refresh)

// Get status severity
const getStatusSeverity = (status: string | undefined) => {
  if (!status) { return 'upcoming' }

  const severityMap: Record<string, string> = {
    ACTIVE: 'paid',
    ARCHIVED: 'failed',
    INACTIVE: 'warning',
  }

  return severityMap[status] || 'upcoming'
}

const handleSort = (event: any) => {
  const { sortField, sortOrder } = event
  setSearchParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  search()
}

// Search model
const searchModel = ref<Partial<Api.ContactListReq>>({
  name: '',
})

// Search fields configuration
const searchFields = computed(() => [
  {
    name: 'name',
    label: 'Name',
    type: SearchFieldType.TEXT,
    placeholder: 'Search by name',
    maxlength: 50,
    defaultValue: '',
  },
])

// Add contact dialog control
const addContactDialog = ref(false)

// Edit contact dialog control
const editContactDialog = ref(false)

// Form submission status
const isSubmitting = ref(false)

// Form validation schema
const schema = toTypedSchema(z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  email: z.string().email({ message: 'Please enter a valid email' }),
}))

// Edit form validation schema
const editSchema = toTypedSchema(z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  email: z.string().email({ message: 'Please enter a valid email' }),
}))

// Form data
const contactForm = reactive<{
  name: string
  email: string
}>({
  name: '',
  email: '',
})

// Edit form data
const editForm = reactive<{
  id: number | null
  contact_id: string
  name: string
  email: string
}>({
  id: null,
  contact_id: '',
  name: '',
  email: '',
})

// Open add contact dialog
const openAddContactDialog = () => {
  // Reset form
  contactForm.name = ''
  contactForm.email = ''
  addContactDialog.value = true
}

// Open edit contact dialog
const openEditContactDialog = (data: Contact.Info) => {
  editForm.id = data.id as number
  editForm.contact_id = data.contact_id
  editForm.name = data.name
  editForm.email = data.email
  editContactDialog.value = true
}

// Submit form
const onSubmit = (values: Record<string, unknown>) => {
  isSubmitting.value = true
  contactApi.create(values as unknown as Api.CreateContactReq).then((res: any) => {
    if (res.code === 0) {
      toast.add({ severity: 'success', summary: t('common.success', 'Success'), detail: 'Contact created successfully', life: 3000 })
      addContactDialog.value = false
      refresh()
    }

    isSubmitting.value = false
  }).catch(() => {
    isSubmitting.value = false
  })
}

// Update form submission
const onUpdateSubmit = async () => {
  isSubmitting.value = true
  try {
    const response = await contactApi.update({
      id: editForm.id as number,
      contact_id: editForm.contact_id,
      name: editForm.name,
      email: editForm.email,
    })

    if (response.code === 0) {
      toast.add({ severity: 'success', summary: t('common.success', 'Success'), detail: 'Contact updated successfully', life: 3000 })
      editContactDialog.value = false
      refresh()
    }
    isSubmitting.value = false
  }
  catch {
    isSubmitting.value = false
  }
}

// Search handler
const handleSearch = () => {
  setSearchParams(searchModel.value)
  search()
}
</script>

<template>
  <div class="contact-page">
    <!-- Search bar -->
    <BaseSearch
      v-model="searchModel"
      :loading="loading"
      :basic-search-fields="searchFields"
      @search="handleSearch"
    />

    <div class="flex items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8">
      <Button label="Add Contact" icon="pi pi-plus" @click="openAddContactDialog" />
    </div>

    <!-- Contacts table -->
    <BaseDataTable
      :show-search-bar="false"
      :value="list" :columns="columns" :scrollable="true" :show-multiple-column="false"
      :loading="loading" :paginator="true" :rows="20" :total-records="total" :lazy="true" data-key="id"
      :failed="failed" :failure-message="failureMessage" :striped-rows="true"
      search-placeholder="Search contacts..."
      @page="(e: DataTablePageEvent) => handlePageChange(e)"
      @sort="handleSort"
    >
      <template #action="{ data }">
        <BaseDataTableActions content-width="50px">
          <template #default>
            <Button icon="pi pi-pencil" text @click="openEditContactDialog(data)" />
          </template>
        </BaseDataTableActions>
      </template>
      <template #status="{ data }">
        <BaseTag :text="data.contact_status" :type="getStatusSeverity(data.contact_status)" />
      </template>
      <template #created_at="{ data }">
        {{ formatDate(data.created_at) }}
      </template>
    </BaseDataTable>

    <!-- Add contact dialog -->
    <Dialog
      v-model:visible="addContactDialog"
      modal
      header="Add Contact"
      :style="{ width: '500px' }"
      :closable="true"
    >
      <VeeForm :validation-schema="schema" class="flex flex-col gap-4" @submit="onSubmit">
        <Field v-slot="{ field, errorMessage }" v-model="contactForm.name" as="div" class="mb-2" name="name">
          <label for="name" class="mb-2 block">Name</label>
          <InputText id="name" class="w-full" v-bind="field" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>
        <Field v-slot="{ field, errorMessage }" v-model="contactForm.email" as="div" class="mb-2" name="email">
          <label for="email" class="mb-2 block">Email</label>
          <InputText id="email" class="w-full" v-bind="field" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>
        <div class="flex justify-end gap-2 mt-4">
          <Button type="button" label="Cancel" severity="secondary" @click="addContactDialog = false" />
          <Button type="submit" label="Submit" :loading="isSubmitting" />
        </div>
      </VeeForm>
    </Dialog>

    <!-- Edit contact dialog -->
    <Dialog
      v-model:visible="editContactDialog"
      modal
      header="Edit Contact"
      :style="{ width: '500px' }"
      :closable="true"
    >
      <VeeForm :validation-schema="editSchema" class="flex flex-col gap-4" @submit="onUpdateSubmit">
        <div class="mb-2">
          <label for="contact_id" class="mb-2 block">Contact ID (Xero)</label>
          <InputText id="contact_id" class="w-full" :value="editForm.contact_id" disabled />
        </div>
        <Field v-slot="{ field, errorMessage }" v-model="editForm.name" as="div" class="mb-2" name="name">
          <label for="edit_name" class="mb-2 block">Name</label>
          <InputText id="edit_name" class="w-full" v-bind="field" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>
        <Field v-slot="{ field, errorMessage }" v-model="editForm.email" as="div" class="mb-2" name="email">
          <label for="edit_email" class="mb-2 block">Email</label>
          <InputText id="edit_email" class="w-full" v-bind="field" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>
        <div class="flex justify-end gap-2 mt-4">
          <Button type="button" label="Cancel" severity="secondary" @click="editContactDialog = false" />
          <Button type="submit" label="Update" :loading="isSubmitting" />
        </div>
      </VeeForm>
    </Dialog>
  </div>
</template>
