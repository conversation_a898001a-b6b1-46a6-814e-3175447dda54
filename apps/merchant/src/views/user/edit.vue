<script setup lang="ts">
import type { Ref } from 'vue'
import { toTypedSchema } from '@vee-validate/yup'
import { storeToRefs } from 'pinia'
import { useToast } from 'primevue/usetoast'
import { Field, Form as VeeForm } from 'vee-validate'
import { nextTick, onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import * as yup from 'yup'
import { useListRefresh } from '@/composables/useListRefresh'
import { user as userApi, userRole } from '@/services/api'
import { useUserStore } from '@/store/modules/user'

defineOptions({
  name: 'UserEdit',
})

const { t } = useI18n()
const toast = useToast()
const router = useRouter()
const route = useRoute()
const formRef = ref()
const loading = ref(false)
const merchantId = ref<string>(route.params.id as string)
const { backWithRefresh } = useListRefresh('usersList', () => {})

const userStore = useUserStore()

const { groupList } = storeToRefs(userStore)

// 表单初始值
const initialValues: Ref<{
  user_name: string
  email: string
  password: string
  mfa_check: number
  business_ids: string[]
  // roles: { name: string, id: number }[]
}> = ref({
  user_name: '',
  email: '',
  password: '',
  mfa_check: 0,
  business_ids: [],
  // roles: [],
})

const isPasswordEdit = ref(false)

// 角色选项
const roles = ref<{ name: string, id: number }[]>([])

// 获取角色列表
const fetchRoles = async () => {
  try {
    const response = await userRole.getList({
      page: 1,
      page_size: 100,
    })
    roles.value = (response.data?.data || []).map((role: UserRole.Info) => ({
      name: role.name,
      id: role.id || 0,
    }))
  }
  catch (error) {
    console.error('Failed to fetch roles:', error)
    toast.add({
      severity: 'error',
      summary: t('common.error'),
      detail: t('merchant.role.messages.fetchRolesFailed', 'Failed to fetch roles'),
      life: 3000,
    })
  }
}

// 获取商户详情
const fetchMerchantDetail = async () => {
  try {
    loading.value = true
    const response = await userApi.getUserDetail(merchantId.value)
    // const { name: user_name, email, 'mfa_check': tfa_check, roles: merchantRoles } = response.data as User.UserInfo & { roles: Array<{ id: number, name: string }> }
    const { name: user_name, email, 'mfa_check': tfa_check, merchant_user_business = [] } = response.data as User.UserInfo & { roles: Array<{ id: number, name: string }> }

    initialValues.value = {
      user_name,
      email,
      password: '',
      mfa_check: tfa_check,
      business_ids: merchant_user_business?.map(item => item.business_id),
      // roles: [],
    }

    // if (merchantRoles && Array.isArray(merchantRoles)) {
    //   initialValues.value.roles = merchantRoles.map((role) => {
    //     return {
    //       name: role.name,
    //       id: role.id || 0,
    //     }
    //   })
    // }
  }
  catch (error) {
    console.error('Failed to fetch merchant detail:', error)
    toast.add({
      severity: 'error',
      summary: t('common.error'),
      detail: t('merchant.messages.updateFailed', 'Failed to fetch merchant detail'),
      life: 3000,
    })
  }
  finally {
    loading.value = false
  }
}

// 表单验证schema
const schema = toTypedSchema(yup.object({
  user_name: yup.string()
    .min(3, t('validation.minLength', { min: 3 }))
    .max(50, t('validation.maxLength', { max: 50 }))
    .required(),
  email: yup.string()
    .email(t('validation.emailInvalid'))
    .required(t('validation.emailRequired')),
  password: yup.string()
    .test('password-validation', t('validation.passwordMinLength'), (value) => {
      if (!value || value === '') { return true } // 空密码允许通过（保持原密码不变）
      return value.length >= 8
    })
    .test('password-uppercase', t('validation.passwordUppercase'), (value) => {
      if (!value || value === '') { return true }
      return /[A-Z]/.test(value)
    })
    .test('password-lowercase', t('validation.passwordLowercase'), (value) => {
      if (!value || value === '') { return true }
      return /[a-z]/.test(value)
    })
    .test('password-number', t('validation.passwordNumber'), (value) => {
      if (!value || value === '') { return true }
      return /\d/.test(value)
    }),
  mfa_check: yup.number().required(),
  business_ids: yup.array().optional(),
  // roles: yup.array().of(yup.object({ name: yup.string(), id: yup.number() })).min(1, 'Role is required'),
}))

// 提交表单
const submitForm = async () => {
  loading.value = true

  try {
    const result = await formRef.value?.validate()

    if (!result.valid) {
      loading.value = false
      return
    }

    const formData = { ...result.values, roles: result?.values?.roles?.map((role: { id: number }) => role?.id) || [] }

    // 如果密码为空，从提交数据中移除
    if (!formData.password) {
      delete formData.password
    }

    const { code } = await userApi.editUpdateUser(merchantId.value, formData)

    if (code === 0) {
      toast.add({
        severity: 'success',
        summary: t('common.success'),
        detail: t('merchant.messages.updateSuccess', 'Merchant updated successfully'),
      })

      backWithRefresh()
      fetchMerchantDetail()
    }
  }
  catch (error) {
    console.error('Failed to update user:', error)
  }
  finally {
    loading.value = false
  }
}

// 取消操作
const cancel = () => {
  router.back()
}

onMounted(() => {
  Promise.all([
    fetchRoles(),
    fetchMerchantDetail(),
  ]).then(() => {
    nextTick(() => {
      isPasswordEdit.value = true
    })
  })
})
</script>

<template>
  <div class="merchant-edit-page">
    <div class="p-4 bg-white rounded-2xl">
      <VeeForm
        ref="formRef"
        :validation-schema="schema"
        class="merchant-form flex flex-col gap-4"
        @submit="submitForm"
      >
        <!-- 用户名 -->
        <Field v-slot="{ field, errorMessage, handleChange }" v-model="initialValues.user_name" name="user_name" class="form-col">
          <div class="field">
            <label for="user_name" class="mb-2 block">{{ t('merchant.form.name', 'Username') }}*</label>
            <InputText
              id="user_name"
              v-model="field.value"
              :placeholder="t('merchant.form.name', 'Username')"
              class="w-full"
              autocomplete="new-password"
              @value-change="handleChange"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>

        <!-- 邮箱 -->
        <Field v-slot="{ field, errorMessage }" v-model="initialValues.email" name="email" class="form-col">
          <div class="field">
            <label for="email" class="mb-2 block">{{ t('merchant.form.email', 'Email') }}*</label>
            <InputText
              id="email"
              v-bind="field"
              :placeholder="t('merchant.form.email', 'Email')"
              class="w-full"
              type="email"
              autocomplete="new-password"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>

        <!-- 密码 -->
        <Field v-slot="{ field, errorMessage, handleChange }" v-model="initialValues.password" name="password" as="div">
          <div class="field">
            <label for="new-password" class="mb-2 block">{{ t('merchant.form.password', 'Password') }}</label>
            <Password
              id="new-password"
              v-model="field.value"
              :placeholder="t('merchant.form.password', 'Password')"
              class="w-full"
              toggle-mask
              :feedback="true"
              autocomplete="new-password"
              :disabled="!isPasswordEdit"
              :input-props="{
                autocomplete: 'new-password',
              }"
              @value-change="handleChange"
            />
            <div class="text-sm text-gray-500 mt-2">
              <!-- 如果不填密码，密码将保持不变 -->
              If you do not fill in the password, the password will remain unchanged
            </div>
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>

        <!-- 2FA认证 -->
        <Field v-slot="{ errorMessage }" v-model="initialValues.mfa_check" name="mfa_check" as="div" class="form-col">
          <div class="field">
            <label class="mb-2 block">{{ t('merchant.form.2fa', '2FA Authentication') }}</label>
            <Select
              v-model="initialValues.mfa_check"
              :options="[
                { label: 'Enabled', value: 1 },
                { label: 'Disabled', value: 0 },
              ]"
              option-label="label"
              option-value="value"
              class="w-full"
              name="to_fa_check"
              :placeholder="t('merchant.form.select2FA', 'Select 2FA Option')"
            />
          </div>
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>

        <!-- 角色 -->
        <!-- <Field v-slot="{ field, errorMessage, handleChange }" v-model="initialValues.roles" name="roles" class="form-col">
          <div class="field mb-4">
            <label for="roles" class="mb-2 block">{{ t('merchant.form.roles', 'Roles') }}</label>
            <MultiSelect
              :model-value="field.value"
              :options="roles"
              option-label="name"
              :placeholder="t('merchant.form.selectRoles', 'Select Roles')"
              display="chip"
              class="w-full"
              name="roles"
              @update:model-value="handleChange"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field> -->

        <!-- Assign to Business -->
        <Field v-slot="{ field, errorMessage, handleChange }" v-model="initialValues.business_ids" name="business_ids" class="form-col">
          <div class="field mb-4">
            <label for="bids" class="mb-2 block">Assign to Business</label>
            <MultiSelect
              :model-value="field.value"
              :options="groupList.map(item => {
                return {
                  ...item,
                  label: `${item.label} - ${item.value}`,
                }
              })"
              option-label="label"
              option-value="value"
              class="w-full"
              placeholder="Select Assign to Business"
              @update:model-value="handleChange"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>

          <!-- 表单按钮 -->
          <div class="flex justify-end mt-6 gap-2">
            <Button
              type="button"
              :label="t('common.cancel')"
              icon="pi pi-times"
              class="p-button-text"
              @click="cancel"
            />
            <Button
              type="submit"
              :label="t('common.save')"
              icon="pi pi-check"
              :loading="loading"
            />
          </div>
        </field>
      </VeeForm>
    </div>
  </div>
</template>

<style scoped>
.merchant-form {
  max-width: 600px;
}

.form-row {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.form-col {
  flex: 1;
  min-width: 250px;
}

.w-full {
  width: 100%;
}

:deep(.p-password) {
  width: 100%;
}

:deep(.p-password-input) {
  width: 100%;
}

/* 响应式样式 */
@media screen and (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 1rem;
  }

  .form-col {
    min-width: 100%;
  }
}

/* 小型移动设备 */
@media screen and (max-width: 480px) {
  :deep(.p-float-label) {
    font-size: 0.9rem;
  }

  :deep(.p-inputtext) {
    font-size: 0.9rem;
    padding: 0.5rem;
  }
}
</style>
