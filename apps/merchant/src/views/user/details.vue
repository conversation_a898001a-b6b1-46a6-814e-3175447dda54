<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { user as userApi } from '@/services/api'
import { formatDate } from '@/utils/date'

defineOptions({
  name: 'UserDetails',
})

const route = useRoute()
const router = useRouter()
const user = ref<User.UserInfo | null>(null)
const loading = ref(true)
const error = ref(false)
const errorMessage = ref('')

const fetchMerchantDetails = async () => {
  const id = route.params.id as string
  if (!id) {
    error.value = true
    errorMessage.value = 'Merchants is required'
    loading.value = false
    return
  }

  loading.value = true
  error.value = false

  try {
    const response = await userApi.getUserDetail(id)
    user.value = response.data
  }
  catch (err: any) {
    error.value = true
    errorMessage.value = err.message || 'Failed to load merchant details'
  }
  finally {
    loading.value = false
  }
}

const getStatusText = (status: number | undefined) => {
  if (status === undefined) { return 'Unknown' }
  return status === 1 ? 'Active' : 'Inactive'
}

const getTypeText = (type: number | undefined) => {
  if (type === undefined) { return 'Unknown' }

  const typeMap: Record<string, string> = {
    1: 'Admin',
    2: 'User',
    3: 'Guest',
  }
  return typeMap[String(type)] || 'Unknown'
}

const goBack = () => {
  router.back()
}

const goToEdit = () => {
  router.push({
    name: 'userEdit',
    params: {
      id: route.params.id,
    },
  })
}

onMounted(() => {
  fetchMerchantDetails()
})
</script>

<template>
  <div class="merchant-details">
    <div v-if="loading" class="flex justify-content-center align-items-center min-h-screen">
      <ProgressSpinner stroke-width="3" />
    </div>
    <div v-else-if="error" class="flex flex-column align-items-center gap-3 p-4">
      <Message severity="error" :closable="false" class="w-full md:w-6">
        {{ errorMessage }}
      </Message>
      <Button label="Retry" icon="pi pi-refresh" @click="fetchMerchantDetails" />
    </div>
    <div v-else-if="user">
      <div class="flex align-items-center mb-4">
        <Button
          icon="pi pi-arrow-left"
          text
          class="mr-3"
          @click="goBack"
        />
        <h1 class="text-xl font-medium m-0">
          {{ user.name }}
        </h1>
        <Button
          icon="pi pi-pencil"
          text
          class="ml-auto"
          @click="goToEdit"
        />
      </div>

      <div class="card">
        <div class="section">
          <h2 class="section-title">
            Basic Information
          </h2>
          <div class="info-list">
            <div class="info-item">
              <div class="info-label">
                Status
              </div>
              <div class="info-value">
                <Tag :value="getStatusText(user.status)" :severity="user.status === 1 ? 'success' : 'danger'" />
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">
                Type
              </div>
              <div class="info-value">
                {{ getTypeText(user.type) }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">
                Email
              </div>
              <div class="info-value">
                {{ user.email || 'N/A' }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">
                2FA Status
              </div>
              <div class="info-value">
                <Tag :value="user.mfa_check === 1 ? 'Enabled' : 'Disabled'" :severity="user.mfa_check === 1 ? 'success' : 'warning'" />
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">
                Assign to Business
              </div>
              <div class="info-value">
                {{ user?.merchant_user_business?.map((item) => `${item?.business?.business_name} - ${item?.business?.business_id}`).join(', ') || 'N/A' }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">
                Created
              </div>
              <div class="info-value">
                {{ user.created_at ? formatDate(user.created_at) : 'N/A' }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">
                Updated
              </div>
              <div class="info-value">
                {{ user.updated_at ? formatDate(user.updated_at) : 'N/A' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.merchant-details {
  min-height: 100vh;
  background-color: var(--surface-ground);
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.section {
  padding: 1.5rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
  color: var(--text-color);
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--surface-border);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  width: 140px;
  color: var(--text-color-secondary);
}

.info-value {
  flex: 1;
  color: var(--text-color);
}

@media screen and (max-width: 768px) {
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .info-label {
    width: 100%;
  }
}
</style>
