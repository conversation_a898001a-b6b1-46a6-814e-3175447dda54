<script setup lang="ts">
import { user as userApi } from '@/services/api'
import { useToast } from 'primevue/usetoast'
import { onMounted, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const toast = useToast()

const isLoading = ref(false)
const isChangingPassword = ref(false)
const passwordSubmitted = ref(false)

// 修改密码表单
const changePassword = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
})

// 加载用户设置
onMounted(async () => {
  // Check 2FA status
  loadUserSettings()
})

// 加载用户设置
const loadUserSettings = async () => {
  isLoading.value = true
  try {
    // 这里应该是从API获取用户设置
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
  }
  catch (error) {
    console.error('Failed to load user settings', error)
  }
  finally {
    isLoading.value = false
  }
}

// 更新密码
const updatePassword = async () => {
  passwordSubmitted.value = true

  // 表单验证
  if (!changePassword.currentPassword || !changePassword.newPassword || !changePassword.confirmPassword) {
    return
  }

  if (changePassword.newPassword !== changePassword.confirmPassword) {
    return
  }

  isChangingPassword.value = true
  try {
    const { code } = await userApi.updateUserPassword({
      password: changePassword.currentPassword,
      new_password: changePassword.newPassword,
    })

    if (code === 0) {
      // 重置表单
      changePassword.currentPassword = ''
      changePassword.newPassword = ''
      changePassword.confirmPassword = ''
      passwordSubmitted.value = false
      toast.add({
        severity: 'success',
        summary: t('common.success'),
        detail: t('user.settings.passwordUpdated'),
        life: 3000,
      })
    }
  }
  finally {
    isChangingPassword.value = false
  }
}
</script>

<template>
  <div class="settings-container">
    <div class="settings-header">
      <div class="settings-title">
        <h1>{{ t('user.settings.title') }}</h1>
        <p>{{ t('user.settings.subtitle') }}</p>
      </div>
    </div>

    <div class="settings-content">
      <div v-if="isLoading" class="loading-container">
        <ProgressSpinner />
      </div>

      <div v-else>
        <div class="password-section max-w-200">
          <div class="p-grid">
            <div class="p-col-12 p-md-4">
              <div class="field">
                <label for="currentPassword">{{ t('user.settings.currentPassword') }}</label>
                <Password
                  id="currentPassword"
                  v-model="changePassword.currentPassword"
                  :feedback="false"
                  toggle-mask
                  class="w-full"
                  :class="{ 'p-invalid': passwordSubmitted && !changePassword.currentPassword }"
                />
                <Message v-if="passwordSubmitted && !changePassword.currentPassword" variant="simple" severity="error">
                  {{ t('user.settings.currentPasswordRequired') }}
                </Message>
              </div>
            </div>

            <div class="p-col-12 p-md-4">
              <div class="field">
                <label for="newPassword">{{ t('user.settings.newPassword') }}</label>
                <Password
                  id="newPassword"
                  v-model="changePassword.newPassword"
                  toggle-mask
                  class="w-full"
                  :class="{ 'p-invalid': passwordSubmitted && !changePassword.newPassword }"
                />
                <Message v-if="passwordSubmitted && !changePassword.newPassword" variant="simple" severity="error">
                  {{ t('user.settings.newPasswordRequired') }}
                </Message>
              </div>
            </div>

            <div class="p-col-12 p-md-4">
              <div class="field">
                <label for="confirmPassword">{{ t('user.settings.confirmPassword') }}</label>
                <Password
                  id="confirmPassword"
                  v-model="changePassword.confirmPassword"
                  :feedback="false"
                  toggle-mask
                  class="w-full"
                  :class="{ 'p-invalid': passwordSubmitted && !changePassword.confirmPassword }"
                />
                <Message v-if="passwordSubmitted && !changePassword.confirmPassword" variant="simple" severity="error">
                  {{ t('user.settings.confirmPasswordRequired') }}
                </Message>
                <Message v-else-if="passwordSubmitted && changePassword.newPassword !== changePassword.confirmPassword" severity="error">
                  {{ t('user.settings.passwordsDoNotMatch') }}
                </Message>
              </div>
            </div>
          </div>

          <div class="form-actions">
            <Button
              :label="t('user.settings.updatePassword')"
              icon="pi pi-lock"
              :loading="isChangingPassword"
              class="p-button-primary"
              severity="warn"
              @click="updatePassword"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.settings-container {
  margin: 0 auto;
}

.two-factor-section {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;

  .section-description {
    color: var(--text-color-secondary);
    margin-bottom: 1.5rem;
  }

  .two-factor-status {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 500;

      i {
        font-size: 1.5rem;

        &.text-success {
          color: var(--green-500);
        }

        &.text-danger {
          color: var(--red-500);
        }
      }
    }
  }
}

.settings-header {
  position: relative;
  background: linear-gradient(90deg, #e3f2fd 0%, #fff3e0 100%);
  padding: 2rem;
  border-top-left-radius: 16px;
    border-top-right-radius: 16px;
}

.settings-title {
  h1 {
    margin: 0 0 8px 0;
    font-size: 1.5rem;
    font-weight: 600;
  }

  p {
    margin: 0;
    color: #666;
  }
}

.settings-content {
  margin: 0 auto;
  margin-top: -10px;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 40px 0;
}

:deep(.p-tabview-panels) {
  padding: 24px 0 0 0;
}

.field {
  margin-bottom: 24px;

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }
}

.field-help {
  display: block;
  color: #666;
  margin-top: 4px;
}

.notification-section,
.password-section {
  margin-bottom: 32px;

  h2 {
    font-size: 1.2rem;
    margin-bottom: 16px;
    font-weight: 600;
  }
}

:deep(.p-inputtext) {
  width: 100%;
}

.notification-option {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }
}

.notification-description {
  margin: 4px 0 0 32px;
  color: #666;
  font-size: 0.9rem;
}

.password-requirements {
  margin-top: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;

  h3 {
    font-size: 1rem;
    margin-bottom: 12px;
    font-weight: 600;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  li {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    i {
      color: #4caf50;
      margin-right: 8px;
    }
  }
}

.form-actions {
  margin-top: 32px;
  display: flex;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .settings-content {
    padding: 16px;
  }

  :deep(.p-tabview-panels) {
    padding: 16px 0 0 0;
  }
}
</style>
