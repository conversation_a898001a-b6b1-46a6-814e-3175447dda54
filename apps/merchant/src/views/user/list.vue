<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import type { DictItem } from '@/services/api/dict'
import { useToast } from 'primevue/usetoast'
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import BaseDataTable from '@/components/common/BaseDataTable.vue'
import { useDict } from '@/composables/useDict'
import { useListRefresh } from '@/composables/useListRefresh'
import { usePermissions } from '@/composables/usePermissions'
import { useRequestList } from '@/composables/useRequestList'
import { Permissions } from '@/constants/permissions'
import { SearchFieldType } from '@/constants/search'
import { user as userApi } from '@/services/api'
import { formatDate } from '@/utils/date'
import { addAllToDict } from '@/utils/dict'

defineOptions({
  name: 'merchantList',
})

const { t } = useI18n()
const toast = useToast()

// 使用 useRequestList 处理商户列表数据
const requestList = useRequestList<User.UserInfo[], Api.UserListReq>({
  requestFn: userApi.getUserList,
})

const router = useRouter()

const { hasPermission, hasAnyPermission } = usePermissions()

const {
  list,
  loading,
  total,
  refresh,
  setSearchParams,
  search,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
} = requestList

// 使用通用的列表刷新逻辑
useListRefresh('merchantsList', refresh)

// 列配置
const columns = ref<TableColumnItem[]>([
  {
    field: 'name',
    header: t('merchant.columns.name', 'Username'),
    style: { minWidth: '150px' },
    template: 'name',
    sortable: true,
    sortField: 'name',
  },
  {
    field: 'email',
    header: t('merchant.columns.email', 'Email'),
    style: { minWidth: '200px' },
  },
  {
    field: 'status',
    header: t('merchant.columns.status', 'Status'),
    style: { minWidth: '100px' },
    template: 'status',
  },
  {
    field: 'business_ids',
    header: 'Business',
    template: 'business',
  },
  {
    field: 'type',
    header: t('merchant.columns.type', 'Type'),
    style: { minWidth: '120px' },
    template: 'type',
  },
  {
    field: 'mfa_check',
    header: t('merchant.columns.2fa', '2FA Status'),
    style: { minWidth: '120px' },
    template: '2fa',
  },
  {
    field: 'created_at',
    header: t('common.created', 'Created Date'),
    style: { minWidth: '150px' },
    sortable: true,
    template: 'date',
    sortField: 'created_at',
  },
  {
    field: 'action',
    header: '',
    style: { width: '50px' },
    template: 'action',
    alignFrozen: 'right',
    frozen: true,
  },
])

const statusOptions = ref<DictItem[]>([])
const typeOptions = ref<DictItem[]>([])

const searchModel = ref<Partial<Api.UserListReq>>({
  'name': '',
  'email': '',
  'status': '',
  'type': '',
  'created_at[]': [],
})

// 配置搜索字段
const searchFields = computed(() => [
  {
    name: 'name',
    label: 'Username',
    type: SearchFieldType.TEXT,
    placeholder: 'Search for Username',
    maxlength: 50,
    defaultValue: '',
  },
  {
    name: 'status',
    label: 'Status',
    type: SearchFieldType.SELECT,
    placeholder: 'All',
    options: statusOptions.value,
    defaultValue: '',
  },
  {
    name: 'type',
    label: 'Type',
    type: SearchFieldType.SELECT,
    placeholder: 'All',
    options: typeOptions.value,
    defaultValue: '',
  },
])

// 排序处理
const handleSort = (event: any) => {
  const { sortField, sortOrder } = event
  requestList.setParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  requestList.search()
}

// 获取状态样式
const getStatusSeverity = (status: number | undefined) => {
  if (status === undefined) { return 'upcoming' }

  const severityMap: Record<string, string> = {
    1: 'paid',
    0: 'failed',
  }
  return severityMap[String(status)] || 'upcoming'
}

// 获取状态文本
const getStatusText = (status: number | undefined) => {
  if (status === undefined) { return 'Unknown' }
  const statusMap: Record<string, string> = {
    1: 'Enabled',
    0: 'Disabled',
  }
  return statusMap[String(status)] || 'Unknown'
}

// 导航到详情页
const navigateToDetail = ({ data: row }: { data: User.UserInfo }) => {
  router.push({
    name: 'userDetail',
    params: {
      id: row.id,
    },
  })
}

// 导航到编辑页
const navigateToEdit = (data: User.UserInfo) => {
  router.push({
    name: 'userEdit',
    params: {
      id: data.id,
    },
  })
}

// 导航到创建页面
const navigateToCreate = () => {
  router.push({ name: 'userCreate' })
}

// 删除确认对话框
const deleteDialog = ref(false)
const selectedMerchant = ref<User.UserInfo | null>(null)

// 确认删除
const confirmDelete = (data: User.UserInfo) => {
  selectedMerchant.value = data
  deleteDialog.value = true
}

// 执行删除
const deleteMerchant = () => {
  if (!selectedMerchant.value) { return }

  deleteDialog.value = false

  userApi.removeUser(String(selectedMerchant.value.id)).then((_res) => {
    toast.add({
      severity: 'success',
      summary: t('common.success', 'Success'),
      detail: t('merchant.messages.deleteSuccess', 'Merchant deleted successfully'),
    })
    refresh()
  }).catch((error) => {
    console.error('Failed to delete user:', error)
    toast.add({
      severity: 'error',
      summary: t('common.error', 'Error'),
      detail: t('merchant.messages.deleteFailed', 'Failed to delete merchant'),
    })
  })

  selectedMerchant.value = null
}

// 搜索处理
const handleSearch = () => {
  setSearchParams(searchModel.value)
  search()
}

useDict('user_status', (res) => {
  statusOptions.value = addAllToDict(res, { label: 'All', value: '' })
})

const { getLabel: getTypeLabel } = useDict('user_type', (res) => {
  typeOptions.value = addAllToDict(res, { label: 'All', value: '' })
})
</script>

<template>
  <div class="merchant-list-page">
    <BaseSearch
      v-model="searchModel"
      :loading="loading"
      :basic-search-fields="searchFields"
      @search="handleSearch"
    />

    <div class="flex items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8">
      <Button
        :label="t('merchant.buttons.create')"
        class="!px-8"
        severity="warn"
        icon="pi pi-plus"
        @click="navigateToCreate"
      />
    </div>

    <BaseDataTable
      :row-hover="hasPermission(Permissions.USER_DETAIL)"
      :columns="columns"
      :value="list"
      :loading="loading"
      :total-records="total"
      :paginator="true"
      :rows="50"
      :lazy="true"
      data-key="id"
      :show-search-bar="false"
      :scrollable="true"
      search-placeholder="Search merchants..."
      :failed="failed"
      :failure-message="failureMessage"
      :striped-rows="true"
      @sort="handleSort"
      @page="(e: DataTablePageEvent) => handlePageChange(e)"
      @row-click="navigateToDetail"
    >
      <template #name="{ data }">
        <div>{{ data.name }}</div>
      </template>
      <template #status="{ data }">
        <BaseTag :text="getStatusText(data.status)" :type="getStatusSeverity(data.status)" />
      </template>
      <template #type="{ data }">
        <div>{{ getTypeLabel(data.type) }}</div>
      </template>
      <template #business="{ data }">
        <div class="flex flex-col flex-wrap gap-2">
          <div v-for="item in data?.merchant_user_business" :key="item.business_id" class="whitespace-nowrap">
            {{ item?.business?.business_name }} - {{ item?.business?.business_id }}
          </div>
        </div>
      </template>
      <template #2fa="{ data }">
        <BaseTag :text="data.mfa_check === 1 ? 'Enabled' : 'Disabled'" :type="data.mfa_check === 1 ? 'paid' : 'failed'" />
      </template>
      <template #date="{ data }">
        <div>{{ formatDate(data.created_at) }}</div>
      </template>
      <template #action="{ data }">
        <BaseDataTableActions
          v-if="hasAnyPermission([
            Permissions.USER_UPDATE,
            Permissions.USER_DELETE,
          ]) && data.type !== 0"
          :is-show-detail="false"
          :is-show-delete="data.type !== 0 && hasPermission(Permissions.USER_DELETE)"
          :is-show-edit="data.type !== 0 && hasPermission(Permissions.USER_UPDATE)"
          :loading="data.__loading"
          @edit="navigateToEdit(data)"
          @delete="confirmDelete(data)"
        />
      </template>
    </BaseDataTable>
    <!-- 删除确认对话框 -->
    <Dialog
      v-model:visible="deleteDialog"
      :style="{ width: '450px' }"
      :header="t('merchant.dialogs.confirmDelete')"
      :modal="true"
    >
      <div class="confirmation-content">
        <i class="pi pi-exclamation-triangle mr-3" style="font-size: 2rem" />
        <span v-if="selectedMerchant">
          {{ t('merchant.dialogs.deleteConfirmMessage', { name: selectedMerchant.name }) }}
        </span>
      </div>
      <template #footer>
        <Button :label="t('common.no')" icon="pi pi-times" text @click="deleteDialog = false" />
        <Button :label="t('common.yes')" icon="pi pi-check" text @click="deleteMerchant" />
      </template>
    </Dialog>
  </div>
</template>

<style scoped>
.p-dialog .p-dialog-header {
  border-bottom: 1px solid #dee2e6;
  padding: 1.5rem;
}

.p-dialog .p-dialog-footer {
  border-top: 1px solid #dee2e6;
  padding: 1.5rem;
  text-align: right;
}

.p-dialog .p-dialog-content {
  padding: 2rem;
}

.field {
  margin-bottom: 1.5rem;
}

.confirmation-content {
  display: flex;
  align-items: center;
}
</style>
