<script setup lang="ts">
import InputOTP from 'primevue/inputotp'
import { useToast } from 'primevue/usetoast'
import QRCode from 'qrcode'
import { computed, onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { publicRouterName } from '@/router/publicRouterName'
import { user as userApi } from '@/services/api'
import { useUserStore } from '@/store/modules/user'

const { t } = useI18n()
const router = useRouter()
const toast = useToast()
const userStore = useUserStore()

// Copy secret key function
const copySecretKey = () => {
  if (state.value && state.value.secret) {
    navigator.clipboard.writeText(state.value.secret)
      .then(() => {
        copySuccess.value = true
        toast.add({
          severity: 'success',
          summary: t('common.success'),
          detail: t('user.twoFactorAuth.secretKeyCopied'),
        })

        // Reset copy success after 3 seconds
        setTimeout(() => {
          copySuccess.value = false
        }, 3000)
      })
      .catch(() => {
        toast.add({
          severity: 'error',
          summary: t('common.error'),
          detail: t('user.twoFactorAuth.copyFailed'),
        })
      })
  }
}

// State
const isLoading = ref(false)
const isVerifying = ref(false)
const isEnabled = ref(false)
const verificationCode = ref<string>('')
const errorMessage = ref('')
const copySuccess = ref(false)

const state = ref<Api.UserTwoFactorSetupRes>({
  secret: '',
  google2fa_url: '',
  ios_download_path: '',
  android_download_path: '',
  is_bind: false,
})

const qrcodeCanvas = (el: any) => {
  if (el && state.value.google2fa_url) {
    QRCode.toCanvas(el, state.value.google2fa_url, {
      width: 240,
      margin: 0,
    })
  }
}

// Computed
const isValidCode = computed(() => {
  const code = String(verificationCode.value)
  if (code && code.length === 6 && /^\d+$/.test(code)) {
    nextStep()
    return true
  }
  return false
})

// Get QR code for initial setup
const getSetupInfo = async () => {
  isLoading.value = true

  try {
    const { data, code = 1 } = await userApi.get2FASetupInfo()
    if (code === 0) {
      state.value = data
      if (data.is_bind) {
        currentStep.value = 2
      }
    }
  }
  catch (error) {
    handleError(error, t('user.twoFactorAuth.errorGettingQRCode'))
  }
  finally {
    isLoading.value = false
  }
}

// Enable 2FA after verification
const enable2FA = async () => {
  isLoading.value = true

  try {
    const { code, message } = await userApi.verify2FACode({
      secret: state.value.secret,
      verify_code: String(verificationCode.value),
    })

    if (code !== 0) {
      errorMessage.value = message || t('user.twoFactorAuth.invalidCode')
      return false
    }
    else {
      isLoading.value = false
      errorMessage.value = ''
    }

    isEnabled.value = true
    return true
  }
  catch (error) {
    isLoading.value = false
    handleError(error, t('user.twoFactorAuth.errorEnabling'))
    return false
  }
}

// Reset verification code
const resetCode = () => {
  verificationCode.value = ''
  errorMessage.value = ''
}

// Handle API errors
const handleError = (error: any, defaultMessage: string) => {
  const message = error?.message || defaultMessage

  errorMessage.value = message
  toast.add({
    severity: 'error',
    summary: t('user.twoFactorAuth.error'),
    detail: message,
    life: 5000,
  })
}

// Step management
const currentStep = ref(1) // 1: Setup, 2: Verification

// Progress through steps
const nextStep = async () => {
  if (currentStep.value === 1) {
    currentStep.value = 2
  }
  else if (currentStep.value === 2) {
    if (isValidCode.value) {
      const success = await enable2FA()
      if (success) {
        await userStore.initializeFromStorage()
        isLoading.value = false
        toast.add({
          severity: 'success',
          summary: t('user.twoFactorAuth.success'),
          detail: t('user.twoFactorAuth.setupComplete'),
        })
        router.push({
          name: publicRouterName.ROOT,
        })
      }
      else {
        verificationCode.value = ''
      }
    }
  }
}

// Go back to previous step
const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
    resetCode()
  }
  else {
    userStore.logout()
    router.push('/')
  }
}

// Init component
onMounted(async () => {
  // Get setup information (QR code and secret)
  await getSetupInfo()
})
</script>

<template>
  <div class="two-factor-auth-container">
    <div class="two-factor-auth-card">
      <div v-if="isLoading" class="loading-container">
        <ProgressSpinner />
      </div>

      <div v-else>
        <!-- Step 1: Setup with QR Code -->
        <div v-if="currentStep === 1" class="setup-step">
          <h2>{{ t('user.twoFactorAuth.setupStep') }}</h2>
          <div class="setup-instructions">
            <ol>
              <li>{{ t('user.twoFactorAuth.downloadApp') }}</li>
              <li>{{ t('user.twoFactorAuth.scanQRCode') }}</li>
              <li>{{ t('user.twoFactorAuth.getVerificationCode') }}</li>
            </ol>
          </div>

          <div class="qr-code-container">
            <div class="qr-code-main">
              <div v-if="state.google2fa_url" class="qr-code">
                <canvas id="qrcode-canvas" :ref="qrcodeCanvas" />
              </div>
              <p class="scan-instruction">
                <i class="pi pi-mobile" />
                {{ t('user.twoFactorAuth.scanQRCode') }}
              </p>
            </div>

            <div class="qr-code-apps">
              <div class="app-download">
                <img :src="state.android_download_path" alt="Google Authenticator" class="app-icon">
                <span class="app-label">Android</span>
              </div>
              <div class="app-download">
                <img :src="state.ios_download_path" alt="Authy" class="app-icon">
                <span class="app-label">iOS</span>
              </div>
            </div>
          </div>

          <div v-if="state.secret" class="secret-key-container">
            <h3>{{ t('user.twoFactorAuth.manualSetup') }}</h3>
            <p>{{ t('user.twoFactorAuth.enterSecretKey') }}</p>
            <div class="secret-key-wrapper">
              <div v-if="state" class="secret-key">
                {{ state.secret }}
              </div>
              <Button
                icon="pi pi-copy" severity="secondary" text rounded aria-label="Copy" class="copy-button"
                @click="copySecretKey"
              />
            </div>
          </div>

          <div class="action-buttons">
            <Button :label="t('common.back')" severity="secondary" outlined :disabled="isLoading" @click="prevStep" />
            <Button :label="t('common.next')" severity="primary" :disabled="isLoading" @click="nextStep" />
          </div>
        </div>

        <!-- Step 2: Verification -->
        <div v-else-if="currentStep === 2" class="verification-step">
          <h2>{{ t('user.twoFactorAuth.verificationStep') }}</h2>
          <p class="verification-description">
            {{ t('user.twoFactorAuth.enterCodeFromApp') }}
          </p>

          <div class="verification-form">
            <div class="verification-code-container">
              <div class="verification-code-label">
                {{ t('user.twoFactorAuth.verificationCode') }}
              </div>
              <InputOTP
                id="verificationCode" v-model="verificationCode"
                :disabled="isVerifying" :length="6" input-class="otp-input" separator="" size="large"
                :class="{ 'p-invalid': errorMessage }" @keyup.enter="nextStep"
              />
              <div class="flex items-center justify-center w-full mt-6">
                <Message v-if="errorMessage" severity="error" size="small" variant="simple">
                  {{ errorMessage }}
                </Message>
              </div>
            </div>
          </div>

          <div class="action-buttons">
            <Button
              label="Back To Login" severity="secondary" outlined
              @click="userStore.logout()"
            />
            <Button
              v-if="!state.is_bind" :label="t('common.back')" severity="secondary" outlined
              :disabled="isVerifying" @click="prevStep"
            />
            <Button
              style="margin-left: auto;"
              :label="t('user.twoFactorAuth.verifyAndEnable')" severity="primary" :loading="isVerifying"
              :disabled="!isValidCode || isVerifying" @click="nextStep"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.two-factor-auth-container {
  display: flex;
  justify-content: center;
  padding: 2rem;
  background-color: var(--surface-ground);
  min-height: calc(100vh - 64px);
  /* Adjust based on your header height */
}

.two-factor-auth-card {
  background-color: white;
  border-radius: 24px;
  box-shadow: 0 16px 50px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 700px;
  padding: 2.5rem 3.5rem;
  margin: 1rem auto;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--surface-border);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, var(--p-primary-300), var(--p-primary-500));
    z-index: 1;
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 3rem;
}

.setup-step,
.verification-step {
  h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 1.5rem 0 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding-bottom: 1rem;
    text-align: center;

    &::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, var(--p-primary-400), var(--p-primary-300));
      border-radius: 2px;
    }
  }
}

.setup-instructions {
  margin: 1.5rem 0;
  background-color: var(--surface-hover);
  padding: 1.75rem 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);

  ol {
    margin: 0;

    li {
      margin-bottom: 0.5rem;
      padding-left: 0.5rem;
      color: var(--text-color);
      line-height: 1.5;
      font-size: 1.05rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.qr-code-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 2.5rem 0;
  background: linear-gradient(to bottom, var(--surface-card), var(--surface-section, #f8f9fa));
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--surface-border);
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 6px;
    background: linear-gradient(90deg, var(--p-primary-300), var(--p-primary-400), var(--p-primary-300));
    opacity: 0.7;
  }

  .qr-code-main {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 1.5rem;
  }

  .scan-instruction {
    margin-top: 1.25rem;
    font-size: 1.05rem;
    color: var(--text-color-secondary);
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--surface-card);
    padding: 0.75rem 1.25rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--surface-border);

    i {
      margin-right: 0.5rem;
      color: var(--p-primary-400);
    }
  }

  .qr-code {
    padding: 1.5rem;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    margin-bottom: 0.5rem;
    border: 1px solid var(--surface-border);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 70%);
      opacity: 0.6;
      z-index: 0;
    }

    #qrcode-canvas {
      height: 280px;
      width: 280px;
      position: relative;
      z-index: 1;
    }
  }

  .qr-code-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 280px;
    height: 280px;
    background-color: var(--surface-hover);
    border-radius: 12px;
    font-size: 2rem;
    color: var(--primary-color);
    border: 1px dashed var(--surface-border);
  }

  .qr-code-apps {
    display: flex;
    gap: 4rem;
    margin-top: 2rem;

    .app-download {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
    }

    .app-icon {
      width: 110px;
      height: 110px;
      border-radius: 8px;
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
      transition: transform 0.2s ease;
      border: 1px solid var(--surface-border);

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
      }
    }

    .app-label {
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--text-color);
      background-color: var(--surface-card);
      padding: 0.6rem 1.2rem;
      border-radius: 20px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
      border: 1px solid var(--surface-border);
      min-width: 110px;
      text-align: center;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
        background-color: var(--p-primary-100, #f0f7ff);
        color: var(--p-primary-500);
      }
    }
  }
}

.secret-key-container {
  margin: 1.5rem 0;
  text-align: center;
  background-color: var(--surface-card);
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px dashed var(--surface-border);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);

  h3 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color-secondary);
    display: flex;
    align-items: center;
    justify-content: center;

    &::before {
      content: '\e906';
      /* pi-info-circle */
      font-family: 'primeicons';
      margin-right: 0.5rem;
      color: var(--p-primary-300);
      font-size: 0.9rem;
    }
  }

  p {
    margin-bottom: 1rem;
    color: var(--text-color-secondary);
    max-width: 80%;
    margin-left: auto;
    margin-right: auto;
    font-size: 0.95rem;
  }

  .secret-key-wrapper {
    position: relative;
    display: inline-flex;
    align-items: center;
    margin: 0.75rem auto;

    .copy-button {
      position: absolute;
      right: -35px;
      top: 50%;
      transform: translateY(-50%);
      transition: all 0.2s ease;
      font-size: 0.9rem;
      width: 2rem;
      height: 2rem;

      &:hover {
        color: var(--p-primary-500);
        background-color: var(--surface-hover);
      }

      &:focus {
        box-shadow: 0 0 0 2px var(--surface-ground), 0 0 0 4px var(--p-primary-300);
      }
    }
  }

  .secret-key {
    background-color: var(--surface-hover);
    padding: 0.75rem 1.25rem;
    border-radius: 6px;
    font-size: 1rem;
    letter-spacing: 1px;
    display: inline-block;
    font-weight: 500;
    color: var(--text-color-secondary);
    min-width: 50%;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--surface-border);
    position: relative;
    user-select: all;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--surface-card);
    }

    &::before {
      content: '🔑';
      position: absolute;
      left: -25px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 0.9rem;
    }
  }
}

.verification-description {
  text-align: center;
  font-size: 1.05rem;
  color: var(--text-color-secondary);
  margin: 2rem 0;
}

.verification-form {
  margin: 2.5rem 0 5rem;
  margin-top: 5rem;
  display: flex;
  justify-content: center;

  .verification-code-container {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    padding: 4rem 2rem;
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--surface-border);
    width: 100%;
    max-width: 450px;
    text-align: center;
    position: relative;
    overflow: hidden;
  }

  .verification-code-label {
    font-weight: 600;
    color: var(--text-color-secondary);
    margin-bottom: 3rem;
    font-size: 1.1rem;
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;

  }

  :deep(.p-inputotp) {
    gap: 0.5rem;
    justify-content: center;
    position: relative;
    z-index: 1;

    .otp-input {
      width: 3.5rem;
      height: 3.5rem;
      text-align: center;
      font-size: 1.4rem;
      font-weight: 600;
      color: var(--text-primary);
      border: 2px solid var(--surface-border);
      border-radius: 12px;
      padding: 0;
      transition: all 0.3s ease;
      background-color: var(--surface-card);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);

      &:focus {
        border-color: var(--p-primary-400);
        box-shadow: 0 0 0 2px var(--surface-ground), 0 0 0 4px var(--p-primary-200);
        transform: translateY(-2px);
      }
    }

    &.p-invalid .otp-input {
      border-color: var(--red-500);
    }
  }
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 2.5rem;

  button {
    min-width: 120px;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &:first-child {
      margin-right: 1rem;
    }
  }
}
</style>
