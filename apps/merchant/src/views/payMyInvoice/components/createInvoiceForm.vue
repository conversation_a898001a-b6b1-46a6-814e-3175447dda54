<script setup lang="ts">
import type { FormContext } from 'vee-validate'
import type { PropType } from 'vue'
import { Format } from '@shared'
import { toTypedSchema } from '@vee-validate/yup'
import dayjs from 'dayjs'
import { Decimal } from 'decimal.js'
import { storeToRefs } from 'pinia'
import { Field, FieldArray, Form as VeeForm } from 'vee-validate'
import { ref, watch } from 'vue'
import * as yup from 'yup'
import { SurchargeRate } from '@/constants/user'
import { customer as customerApi } from '@/services/api'
import { useUserStore } from '@/store/modules/user'

defineOptions({
  name: 'CreateInvoiceForm',
})

// 定义组件属性
const props = defineProps({
  isShowSelectCustomer: {
    type: Boolean,
    default: true,
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
  schema: {
    type: Object,
    default: () => yup.object({}),
  },
  options: {
    type: Object,
    default: () => ({
      customers: [],
      xeroChartOfAccounts: [],
      xeroInvoiceTemplates: [],
    }),
  },
  optionsLoading: {
    type: Object,
    default: () => ({
      createInvoice: false,
      loadingConfig: false,
      loadingCustomers: false,
      loadingXeroChartOfAccounts: false,
      loadingXeroInvoiceTemplates: false,
    }),
  },
  mode: {
    type: String,
    default: 'add',
    validator: (value: string) => ['add', 'edit', 'view'].includes(value),
  },
  addLineItems: {
    type: Function,
    default: () => {
      return () => false
    },
  },
  config: {
    type: Object as PropType<Api.MerchantConfigItem | null>,
    default: () => ({}),
  },
})

const emits = defineEmits(['submit', 'updateCustomer'])

const userStore = useUserStore()

const businessId = ref<string | null>(null)

const { user } = storeToRefs(userStore)

const addCustomerModel = ref({
  name: '',
  email: '',
  phone: '',
})

const isShowAddCustomer = ref(false)

const isShowAddCustomerLoading = ref(false)

const addCustomerFormRef = ref<FormContext | null>(null)

const addCustomerSchema = toTypedSchema(yup.object({
  name: yup.string().required('Name is required'),
  email: yup.string().email('Invalid email').required('Email is required'),
  phone: yup.string().matches(/^\d+$/, 'Phone number must be a number').optional(),
  address: yup.string().optional(),
}))

const formRef = ref<FormContext | null>(null)

const handleSubmit = (values: any) => {
  emits('submit', values)
}

const addLineItem = (push: (item: any) => void) => {
  push({ description: '', amount: '' })
}

const removeLineItem = (remove: (index: number) => void, index: number) => {
  remove(index)
}

const handleCustomerChange = (event: string) => {
  const customer = props.options.customers.find((customer: any) => customer.customer_id === event)
  if (customer) {
    const names = String(customer.name)?.split(' ')
    props.formData.firstName = names[0] || ''
    formRef.value?.setFieldValue('firstName', names[0] || '')
    props.formData.lastName = names[1] || ''
    formRef.value?.setFieldValue('lastName', names.pop() || '')
    props.formData.email = customer.email_primary
    formRef.value?.setFieldValue('email', customer.email_primary)
    props.formData.phone = customer.phone_mobile
    formRef.value?.setFieldValue('phone', customer.phone_mobile)
    props.formData.address = customer.address
    formRef.value?.setFieldValue('address', customer.address)
  }
}

const handleAddCustomer = () => {
  addCustomerFormRef.value?.validate().then(async (result) => {
    if (result.valid) {
      try {
        isShowAddCustomerLoading.value = true
        const { code } = await customerApi.createCustomerSimple({
          email: result.values?.email,
          name: result.values?.name,
          phone_number: result.values?.phone,
        }, {
          headers: {
            'Business-Id': businessId.value,
          },
        })

        if (code === 0) {
          window.$toast.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Customer created successfully',
          })
          isShowAddCustomer.value = false
          emits('updateCustomer', result.values)
        }
      }
      finally {
        isShowAddCustomerLoading.value = false
      }
    }
  })
}

watch([() => formRef.value?.values?.lineItems, () => formRef.value?.values?.addLineItems, () => formRef.value?.values?.isInclusiveGst], ([lineItems, addLineItems, isInclusiveGst]) => {
  if (addLineItems) {
    const amount = lineItems?.reduce((acc: Decimal, item: { amount: string | number }) => acc.plus(new Decimal(item.amount || 0)), new Decimal(0))
    if (!isInclusiveGst) {
      props.formData.paymentAmount = amount?.toNumber() || 0
    }
    formRef.value?.setFieldValue('paymentAmount', amount?.toNumber() || 0, false)
    formRef.value?.setFieldTouched('paymentAmount', false)
  }
  else {
    lineItems?.forEach((_: { amount: string | number }, index: number) => {
      formRef.value?.setFieldTouched(`lineItems[${index}].amount`, false)
    })
    formRef.value?.setFieldTouched('lineItems', false)
  }
}, {
  deep: true,
})

defineExpose({
  formRef,
  handleCustomerChange,
})
</script>

<template>
  <VeeForm
    ref="formRef" v-slot="{ values }" class="create-invoice-form" :initial-values="props.formData"
    :validation-schema="props.schema" @submit="handleSubmit"
  >
    <!-- 预批准提示 -->
    <div class="alert-info mb-4 p-3 rounded">
      <i class="pi pi-info-circle mr-2" />
      If the customer has an active pre-approval then payment will be made automatically, alternatively a pay now page
      is available.
    </div>
    <!-- 客户选择 -->
    <Field
      v-if="props.isShowSelectCustomer" v-slot="{ field, errorMessage, handleChange }"
      v-model="props.formData.selectCustomer" name="selectCustomer"
    >
      <div class="form-group mb-2">
        <div class="flex items-center py-2 justify-between">
          <label for="selectCustomer" class="form-label">Select Customer</label>
          <Button type="button" icon="pi pi-plus" severity="secondary" @click="isShowAddCustomer = true" />
        </div>
        <Select
          id="selectCustomer" v-model="field.value" :options="props.options.customers"
          :loading="props.optionsLoading.loadingCustomers" option-label="name" option-value="customer_id"
          placeholder="- Please Select -" class="w-full" :filter="true" :class="{ 'p-invalid': errorMessage }"
          :virtual-scroller-options="{ itemSize: 38 }" @value-change="(e: string) => {
            handleChange(e)
            handleCustomerChange(e)
          }"
        />
        <Message v-if="errorMessage" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <!-- 姓名字段 -->
    <div class="grid lg:grid-cols-2 grid-cols-1 gap-4 mb-2">
      <Field v-slot="{ errorMessage }" name="firstName">
        <div class="flex flex-row items-center mb-2">
          <label for="firstName" class="form-label w-30">
            First Name
          </label>
          <div class="flex flex-col w-full gap-2">
            <InputText
              id="firstName" v-model="props.formData.firstName" class="w-full"
              :class="{ 'p-invalid': errorMessage }" :disabled="true"
            />
            <Message v-if="errorMessage" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </div>
      </Field>

      <Field v-slot="{ errorMessage }" name="lastName">
        <div class="flex flex-row items-center mb-2">
          <label for="lastName" class="form-label w-30">
            Last Name
          </label>
          <div class="flex flex-col w-full gap-2">
            <InputText
              id="lastName" v-model="props.formData.lastName" class="w-full"
              :class="{ 'p-invalid': errorMessage }" :disabled="true"
            />
            <Message v-if="errorMessage" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </div>
      </Field>
    </div>

    <!-- 邮箱和电话 -->
    <div class="grid lg:grid-cols-2 grid-cols-1 gap-4 mb-2">
      <Field v-slot="{ errorMessage }" name="email">
        <div class="flex flex-row items-center mb-2">
          <label for="email" class="form-label w-30">
            Email
          </label>
          <div class="flex flex-col w-full gap-2">
            <InputText
              id="email" v-model="props.formData.email" type="email" class="w-full"
              :class="{ 'p-invalid': errorMessage }" :disabled="true"
            />
            <Message v-if="errorMessage" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </div>
      </Field>

      <Field v-slot="{ errorMessage }" name="phone">
        <div class="flex flex-row items-center mb-2">
          <label for="phone" class="form-label w-30">
            Phone
          </label>
          <div class="flex flex-col w-full gap-2">
            <InputText
              id="phone" v-model="props.formData.phone" class="w-full" :class="{ 'p-invalid': errorMessage }"
              :disabled="true"
            />
            <Message v-if="errorMessage" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </div>
      </Field>
    </div>

    <!-- 地址 -->
    <Field v-slot="{ errorMessage }" name="address">
      <div class="form-group mb-2">
        <label for="address" class="form-label">Address</label>
        <Textarea
          id="address" v-model="props.formData.address" class="w-full" :class="{ 'p-invalid': errorMessage }"
          rows="2" :disabled="true" auto-resize
        />
        <Message v-if="errorMessage" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <!-- 付款到期日期 -->
    <Field v-slot="{ errorMessage, handleChange }" name="paymentDueDate">
      <div class="form-group mb-2">
        <label for="paymentDueDate" class="form-label">Payment Due Date</label>
        <DatePicker
          id="paymentDueDate" v-model="props.formData.paymentDueDate" class="w-full"
          :class="{ 'p-invalid': errorMessage }" :min-date="dayjs().add(1, 'day').toDate()" date-format="dd/mm/yy"
          show-icon placeholder="Please Select" @value-change="handleChange"
        />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <!-- Xero 账户图表 -->
    <Field v-if="user?.xero_link" v-slot="{ errorMessage, handleChange }" name="xeroChartOfAccount">
      <div class="form-group mb-2">
        <label for="xeroChartOfAccount" class="form-label">Xero Chart of Account <span
          class="text-red-500"
        >*</span></label>
        <Select
          id="xeroChartOfAccount" v-model="props.formData.xeroChartOfAccount"
          :options="props.options.xeroChartOfAccounts" option-label="name" option-value="account_code"
          placeholder="- Please Select -" class="w-full" :class="{ 'p-invalid': errorMessage }"
          :loading="props.optionsLoading.loadingXeroChartOfAccounts" @value-change="handleChange"
        />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <!-- Xero 发票模板 -->
    <Field v-if="user?.xero_link" v-slot="{ errorMessage, handleChange }" name="xeroInvoiceTemplate">
      <div class="form-group mb-2">
        <label for="xeroInvoiceTemplate" class="form-label">Xero Invoice Template <span
          class="text-red-500"
        >*</span></label>
        <Select
          id="xeroInvoiceTemplate" v-model="props.formData.xeroInvoiceTemplate"
          :options="props.options.xeroInvoiceTemplates" option-label="name" option-value="theme_id"
          placeholder="- Please Select -" class="w-full" :class="{ 'p-invalid': errorMessage }"
          :loading="props.optionsLoading.loadingXeroInvoiceTemplates" @value-change="handleChange"
        />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <!-- 付款金额 -->
    <Field v-slot="{ errorMessage, handleChange, meta }" name="paymentAmount">
      <div class="form-group mb-1">
        <label for="paymentAmount" class="form-label">
          Payment amount
        </label>
        <InputNumber
          id="paymentAmount" v-model="props.formData.paymentAmount" class="w-full"
          :class="{ 'p-invalid': errorMessage }" mode="currency" currency="AUD" locale="en-AU" :min="0" placeholder="0"
          :disabled="props.formData.addLineItems" @value-change="handleChange"
        />
        <Message v-if="errorMessage && meta.touched" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
        <div class="mt-1">
          <small class="text-gray-500">This field is required and should be a positive number.</small>
        </div>
      </div>
    </Field>

    <!-- 如果不勾选 GST 则显示 paymentAmount  -->
    <Field v-if="!values.isInclusiveGst && config" name="paymentAmountWithoutGst">
      <div class="form-group mb-2 flex items-center">
        <label for="paymentAmountWithoutGst" class="form-label !mb-0">
          <template v-if="props.optionsLoading.loadingConfig">
            <ProgressSpinner style="width: 40px; height: 40px;" />
          </template>
          <template v-else>
            Payment Amount Without GST ({{ config?.gst?.fee_rate === SurchargeRate.PERCENTAGE ? `${config?.gst?.fee_value}%` : `$${config?.gst?.fee_value}` }})
          </template>
        </label>
        <template v-if="props.optionsLoading.loadingConfig">
          <ProgressSpinner style="width: 40px; height: 40px;" />
        </template>
        <template v-else>
          <span v-if="config?.gst?.fee_rate === SurchargeRate.PERCENTAGE" class="ml-2">
            {{ Format.formatAmount(new Decimal(values?.paymentAmount || 0).times(new Decimal(config?.gst?.fee_value).div(100).add(1).toNumber()).toFixed(2)) }}
          </span>
          <span v-if="config?.gst?.fee_rate === SurchargeRate.FIXED" class="ml-2">
            {{ Format.formatAmount(new Decimal(values?.paymentAmount || 0).plus(new Decimal(config?.gst?.fee_value)).toFixed(2)) }}
          </span>
        </template>
      </div>
    </Field>

    <!-- GST -->
    <Field v-slot="{ field, handleChange }" name="isInclusiveGst">
      <div class="form-group mb-2">
        <div class="flex items-center">
          <Checkbox
            v-model="field.value" input-id="isInclusiveGst" :binary="true" class="mr-2" @value-change="
              (e: boolean) => {
                handleChange(e)
              }
            "
          />
          <label for="isInclusiveGst">GST Inclusive</label>
        </div>
      </div>
    </Field>

    <!-- 参考 -->
    <Field v-slot="{ errorMessage, handleChange }" v-model="props.formData.reference" name="reference">
      <div class="form-group mb-2">
        <label for="reference" class="form-label">
          Reference
        </label>
        <InputText
          id="reference" v-model="props.formData.reference" class="w-full"
          :class="{ 'p-invalid': errorMessage }" maxlength="255" @value-change="handleChange"
        />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <!-- 添加明细项目 -->
    <!-- <Field v-slot="{ field, handleChange }" v-model="props.formData.addLineItems" name="addLineItems">
      <div class="form-group mb-2">
        <div class="flex items-center">
          <Checkbox
            v-model="field.value" input-id="addLineItems" :binary="true" class="mr-2"
            @value-change="handleChange"
          />
          <label for="addLineItems">Add Line Items?</label>
        </div>
      </div>
    </Field> -->
    <!-- 明细项目列表 -->
    <FieldArray v-slot="{ fields, push, remove }" name="lineItems">
      <div v-show="values?.addLineItems" class="line-items-section mb-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-lg font-medium">
            Add Line Item
          </h4>
        </div>

        <div v-for="(field, index) in fields" :key="field.key" class="line-item-row mb-3">
          <div class="grid grid-cols-12 gap-3 items-start">
            <div class="col-span-7">
              <Field
                v-slot="{ field: descField, errorMessage, handleChange, meta }"
                :name="`lineItems[${index}].description`"
              >
                <InputText
                  v-model="descField.value" placeholder="Description" class="w-full"
                  :class="{ 'p-invalid': errorMessage }" maxlength="255" @value-change="handleChange"
                />
                <Message v-if="errorMessage && meta.touched" class="mt-2" severity="error" variant="simple">
                  {{ errorMessage }}
                </Message>
              </Field>
            </div>
            <div class="col-span-4">
              <Field
                v-slot="{ field: amountField, errorMessage, handleChange, meta }"
                :name="`lineItems[${index}].amount`"
              >
                <InputNumber
                  v-model="amountField.value" placeholder="Amount" class="w-full"
                  :class="{ 'p-invalid': errorMessage }" mode="currency" currency="AUD" locale="en-AU" :min="0"
                  :invalid="!!errorMessage" @value-change="handleChange"
                />
                <Message v-if="errorMessage && meta.touched" class="mt-2" severity="error" variant="simple">
                  {{ errorMessage }}
                </Message>
              </Field>
            </div>
            <div class="col-span-1">
              <Button
                v-if="fields.length > 1" type="button" icon="pi pi-trash" class="p-button-text p-button-danger"
                @click="removeLineItem(remove, index)"
              />
            </div>
          </div>
        </div>

        <Button type="button" label="Add" icon="pi pi-plus" class="p-button-outlined" @click="addLineItem(push)" />
      </div>
    </FieldArray>

    <!-- 注意事项 -->
    <!-- <div v-if="!user?.xero_link" class="notice-section">
      <span>
        <strong>NOTE</strong> Your invoice will be generated upon completion and can be found in your Invoice List. If
        you’ve connected your Xero account, the invoice will also appear in Xero for easy reconciliation.
      </span>
      <span class="mb-0">
        Not Integrated?
        <router-link class="font-bold" :to="{ name: 'integrations' }">
          Connect your Xero account here
        </router-link>
        to get started.
      </span>
    </div> -->

    <!-- 表单按钮 -->
    <div class="form-actions gap-4 flex justify-end mt-4">
      <Button type="button" label="Cancel" severity="secondary" class="w-35" @click="$router.back()" />
      <Button
        type="submit" label="Next" severity="warn" class="min-w-35"
        :loading="props.optionsLoading.createInvoice || props.optionsLoading.updateInvoice"
        :disabled="props.optionsLoading.createInvoice || props.optionsLoading.updateInvoice"
      />
    </div>
    <Dialog
      v-model:visible="isShowAddCustomer" style="max-width: 80vh;min-width: 400px;" header="Add Customer"
      :modal="true"
    >
      <VeeForm
        ref="addCustomerFormRef" :validation-schema="addCustomerSchema" :initial-values="addCustomerModel"
        class="create-invite-base-form space-y-4 min-h-50 flex-1" @submit="handleAddCustomer"
      >
        <Field v-slot="{ field, errorMessage, handleChange }" name="name">
          <div class="form-group mb-2">
            <label for="name" class="form-label">
              Name<span class="text-red-500">*</span>
            </label>
            <InputText
              id="name" v-model="field.value" class="w-full" :class="{ 'p-invalid': errorMessage }"
              maxlength="255" @value-change="handleChange"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>
        <Field v-slot="{ field, errorMessage, handleChange }" name="email">
          <div class="form-group mb-2">
            <label for="email" class="form-label">
              Email<span class="text-red-500">*</span>
            </label>
            <InputText
              id="email" v-model="field.value" class="w-full" :class="{ 'p-invalid': errorMessage }"
              maxlength="255" @value-change="handleChange"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>
        <Field v-slot="{ field, errorMessage, handleChange }" name="phone">
          <div class="form-group mb-2">
            <label for="phone" class="form-label">
              Phone
            </label>
            <InputText
              id="phone" v-model="field.value" class="w-full" :class="{ 'p-invalid': errorMessage }"
              maxlength="255" @value-change="handleChange"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>
      </VeeForm>
      <template #footer>
        <Button type="button" label="CANCEL" severity="secondary" class="w-35" @click="isShowAddCustomer = false" />
        <Button
          type="submit" label="ADD CUSTOMER" severity="warn" class="min-w-35" :loading="isShowAddCustomerLoading"
          @click="handleAddCustomer"
        />
      </template>
    </Dialog>
  </VeeForm>
</template>

<style lang="scss" scoped>
.create-invoice-form {
  background-color: var(--color-white);
  padding: 20px;
  border-radius: 10px;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-group {
  margin-bottom: 1rem;
}

.alert-info {
  font-size: 13px;
  background: #FFE3E8;
  color: var(--color-gray);
}

.line-item-row {
  padding: 1rem;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
}

.notice-section {
  border: 2px solid #FE4C1C;
  padding: 4px 8px;
  font-size: 12px;
}

:deep(.p-textarea) {
  border-radius: 10px;
}

:deep(.p-inputnumber) {
  .p-inputtext {
    height: 44px;
    border-radius: 10px !important;
  }
}

:deep(.p-inputtext) {
  height: 44px;
  border-radius: 10px;
}

:deep(.p-select) {
  border-radius: 10px;
}

:deep(.p-select-label) {
  height: 44px;

  line-height: 30px;
}

:deep(.p-button) {
  font-size: 16px;
  padding: 8px 16px;
}

/* 确保输入框不会超出单元格 */
.tiered-pricing-table :deep(.p-inputnumber),
.tiered-pricing-table :deep(.p-inputtext) {
  width: 100%;
  max-width: 100%;
}
</style>
