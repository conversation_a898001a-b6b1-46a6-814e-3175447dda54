<script setup lang="ts">
import logo from '@/assets/BillbuddyLogo.png'
// 定义组件属性
defineProps({
  formData: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  options: {
    type: Object,
    default: () => ({
      status: [],
      process_type: [],
      currency: [],
      billing_period: [],
      billing_period_custom_unit: [],
      one_off_pricing_model: [],
      recurringTieredPaymentMethod: [],
    }),
  },
  optionsLoading: {
    type: Function,
    default: () => {
      return () => false
    },
  },
})
</script>

<template>
  <div class="p-4 bg-white rounded-2xl h-full">
    <div class="flex justify-center mb-4">
      <img :src="logo" alt="logo" class="w-100">
    </div>
  </div>
</template>

<style scoped>

</style>
