<script setup lang="ts">
import type { DictItem } from '@/services/api/dict'
import { Format } from '@shared'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import BaseDataTable from '@/components/common/BaseDataTable.vue'
import { useDict } from '@/composables/useDict'
import { useExport } from '@/composables/useExport'
import { useRequestList } from '@/composables/useRequestList'
import { SearchFieldType } from '@/constants/search'
import { TransactionType } from '@/constants/transaction'
import { invoice as invoiceApi } from '@/services/api'
import { formatDate } from '@/utils/date'
import { addAllToDict } from '@/utils/dict'
import { getInvoiceTagStatus } from '@/utils/tagStatus'

defineOptions({
  name: 'InvoicesList',
})

// 使用 useRequestList 处理交易列表数据
const requestList = useRequestList<Invoice.Info[], Api.InvoiceListReq>({
  requestFn: invoiceApi.getInvoiceList,
})

// Setup export functionality
const { isExporting, handleExport } = useExport({
  exportFn: invoiceApi.exportInvoices,
  getParams: () => {
    return setSearchParams(searchModel.value)
  },
  onExportStart: () => {
    window.$toast.add({
      severity: 'info',
      summary: 'Export Started',
      detail: 'Preparing your export file...',
      life: 3000,
    })
  },
})

const router = useRouter()
const route = useRoute()

const {
  list,
  loading,
  total,
  refresh,
  search,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
  other,
  setSearchParams,
} = requestList

// 列配置
const columns = ref<TableColumnItem[]>([
  {
    field: 'invoice_number',
    header: 'Invoice Number',
    style: { minWidth: '110px' },
  },
  {
    field: 'customer_name',
    header: 'Customer',
    style: { minWidth: '110px' },
    template: 'customer',
  },
  {
    field: 'service_type',
    header: 'Service Type',
    style: { minWidth: '110px' },
    template: 'service_type',
  },
  {
    field: 'reference',
    header: 'Reference',
    style: { minWidth: '110px' },
  },
  {
    field: 'status',
    header: 'Status',
    style: { width: '100px' },
    template: 'status',
  },
  {
    field: 'payment_amount',
    header: 'Amount',
    style: { minWidth: '110px' },
    template: 'payment_amount',
    sortable: true,
  },
  { field: 'due_date', header: 'Due date', style: { minWidth: '120px' }, template: 'due_date' },
  { field: 'credit_brand', header: 'Payment Method', template: 'payment_method', style: { minWidth: '120px' } },
  { field: 'creation_date', header: 'Creation Date', style: { minWidth: '120px' }, template: 'creation_date' },
  { field: 'email_status', header: 'Sent', style: { minWidth: '90px' }, template: 'email_status' },
])

const searchModel = ref<Api.InvoiceListReq>({
  'status': null,
  'amount': '',
  'created_at[]': [],
  'credit_brand': '',
  'service_type': '',
  'invoice_number': '',
})

const statusOptions = ref<DictItem[]>([])

const statusFilterOptions = ref<DictItem[]>([])

const paymentTypeOptions = ref<DictItem[]>([])

const subscriptionOptions = ref<DictItem[]>([])

// const dateRange = ref<string[]>([])

const searchFields = computed(() => [
  {
    name: 'invoice_number',
    label: 'Invoice Number',
    type: SearchFieldType.TEXT,
    placeholder: 'Enter invoice number',
    defaultValue: '',
  },
  {
    name: 'status',
    label: 'Status',
    type: SearchFieldType.SELECT,
    placeholder: 'All',
    options: statusOptions.value,
    defaultValue: null,
  },
  {
    name: 'credit_brand',
    label: 'Payment Method',
    type: SearchFieldType.SELECT,
    options: paymentTypeOptions.value,
    defaultValue: null,
  },

])

const advancedSearchFields = computed(() => [
  {
    name: 'created_at[]',
    label: 'Date and time',
    type: SearchFieldType.DATE_RANGE,
    placeholder: 'Please select date range',
    defaultValue: [],
  },
  {
    name: 'amount',
    label: 'Amount',
    type: SearchFieldType.NUMBER,
    placeholder: 'Please enter amount',
    defaultValue: '',
    minFractionDigits: 2,
  },
  {
    name: 'service_type',
    label: 'Service type',
    type: SearchFieldType.SELECT,
    options: subscriptionOptions.value,
    defaultValue: null,
  },
])

// 搜索处理
const handleSearch = () => {
  setSearchParams(searchModel.value)
  search()
}

// 手动刷新数据
const refreshData = () => {
  refresh()
}

// 排序处理
const handleSort = (event: any) => {
  const { sortField, sortOrder } = event
  setSearchParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  search()
}

// Setup export functionality
// const { isExporting, handleExport } = useExport({
//   exportFn: invoiceApi.exportTransactions,
//   getParams: () => {
//     return setSearchParams(searchModel.value)
//   },
//   onExportStart: () => {
//     window.$toast.add({
//       severity: 'info',
//       summary: 'Export Started',
//       detail: 'Preparing your export file...',
//       life: 3000,
//     })
//   },
// })

const toDetail = ({ data }: { data: Invoice.Info }) => {
  router.push({
    name: 'payMyInvoiceInvoiceDetail',
    params: {
      id: data.id,
    },
  })
}

const { getLabel: getInvoiceStatusLabel } = useDict('invoice_status', (res) => {
  statusOptions.value = res
  statusFilterOptions.value = addAllToDict(res, { label: 'All', value: null }).filter(item => item.value !== 3)
})

const { getLabel: getInvoiceEmailStatusLabel } = useDict('invoice_email_sent')

useDict('credit_brand', (res) => {
  paymentTypeOptions.value = addAllToDict(res)
})

const { getLabel: getServiceTypeLabel } = useDict('invoice_service_type', (res) => {
  subscriptionOptions.value = addAllToDict(res)
})

const handleStatCardClick = (item: { status: number, status_text: string, count: number }) => {
  if (searchModel.value.status === item.status) {
    return
  }
  searchModel.value.status = item.status
  setSearchParams(searchModel.value)
  refresh()
}

onMounted(() => {
  const query = route?.query
  if (query && query.status !== undefined) {
    searchModel.value.status = Number(query.status)
    setSearchParams({
      status: Number(query?.status),
    })

    search()
  }
  else {
    setSearchParams({
      status: undefined,
    })
    search()
  }
})
</script>

<template>
  <div>
    <div v-if="other?.stat" class="common-stat-card">
      <div
        v-for="(item, index) in other?.stat" :key="index" class="common-stat-card__item"
        :class="{ active: searchModel.status === item.status }" @click="handleStatCardClick(item)"
      >
        <div class="common-stat-card__title">
          {{ item.status_text }}
        </div>
        <div class="common-stat-card__count">
          {{ item.count }}
        </div>
      </div>
    </div>

    <BaseSearch
      v-model="searchModel" :loading="loading" :basic-search-fields="searchFields"
      :advanced-search-fields="advancedSearchFields" class="invoice-list-search" @search="handleSearch"
    />
    <div class="flex items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8">
      <BaseExportDialog
        :loading="loading"
        :export-loading="isExporting"
        @export="handleExport"
      />
    </div>
    <BaseDataTable
      :row-hover="true" :value="list" :columns="columns" :show-edit-column="false" :show-search-bar="false"
      :scrollable="true" :show-multiple-column="true" :loading="loading" :paginator="true" :rows="50"
      :total-records="total" :lazy="true" data-key="id" sort-mode="single" search-placeholder="Search"
      type-placeholder="Filter By" :failed="failed" :failure-message="failureMessage" :striped-rows="true"
      @page="handlePageChange" @sort="handleSort" @refresh="refreshData" @row-click="toDetail"
    >
      <template #payment_amount="{ data }">
        {{ Format.formatAmount(data?.sub_total) }}
      </template>
      <template #date="{ data }">
        <span v-if="data?.trans_type === TransactionType.Payment">
          {{ formatDate(data.created_at) }}
        </span>
      </template>
      <template #refunded_date="{ data }">
        <span v-if="data?.trans_type === TransactionType.Refund">
          {{ formatDate(data.created_at) }}
        </span>
      </template>
      <template #payment_method="{ data }">
        <BaseCardType
          :text="data.payment_method?.account_no" :card-type="data.payment_method?.credit_brand"
          :is-show-card-number="true"
        />
      </template>
      <template #customer="{ data }">
        <span v-if="data?.customer_name" class="w-32 underline text-[#FE4C1C]">
          {{ data?.customer_name }}
        </span>
      </template>
      <template #status="{ data }">
        <BaseTag :type="getInvoiceTagStatus(data.status)" :text="getInvoiceStatusLabel(data.status)" class="w-24" />
      </template>
      <template #creation_date="{ data }">
        <span v-if="data?.created_at">
          {{ formatDate(data.created_at) }}
        </span>
      </template>
      <template #due_date="{ data }">
        <span v-if="data?.due_date">
          {{ formatDate(data.due_date) }}
        </span>
      </template>
      <template #service_type="{ data }">
        {{ getServiceTypeLabel(data.service_type) }}
      </template>
      <template #email_status="{ data }">
        {{ getInvoiceEmailStatusLabel(data?.is_send) }}
      </template>
    </BaseDataTable>
  </div>
</template>

<style lang="scss" scoped>
.card {
  padding: 1rem;
}

.p-button {
  padding: 0.5rem 1rem;
  font-weight: 500;
}

.p-button-text {
  color: var(--text-color-secondary);
}

.p-button-text:hover {
  background: var(--surface-hover);
  color: var(--text-color);
}

.invoice-list-search {
  :deep(.p-inputtext.p-component.p-inputnumber-input) {
    width: 200px;
    height: 44px;
    border-radius: 12px;
    color: #fe4c1c;
  }

  :deep(.search-form-input-group) {

    .pi.pi-search.search-form-input-icon {
      display: none;
    }

    .p-inputtext.search-form-input {
      width: 170px;
      height: 44px;
      padding-left: .75rem;
      font-size: 1rem;
      background-color: #ffffff;
      color: #fe4c1c;
    }

  }

  :deep(.search-field-container) {
    .p-inputtext.p-datepicker-input {
      background-color: #ffffff;
      color: #fe4c1c;
      width: 205px;
    }
  }

  :deep(.search-form-container) {
    gap: 1.2rem;
  }

  :deep(.search-btn-container) {
    .p-button-icon.p-button-icon-left.pi-search {
      display: none;
    }
  }

  :deep(.more-filters-container) {
    .p-button-text.more-filters-btn {
      color: #FE4C1C;
    }
  }
}
</style>
