<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getInvoiceDashboard } from '@/services/api/invoice'

const route = useRoute()
const router = useRouter()
// const closeAnnouncement = ref(false)
const overviewData = ref<Invoice.DashboardData[]>()
const viewPayInvoiceList = (status: number | null) => {
  router.push({
    name: 'payMyInvoiceInvoiceList',
    query: status !== null ? { status } : undefined,
  })
}
onMounted(() => {
  getInvoiceDashboard().then((res) => {
    overviewData.value = res.data
  })
})
</script>

<template>
  <div v-if="route.name === 'payMyInvoice'">
    <div class="title">
      <span class="mt-2"> Welcome to</span>
      <img src="@/assets/PMI-logo.png" alt="PMI" class="logo">
    </div>
    <div class="invoice-dashboard-container mt-4">
      <div class="invoice-dashboard-header">
        <div class="hub">
          <div class="header-subtitle">
            Invoice Hub
          </div>
          <div class="py-5">
            <p class="pb-6 text-part">
              PayMyInvoice lets you send digital invoices directly to your customers, making it easy for them to pay you
              online via card, bank transfer, or any available payment method you've enabled. Whether you're billing for
              services or issuing one-off charges, this feature gives you full visibility and controls.
            </p>
            <div class="flex flex-col gap-y-10">
              <div class="flex  items-center operate-group">
                <Button
                  label="CREATE INVOICE" severity="warn" class="invoice-btn "
                  @click="$router.push({ name: 'payMyInvoiceCreateInvoice' })"
                />
                <p class="text-part whitespace-pre-line">
                  Here you can create your one-off invoices to your customers.
                </p>
              </div>
              <div class="flex  items-center operate-group">
                <Button
                  label="INVOICE LIST" class="invoice-btn "
                  @click="$router.push({ name: 'payMyInvoiceInvoiceList' })"
                />
                <p class="text-part whitespace-pre-line">
                  Here you can find all invoices for both PMI invoices and your recurring invoices.
                </p>
              </div>
              <div v-if="false" class="flex  items-center operate-group">
                <Button
                  label="INTEGRATIONS" severity="info" class="invoice-btn "
                  @click="$router.push({ name: 'integrations' })"
                />
                <p class="text-part whitespace-pre-line">
                  Integrate with your accounting software here.
                </p>
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="announcenment w-1/3">
          <div class="header-subtitle flex justify-between items-center">
            <div class="flex gap-4 items-center">
              <i class="pi pi-exclamation-circle" style="color: #ef4f27;" />
              <span>New Announcement</span>
            </div>
            <i
              class="pi-chevron-up pi collapse-btn" :aria-expanded="!closeAnnouncement"
              @click="closeAnnouncement = !closeAnnouncement"
            />
          </div>
          <div class="announcenment-content " :class="{ active: !closeAnnouncement }">
            <div class="announcenment-content-title">
              New Integration:Xero is now Live!
            </div>
            <div class="announcenment-content-detail">
              <p>We’re excited to announce that Bill Buddy now integrates with Xero!</p>
              <p>
                With this integration, your transaction data, invoices, and customer payments within the portal can be
                seamlessly synced to your Xero account — saving you time and making financial management even easier.
              </p>
              <p>
                To get started, simply <span
                  class="underline font-semibold text-[#181349] cursor-pointer"
                  @click="$router.push({ name: 'integrations' })"
                >
                  connect your Xero account on the Integrations page.
                </span>
              </p>
            </div>
          </div>
        </div> -->
      </div>
      <div class="overview">
        <div v-for="(item, index) in overviewData" :key="index" class="overview-item">
          <div class="overview-item-title">
            {{ item?.status_text }}
          </div>
          <div class="overview-item-content">
            <div class="overview-item-count">
              {{ item?.count }}
            </div>
            <div class="flex justify-between text-[#545454]" @click="viewPayInvoiceList(item?.status)">
              <span class="cursor-pointer">View all</span>
              <i class="pi pi-chevron-right cursor-pointer" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <RouterView v-else />
</template>

<style scoped lang="scss">
@use '@/styles/mixins/breakpoints' as *;

.title {
  font-weight: 700;
  font-size: 34px;
  padding-left: 1rem;
  display: flex;
  align-items: center;

  span {
    @include media-breakpoint-down(sm) {
      font-size: 25px;
    }
  }

  .logo {
    height: 84px;

    @include media-breakpoint-down(sm) {
      height: 58px;
    }
  }
}

.collapse-btn[aria-expanded='true'] {
  transition: transform .3s ease;
  transform: rotate(180deg);
}

.collapse-btn[aria-expanded='false'] {
  transition: transform .3s ease;
  transform: rotate(0deg);
}

.operate-group {
  @include media-breakpoint-down(xl) {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
}

.invoice-btn {
  display: inline-block;
  padding: .7rem 2rem;
  font-weight: 700;
  font-size: 17px;
  width: 190px;
  margin-right: 70px;

  @include media-breakpoint-down(xl) {
    font-size: 1rem;
    padding: .7rem .7rem;
    margin-right: 20px;
  }

  @include media-breakpoint-down(xxl) {
    width: 150px;
    font-size: 16px;
    padding: .7rem 1rem;
    margin-right: 20px;
  }

  @include media-breakpoint-down(xxxl) {
    width: 170px;
    font-size: 16px;
    padding: .7rem 1.5rem;
    margin-right: 20px;
  }

}

.text-part {
  color: #545454;
  font-size: 16px;

}

.invoice-dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: start;
  // grid-template-columns: 2fr 1fr;
  // column-gap: 40px;
  gap: 40px;

  @include media-breakpoint-down(md) {
    display: flex;
    flex-direction: column;

  }

  .hub,
  .announcenment {
    background-color: #ffffff;
    border-radius: 10px;
    padding: 1rem 1.8rem;
    padding-bottom: 2rem;

    @include media-breakpoint-down(md) {
      width: 100%;

    }

    .header-subtitle {
      font-weight: 700;
      font-size: 18px;
      border-bottom: 1px solid #A6A6A6;
      padding-bottom: 1rem;

      @include media-breakpoint-down(md) {
        font-size: 16px;
      }
    }
  }
}

.announcenment-content {
  font-size: 16px;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;

  &-title {
    font-weight: bold;
    margin: 1rem 0;
  }

  &-detail {
    p {
      margin: 1rem 0;
      color: #545454;
    }
  }
}

.announcenment-content.active {
  max-height: 500px;
  transition: max-height 0.3s ease-in;
}

.overview {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  margin-top: 2rem;
  gap: 30px;

  @include media-breakpoint-down(md) {
    grid-template-columns: repeat(2, 1fr);
  }

  @include media-breakpoint-down(xl) {
    gap: 20px
  }

  @include media-breakpoint-down(sm) {
    grid-template-columns: repeat(1, 1fr);
  }

  &-item-title {
    font-weight: 700;
    font-size: 18px;
    margin-bottom: .7rem;

    @include media-breakpoint-down(xl) {
      font-size: 16px;
    }
  }

  &-item-content {
    background-color: #ffffff;
    border-radius: 10px;
    padding: 2rem;
  }

  &-item-count {
    font-weight: 700;
    font-size: 50px;
    margin-bottom: 1.2rem;
  }

}
</style>
