import type { Form as VeeForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/yup'
import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import * as yup from 'yup'
import { customer as customerApi, integrations as integrationsApi, invoice as invoiceApi, user as userApi } from '@/services/api'
import { useUserStore } from '@/store/modules/user'

interface FormData {
  selectCustomer: string
  firstName: string
  lastName: string
  email: string
  phone: string
  address: string
  paymentDueDate: Date
  xeroChartOfAccount: string
  xeroInvoiceTemplate: string
  paymentAmount: number | string
  reference: string
  addLineItems: boolean
  lineItems: Array<{
    description: string
    amount: number | string
  }>
  isInclusiveGst: boolean
}

export function useCreateInvoice(mode: 'add' | 'addCustomer' | 'edit') {
  const business_id = ref<string | null>('')

  const formRef = ref<InstanceType<typeof VeeForm>>()

  const userStore = useUserStore()

  const config = ref<Api.MerchantConfigItem | null>(null)

  const { user, activeBid } = storeToRefs(userStore)

  const router = useRouter()
  const route = useRoute()

  const invoiceData = ref<Invoice.Info | null>(null)

  const loadings = ref<{
    createInvoice: boolean
    updateInvoice: boolean
    loadingConfig: boolean
    loadingCustomers: boolean
    loadingXeroChartOfAccounts: boolean
    loadingXeroInvoiceTemplates: boolean
    loadingDetail: boolean
  }>({
    createInvoice: false,
    updateInvoice: false,
    loadingCustomers: true,
    loadingConfig: true,
    loadingXeroChartOfAccounts: true,
    loadingXeroInvoiceTemplates: true,
    loadingDetail: false,
  })

  const options = ref<{
    customers: Customer.Info[]
    xeroChartOfAccounts: Api.XeroChartOfAccountsRes[]
    xeroInvoiceTemplates: Api.XeroInvoiceTemplatesRes[]
  }>({
    customers: [],
    xeroChartOfAccounts: [],
    xeroInvoiceTemplates: [],
  })

  const formData = ref<FormData>({
    selectCustomer: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    paymentDueDate: dayjs().add(1, 'day').toDate(),
    xeroChartOfAccount: '',
    xeroInvoiceTemplate: '',
    paymentAmount: 0,
    reference: '',
    addLineItems: true,
    lineItems: [
      {
        description: '',
        amount: '',
      },
    ],
    isInclusiveGst: false,
  })

  const schema = toTypedSchema(yup.object({
    selectCustomer: mode === 'addCustomer' ? yup.string().optional() : yup.string().required('Customer is required'),
    paymentDueDate: yup.date().required('Payment Due Date is required').min(dayjs().toDate(), 'Payment Due Date must be at least tomorrow'),
    xeroChartOfAccount: yup.mixed().test(
      'xeroChartOfAccount',
      'Xero Chart of Account is required',
      (value) => {
        if (user?.value?.xero_link && !value) {
          return false
        }

        return true
      },
    ),
    xeroInvoiceTemplate: yup.mixed().test(
      'xeroInvoiceTemplate',
      'Xero Invoice Template is required',
      (value) => {
        if (user?.value?.xero_link && !value) {
          return false
        }

        return true
      },
    ),
    // paymentAmount: yup.mixed().test(
    //   'required-positive',
    //   'This field is required and should be a positive number',
    //   (value) => {
    //     if (typeof value === 'number') {
    //       return value > 0
    //     }
    //     if (typeof value === 'string') {
    //       return value.length > 0 && !Number.isNaN(Number(value)) && Number(value) > 0
    //     }
    //     return false
    //   },
    // ),
    // the payment amount is number or string
    paymentAmount: yup.number().notRequired(),
    reference: yup.string().notRequired(),
    addLineItems: yup.boolean().default(false),
    lineItems: yup.array().when('addLineItems', {
      is: true,
      then: schema => schema.of(yup.object({
        description: yup.string().required('Description is required'),
        amount: yup
          .number()
          .typeError('Amount is required')
          .min(1, 'Please enter a positive number')
          .required('Amount is required'),
      })).min(1, 'Line items are required when "Add Line Items" is enabled'),
      otherwise: schema => schema.of(yup.object({
        description: yup.string().notRequired(),
        amount: yup.mixed().notRequired(),
      })).notRequired(),
    }),
    isInclusiveGst: yup.boolean().optional(),
  }))

  const handleSubmit = (values: FormData) => {
    const lineItems: Api.XeroCreateInvoiceLineItem[] = []

    if (values.addLineItems) {
      values.lineItems.forEach((item: any) => {
        lineItems.push({
          description: item.description,
          unit_amount: item.amount,
        })
      })
    }

    const sendData = {
      customer_id: values.selectCustomer,
      due_date: dayjs(values.paymentDueDate).format('YYYY-MM-DD'),
      line_items: lineItems,
      reference: values?.reference,
      theme_id: values?.xeroInvoiceTemplate,
      is_inclusive_gst: values.isInclusiveGst ? 1 : 0,
      is_line_items: values.addLineItems ? 1 : 0,
      account_code: values.xeroChartOfAccount,
      payment_amount: String(values.paymentAmount),
    } as Api.XeroCreateInvoiceReq

    loadings.value.createInvoice = true

    integrationsApi.createXeroInvoice(sendData, {
      headers: {
        'Business-Id': business_id.value,
      },
    }).then((res) => {
      if (res.code === 0 && (mode === 'add' || mode === 'addCustomer')) {
        window.$toast.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Invoice created successfully',
        })
        router.replace({
          name: 'payMyInvoiceCreateAndSend',
          params: {
            id: res.data.id,
          },
        })
      }
      else if (res.code === 0 && mode === 'addCustomer') {
        window.$toast.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Invoice created successfully',
        })
        router.back()
      }
    }).finally(() => {
      loadings.value.createInvoice = false
    })
  }

  const handleEditSubmit = async (values: FormData) => {
    const lineItems: Api.XeroCreateInvoiceLineItem[] = []

    if (values.addLineItems) {
      values.lineItems.forEach((item: any) => {
        lineItems.push({
          line_item_id: item?.id,
          description: item.description,
          unit_amount: item.amount,
        })
      })
    }

    const sendData = {
      id: Number(route.params.id),
      customer_id: values.selectCustomer,
      due_date: dayjs(values.paymentDueDate).format('YYYY-MM-DD'),
      reference: values?.reference,
      line_items: lineItems,
      is_inclusive_gst: values.isInclusiveGst ? 1 : 0,
      is_line_items: values.addLineItems ? 1 : 0,
      account_code: values.xeroChartOfAccount,
      payment_amount: String(values.paymentAmount),
    } as Api.XeroEditInvoiceReq

    try {
      loadings.value.updateInvoice = true

      const { code, data } = await integrationsApi.updateXeroInvoice(sendData)
      if (code === 0) {
        window.$toast.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Invoice updated successfully',
        })
        router.replace({
          name: 'payMyInvoiceEditAndSend',
          params: {
            editId: route.params.id,
            id: data.id,
          },
        })
      }
    }
    finally {
      loadings.value.updateInvoice = false
    }
  }

  const updateCustomers = async () => {
    try {
      const res = await customerApi.getAllCustomer({}, {
        headers: {
          'Business-Id': business_id.value,
        },
      })
      options.value.customers = res.data

      // Populate customer details if available
      if (invoiceData.value?.customer && mode === 'edit') {
        const customer = options.value.customers.find((customer: any) => customer.customer_id === invoiceData.value?.customer_id)
        if (customer) {
          const names = String(customer.name)?.split(' ')
          formData.value.firstName = names[0] || ''
          formData.value.lastName = names[1] || ''
          formData.value.email = customer.email_primary
          formData.value.phone = customer.phone_mobile
          formData.value.address = customer.address || ''
        }
      }
    }
    finally {
      loadings.value.loadingCustomers = false
    }
  }

  const getInvoiceInfo = async () => {
    try {
      loadings.value.loadingDetail = true
      const { data: invoiceResData } = await invoiceApi.getInvoiceDetail({ id: Number(route.params.id) })
      invoiceData.value = invoiceResData
      business_id.value = invoiceResData.business_id as string

      // Populate form data with backend details
      formData.value.selectCustomer = invoiceResData.customer_id
      formData.value.paymentDueDate = new Date(invoiceResData.due_date)
      formData.value.reference = invoiceResData.reference
      formData.value.addLineItems = invoiceResData.is_line_items === 1
      formData.value.isInclusiveGst = invoiceResData.is_inclusive_gst === 1

      // Populate line items if available
      if (invoiceResData.line_items && invoiceResData.is_line_items === 1) {
        formData.value.addLineItems = true
        formData.value.lineItems = invoiceResData.line_items.map(item => ({
          id: item.line_item_id,
          description: item.description,
          amount: item.unit_amount,
        }))
        // Set account code from first line item
        if (invoiceResData.line_items[0].account_code) {
          formData.value.xeroChartOfAccount = invoiceResData.line_items[0].account_code
        }
        if (invoiceResData.branding_theme_id) {
          formData.value.xeroInvoiceTemplate = invoiceResData.branding_theme_id
        }
      }
      else {
        // If no line items, set payment amount from subtotal
        formData.value.paymentAmount = invoiceResData.sub_total || 0
      }
    }
    finally {
      loadings.value.loadingDetail = false
    }
  }

  const setFormRef = (el?: InstanceType<typeof VeeForm>) => {
    if (el) {
      formRef.value = el
    }
  }

  onMounted(async () => {
    if (['add'].includes(mode)) {
      loadings.value.loadingConfig = true
      const { data: { fee_config = [] } } = await userApi.getMerchantConfig()
      userStore.showSelectBid(() => {
        business_id.value = activeBid.value
        const findConfig = fee_config?.find(item => item.business_id === activeBid.value)
        if (findConfig) {
          config.value = findConfig
        }
        loadings.value.loadingConfig = false
        updateCustomers()
      })
    }

    if (['addCustomer'].includes(mode)) {
      business_id.value = activeBid.value
    }

    if (['edit'].includes(mode)) {
      const { data: { fee_config = [] } } = await userApi.getMerchantConfig()
      const findConfig = fee_config?.find(item => item.business_id === activeBid.value)
      if (findConfig) {
        config.value = findConfig
      }
      loadings.value.loadingConfig = false
      await getInvoiceInfo()
      await updateCustomers()
    }

    if (user?.value?.xero_link) {
      integrationsApi.getXeroChartOfAccounts().then((res) => {
        options.value.xeroChartOfAccounts = res.data
        loadings.value.loadingXeroChartOfAccounts = false
      })
      integrationsApi.getXeroInvoiceTemplates().then((res) => {
        options.value.xeroInvoiceTemplates = res.data
        loadings.value.loadingXeroInvoiceTemplates = false
      })
    }
  })

  return {
    // 表单数据
    schema,
    formData,
    options,
    loadings,
    config,

    handleSubmit,
    handleEditSubmit,
    updateCustomers,
    setFormRef,
  }
}
