<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/yup'
import { Decimal } from 'decimal.js'
import { Field, Form as VeeForm } from 'vee-validate'
import { computed, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import * as yup from 'yup'
import logo from '@/assets/BillbuddyLogo.png'
import creditCardNumberImage from '@/assets/merchant/invoice/invioce-payment.png'
import GoogleRecaptcha from '@/components/googleRecaptchaV2/index.vue'
import { integrations as integrationsApi } from '@/services/api'
import { formatDate } from '@/utils/date'

const { t } = useI18n()
const route = useRoute()

// Get invoice ID from route params
const invoiceId = ref(route.params.id as string)

// Form reference for validation
const formRef = ref()

// 控制发票详情显示
const showInvoiceDetails = ref(false)

// 加载状态
const isLoading = ref(false)
const isFetchingInvoice = ref(false)

enum PaymentMethod {
  CreditCard = 2,
  BankAccount = 1,
  DebitCard = 3,
}

// 支付结果状态
const paymentResult = reactive({
  show: false,
  success: false,
  message: '',
  amount: '',
  date: '',
})

// 控制表单显示
const showForm = computed(() => !paymentResult.show)

// reCAPTCHA refs and state
const recaptchaRef = ref<InstanceType<typeof GoogleRecaptcha> | null>(null)
const recaptchaVerified = ref(false)

const maxAmount = ref(************)

const validationSchema = toTypedSchema(yup.object({
  // invoiceNumber: yup.string().required('Invoice number is required'),
  // amount: yup
  //   .number()
  //   .typeError('Amount is required')
  //   .min(0.01, 'Amount must be greater than 0')
  //   .test('maxAmount', function (value) {
  //     if (value === undefined || value === null) { return true }
  //     if (value > maxAmount.value) {
  //       return this.createError({ message: `Amount must be less than ${maxAmount.value}` })
  //     }
  //     return true
  //   })
  //   .nullable()
  //   .required('Amount is required'),
  // email: yup.string().email('Please enter a valid email address').required('Please enter a valid email address'),
  name: yup.string().required('Name is required'),
  creditCardNumber: yup
    .string()
    .when('paymentMethod', {
      is: (val: number) => val === PaymentMethod.CreditCard || val === PaymentMethod.DebitCard,
      then: schema => schema
        .required('Card number must be between 13 and 19 digits')
        .min(13, 'Card number must be between 13 and 19 digits')
        .max(19, 'Card number must be between 13 and 19 digits')
        .test('luhn', 'Invalid card number', (value) => {
          if (!value) { return false }
          const cleanValue = value.replace(/\D/g, '')
          let sum = 0
          let shouldDouble = false
          for (let i = cleanValue.length - 1; i >= 0; i--) {
            let digit = Number.parseInt(cleanValue.charAt(i))
            if (shouldDouble) {
              digit *= 2
              if (digit > 9) { digit -= 9 }
            }
            sum += digit
            shouldDouble = !shouldDouble
          }
          return sum % 10 === 0
        }),
      otherwise: schema => schema.notRequired(),
    }),
  expiryDate: yup
    .string()
    .when('paymentMethod', {
      is: (val: number) => val === PaymentMethod.CreditCard || val === PaymentMethod.DebitCard,
      then: schema => schema
        .required('Expiry date must be in MM/YY format')
        .matches(/^\d{2}\/\d{2}$/, 'Expiry date must be in MM/YY format')
        .test('expiry', 'Card has expired or invalid date', (value) => {
          if (!value) { return false }
          const [month, year] = value.split('/')
          const currentDate = new Date()
          const currentYear = currentDate.getFullYear() % 100
          const currentMonth = currentDate.getMonth() + 1
          const expiryMonth = Number.parseInt(month)
          const expiryYear = Number.parseInt(year)
          if (expiryMonth < 1 || expiryMonth > 12) {
            return false
          }
          return !(expiryYear < currentYear || (expiryYear === currentYear && expiryMonth < currentMonth))
        }),
      otherwise: schema => schema.notRequired(),
    }),
  securityCode: yup
    .string()
    .when('paymentMethod', {
      is: (val: number) => val === PaymentMethod.CreditCard || val === PaymentMethod.DebitCard,
      then: schema => schema
        .required('Security code must be 3 or 4 digits')
        .matches(/^\d{3,4}$/, 'Security code must be 3 or 4 digits'),
      otherwise: schema => schema.notRequired(),
    }),
  google_token: yup.string().required('Please complete the reCAPTCHA verification'),
  // the length max is 6
  bsbNumber: yup.string().when('paymentMethod', {
    is: PaymentMethod.BankAccount,
    then: schema => schema.required('BSB Number is required').max(6, 'BSB Number must be less than 6 digits'),
    otherwise: schema => schema.notRequired(),
  }),
  accountName: yup.string().when('paymentMethod', {
    is: PaymentMethod.BankAccount,
    then: schema => schema.required('Account Name is required'),
    otherwise: schema => schema.notRequired(),
  }),
  accountNumber: yup.string().when('paymentMethod', {
    is: PaymentMethod.BankAccount,
    then: schema => schema.required('Account Number is required'),
    otherwise: schema => schema.notRequired(),
  }),
}))

// Form initial values
const initialValues = reactive({
  invoiceNumber: '',
  amount: 0 as number | null,
  paymentMethod: PaymentMethod.CreditCard,
  email: '',
  name: '',
  creditCardNumber: '',
  expiryDate: '',
  securityCode: '',
  recaptcha: false,

  bsbNumber: '',
  accountName: '',
  accountNumber: '',

  google_token: '',
})

// Invoice details
const invoiceDetails = reactive({
  total: '0',
  amountPaid: '0',
  amountDue: '0',
  surcharge_rate: '0',
  // 1 : 百分比 2 : 固定金额
  surcharge_type: '0',

  created_at: '',
  currency: 'AUD',
  customer: {
    name: '',
    customer_id: null,
  },
  customer_id: '',
  due_date: '',
  id: 0,
  invoice_id: '',
  invoice_number: '',
  line_items: [] as Invoice.LineItem[],
  sub_total: '0',
})

const getActualPaymentAmount = () => {
  if (invoiceDetails.surcharge_type === '1') {
    return new Decimal(initialValues.amount || 0).mul(new Decimal(1).add(new Decimal(invoiceDetails.surcharge_rate || 0).div(100))).toFixed(2)
  }
  else {
    return new Decimal(initialValues.amount || 0).add(new Decimal(invoiceDetails.surcharge_rate || 0)).toFixed(2)
  }
}

// reCAPTCHA handlers
const onRecaptchaVerify = (response: string) => {
  if (response) {
    initialValues.google_token = response
    recaptchaVerified.value = true
  }
}

const onRecaptchaExpired = () => {
  recaptchaVerified.value = false
}

const onRecaptchaError = () => {
  recaptchaVerified.value = false
}

// Submit form
const onSubmit = async (values: Record<string, any>) => {
  // Check recaptcha verification
  if (!recaptchaVerified.value) {
    initialValues.recaptcha = false
  }

  if (!values.google_token) {
    return
  }

  isLoading.value = true

  try {
    const sendData = {
      // amount: Number(initialValues.amount),
      // email: initialValues.email,
      name: initialValues.name,
      google_token: initialValues.google_token,
      invoice_number: initialValues.invoiceNumber,
      type: initialValues.paymentMethod,
      invoice_token: invoiceId.value,
    } as Api.XeroPayReq

    if (initialValues.paymentMethod === PaymentMethod.CreditCard) {
      sendData.card = {
        card_number: initialValues.creditCardNumber,
        expiration_month: initialValues.expiryDate.split('/')[0],
        expiration_year: initialValues.expiryDate.split('/')[1],
        security_code: initialValues.securityCode,
      }
    }
    else if (initialValues.paymentMethod === PaymentMethod.BankAccount) {
      sendData.bank = {
        bsb: initialValues.bsbNumber,
        account_name: initialValues.accountName,
        account_no: initialValues.accountNumber,
      }
    }

    const { data, code } = await integrationsApi.pay(sendData)

    if (code === 0) {
      // Payment successful
      paymentResult.success = true
      paymentResult.message = t('invoicePaymentPage.paymentSuccessMessage')
      paymentResult.amount = `$${invoiceDetails.amountDue}` || ''
      paymentResult.date = new Date().toLocaleString()
      paymentResult.show = true
    }
    else {
      // Payment failed with error code
      paymentResult.success = false
      paymentResult.message = data.message || t('invoicePaymentPage.paymentFailedMessage')
      paymentResult.show = true
      recaptchaRef.value?.reset()
    }
  }
  catch (error) {
    // Payment failed with exception
    paymentResult.success = false
    paymentResult.message = error instanceof Error ? error.message : t('invoicePaymentPage.paymentFailedMessage')
    paymentResult.show = true
    recaptchaRef.value?.reset()
  }
  finally {
    isLoading.value = false
  }
}

// Fetch invoice details
const fetchInvoice = async (query: Api.GetXeroInvoiceConfigReq = {}) => {
  if ((initialValues.invoiceNumber && query.invoice_number) || (invoiceId.value && query.id)) {
    // 显示加载状态
    isFetchingInvoice.value = true

    try {
      const { data, code, message } = await integrationsApi.getXeroInvoiceConfig(query)

      if (code === 0) {
        invoiceDetails.customer.name = data.customer.name
        invoiceDetails.customer_id = data.customer.customer_id
        invoiceDetails.invoice_number = data.invoice_number
        invoiceDetails.invoice_id = data.invoice_id
        invoiceDetails.created_at = data.created_at
        invoiceDetails.due_date = data.due_date
        invoiceDetails.line_items = data.line_items
        invoiceDetails.sub_total = data.sub_total
        invoiceDetails.total = data.sub_total
        invoiceDetails.amountPaid = data.amount_paid
        invoiceDetails.amountDue = data.amount_due
        invoiceDetails.surcharge_rate = String(data.surcharge_fees?.fee_value)
        invoiceDetails.surcharge_type = String(data.surcharge_fees?.fee_rate)
        maxAmount.value = Number(data.amount_due)
        invoiceDetails.currency = 'AUD'
        // showInvoiceDetails.value = true

        if (data.invoice_number) {
          initialValues.invoiceNumber = data.invoice_number
          formRef.value?.setFieldValue('invoiceNumber', data.invoice_number)
        }
      }
      else {
        invoiceDetails.total = '0'
        invoiceDetails.amountPaid = '0'
        invoiceDetails.amountDue = '0'
        invoiceDetails.surcharge_rate = '0'
        invoiceDetails.surcharge_type = '0'
        maxAmount.value = ************
        showInvoiceDetails.value = false

        paymentResult.success = false
        paymentResult.message = message || t('invoicePaymentPage.paymentFailedMessage')
        paymentResult.show = true
      }
    }
    finally {
      isFetchingInvoice.value = false
    }
  }
}

fetchInvoice({ id: invoiceId.value })

const reloadPage = () => {
  window.location.reload()
}
</script>

<template>
  <div class="payment-container">
    <!-- 主要内容区域 -->
    <div class="payment-content border-round overflow-hidden">
      <!-- 支付结果显示 -->
      <div v-if="paymentResult.show" class="payment-result p-4  dark:text-white">
        <div class="result-container border-round p-4 py-8" :class="paymentResult.success ? 'success' : 'error'">
          <div class="result-icon text-center mb-4">
            <i :class="paymentResult.success ? 'pi pi-check-circle' : 'pi pi-times-circle'" style="font-size: 4rem" />
          </div>
          <h2 class="text-center mb-3">
            {{ paymentResult.success ? 'Submit Success' : 'Submit Failed' }}
          </h2>
          <p class="text-center mb-4">
            {{ paymentResult.message }}
          </p>

          <!-- 成功时显示交易详情 -->
          <div v-if="paymentResult.success" class="transaction-details border-1 border-round p-3 mb-4">
            <div class=" flex flex-col gap-2">
              <div class="flex justify-center mb-2">
                <span>{{ t('invoicePaymentPage.amountPaid') }}</span>
                <span class="font-medium">{{ paymentResult.amount }}</span>
              </div>
              <div class="flex justify-center mb-2">
                <span>{{ t('invoicePaymentPage.paymentDate') }}</span>
                <span class="font-medium">{{ paymentResult.date }}</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-content-center">
            <Button
              type="button"
              :label="paymentResult.success ? t('invoicePaymentPage.done') : t('invoicePaymentPage.tryAgain')"
              :icon="paymentResult.success ? 'pi pi-check' : 'pi pi-refresh'"
              :class="paymentResult.success ? 'p-button-success' : 'p-button-primary'"
              @click="paymentResult.success ? reloadPage() : paymentResult.show = false"
            />
          </div>
        </div>
      </div>

      <!-- 表单区域 -->
      <div v-if="showForm" class="payment-form relative">
        <div class="flex justify-center mb-4">
          <img :src="logo" alt="logo" class="w-100">
        </div>
        <ProgressSpinner v-if="isLoading" class="loading-spinner" stroke-width="4" />

        <Card v-else-if="invoiceDetails.created_at" class="invoice-card mb-4">
          <template #content>
            <!-- Invoice header -->
            <div class="invoice-header">
              <div class="invoice-parties">
                <div class="recipient">
                  <h3>To</h3>
                  <p>{{ invoiceDetails.customer.name }}</p>
                </div>
              </div>

              <div class="invoice-meta">
                <div class="meta-item">
                  <h3>Invoice number</h3>
                  <p>{{ invoiceDetails.invoice_number }}</p>
                </div>
                <div class="meta-item">
                  <h3>Issue date</h3>
                  <p>{{ formatDate(invoiceDetails.created_at, 'DD MMM YYYY') }}</p>
                </div>
                <div class="meta-item">
                  <h3>Due date</h3>
                  <p>
                    {{ formatDate(invoiceDetails.due_date, 'DD MMM YYYY') }}
                  </p>
                </div>
              </div>
            </div>

            <Divider />

            <!-- Invoice items -->
            <div class="invoice-items">
              <table>
                <thead>
                  <tr>
                    <th>Description</th>
                    <th>Quantity</th>
                    <th>Unit Price</th>
                    <th>Amount</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in invoiceDetails.line_items" :key="index">
                    <td>{{ item.description }}</td>
                    <td>{{ item.quantity }}</td>
                    <td>{{ item.unit_amount }}</td>
                    <td>{{ item.line_amount }}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Invoice summary -->
            <div class="invoice-summary">
              <div class="summary-row">
                <span>Subtotal</span>
                <span>{{ invoiceDetails.sub_total }}</span>
              </div>

              <div class="amount-due flex justify-between items-center">
                <h3 class="!mb-0">
                  Amount due
                </h3>
                <h2 class="!mb-0">
                  {{ invoiceDetails.currency }} {{ invoiceDetails.sub_total }}
                </h2>
              </div>
            </div>
          </template>
        </Card>

        <VeeForm ref="formRef" :validation-schema="validationSchema" @submit="onSubmit">
          <div class="grid">
            <!-- 表单字段 -->
            <div class="col-1" :class="{ 'md:col-1': showInvoiceDetails }">
              <!-- Invoice Number -->
              <!-- <Field v-slot="{ errorMessage, handleChange }" name="invoiceNumber">
                <div class="field mb-4">
                  <label for="invoiceNumber" class="block mb-2">{{ t('invoicePaymentPage.invoiceNumber') }} <span
                    class="text-red-500"
                  >*</span> </label>
                  <div class="flex justify-content-between gap-4">
                    <InputText
                      id="invoiceNumber" v-model="initialValues.invoiceNumber" class="w-full"
                      placeholder="Please enter invoice number" @value-change="handleChange"
                    />
                    <Button
                      type="button" :label="t('invoicePaymentPage.fetchInvoice')" class="fetch-button"
                      :loading="isFetchingInvoice" severity="warn"
                      @click="fetchInvoice({ invoice_number: initialValues.invoiceNumber })"
                    />
                  </div>
                  <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field> -->

              <!-- Payment Method -->
              <Field v-slot="{ field, handleChange }" v-model="initialValues.paymentMethod" name="paymentMethod">
                <div class="field mb-4 border-y-1 border-y-gray-200 py-4">
                  <div class="flex justify-content-between gap-2">
                    <div class="flex items-center gap-2">
                      <RadioButton
                        v-model="field.value" input-id="creditCard" name="paymentMethod" :value="PaymentMethod.CreditCard"
                        @value-change="handleChange"
                      />
                      <label for="creditCard">Card</label>
                    </div>
                    <!-- <div class="flex items-center gap-2">
                      <RadioButton
                        v-model="field.value" :disabled="true" input-id="debitCard" name="paymentMethod"
                        :value="PaymentMethod.DebitCard" @value-change="handleChange"
                      />
                      <label for="debitCard">Debit Card</label>
                    </div> -->
                    <div class="flex items-center gap-2">
                      <RadioButton
                        v-model="field.value" input-id="bankAccount" name="paymentMethod"
                        :value="PaymentMethod.BankAccount"
                        @value-change="handleChange"
                      />
                      <label for="bankAccount">Bank Account</label>
                    </div>
                  </div>
                </div>
              </Field>

              <div class="payment-forms">
                <div class="payment-form">
                  <!-- Amount of Payment -->
                  <!-- <Field v-slot="{ field, errorMessage, handleChange }" v-model="initialValues.amount" name="amount">
                    <div class="field mb-4">
                      <label for="amount" class="block mb-2">{{ t('invoicePaymentPage.amountOfPayment') }} ({{
                        invoiceDetails.currency }})</label>
                      <InputNumber
                        id="amount" v-model="field.value" class="w-full"
                        :placeholder="t('invoicePaymentPage.atLeastTwoDecimalPlaces')" :min-fraction-digits="2"
                        :max-fraction-digits="2" :max="************"
                        :disabled="Number(invoiceDetails.total) === 0 || Number(invoiceDetails.amountDue) === 0"
                        @value-change="handleChange"
                      />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </div>
                  </Field> -->

                  <!-- Email -->
                  <!-- <Field v-slot="{ field, errorMessage }" v-model="initialValues.email" name="email">
                    <div class="field mb-4">
                      <label for="email" class="block mb-2">{{ t('invoicePaymentPage.email') }}</label>
                      <InputText
                        id="email" v-bind="field" :disabled="Number(invoiceDetails.total) === 0" class="w-full"
                        placeholder="<EMAIL>"
                      />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </div>
                  </Field> -->

                  <!-- Name -->
                  <Field v-slot="{ field, errorMessage }" v-model="initialValues.name" name="name">
                    <div class="field mb-4">
                      <label for="name" class="block mb-2">{{ t('invoicePaymentPage.name') }}</label>
                      <InputText
                        id="name" v-bind="field" :disabled="Number(invoiceDetails.total) === 0" class="w-full"
                        placeholder="Full Name"
                      />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </div>
                  </Field>

                  <template v-if="initialValues.paymentMethod === PaymentMethod.CreditCard">
                    <!-- Credit Card Number -->
                    <Field
                      v-slot="{ field, errorMessage }" v-model="initialValues.creditCardNumber" as="div"
                      class="field mb-4" name="creditCardNumber"
                    >
                      <label for="creditCardNumber" class="block mb-2">{{ t('invoicePaymentPage.creditCardNumber') }}
                        <span class="text-red-500">*</span> </label>
                      <InputText
                        id="creditCardNumber" v-bind="field" :disabled="Number(invoiceDetails.total) === 0"
                        class="w-full" placeholder="XXXX XXXX XXXX XXXX"
                      />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </Field>
                    <div class="flex flex-start mb-4">
                      <Image :src="creditCardNumberImage" alt="creditCardNumber" style="width: 110px" />
                    </div>
                    <!-- Expiry Date and securityCode in a row -->
                    <Field
                      v-slot="{ field, errorMessage }" v-model="initialValues.expiryDate" as="div"
                      class="field mb-4" name="expiryDate"
                    >
                      <div class="field mb-4">
                        <label for="expiryDate" class="block mb-2">{{ t('invoicePaymentPage.expireDate') }} <span
                          class="text-red-500"
                        >*</span> </label>
                        <InputMask
                          :model-value="field.value" :disabled="Number(invoiceDetails.total) === 0"
                          placeholder="MM/YY" mask="99/99" class="w-full"
                        />
                        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                          {{ errorMessage }}
                        </Message>
                      </div>
                    </Field>

                    <Field
                      v-slot="{ field, errorMessage }" v-model="initialValues.securityCode" as="div"
                      class="field mb-4" name="securityCode"
                    >
                      <label for="securityCode" class="block mb-2">{{ t('invoicePaymentPage.securityCode') }} <span
                        class="text-red-500"
                      >*</span> </label>
                      <InputText
                        id="securityCode" v-bind="field" :disabled="Number(invoiceDetails.total) === 0"
                        class="w-full" placeholder="123"
                      />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </Field>
                  </template>

                  <template v-if="initialValues.paymentMethod === PaymentMethod.BankAccount">
                    <Field
                      v-slot="{ field, errorMessage }" v-model="initialValues.bsbNumber" as="div"
                      class="field mb-4" name="bsbNumber"
                    >
                      <label for="bsbNumber" class="block mb-2">BSB Number <span class="text-red-500">*</span> </label>
                      <InputText
                        id="bsbNumber" v-bind="field" :disabled="Number(invoiceDetails.total) === 0"
                        class="w-full" placeholder="123" maxlength="6"
                      />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </Field>
                    <Field
                      v-slot="{ field, errorMessage }" v-model="initialValues.accountName" as="div"
                      class="field mb-4" name="accountName"
                    >
                      <label for="accountName" class="block mb-2">Account Name <span class="text-red-500">*</span>
                      </label>
                      <InputText
                        id="accountName" v-bind="field" :disabled="Number(invoiceDetails.total) === 0"
                        class="w-full" placeholder="Account Name"
                      />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </Field>
                    <Field
                      v-slot="{ field, errorMessage }" v-model="initialValues.accountNumber" as="div"
                      class="field mb-4" name="accountNumber"
                    >
                      <label for="accountNumber" class="block mb-2">Account Number <span class="text-red-500">*</span>
                      </label>
                      <InputText
                        id="accountNumber" v-bind="field" :disabled="Number(invoiceDetails.total) === 0"
                        class="w-full" placeholder="Account Number"
                      />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </Field>
                  </template>

                  <!-- Google reCAPTCHA -->
                  <Field v-slot="{ errorMessage }" v-model="initialValues.google_token" name="google_token">
                    <div class="field mb-4">
                      <label class="block mb-2">{{ t('invoicePaymentPage.verification') || 'Verification' }}</label>
                      <GoogleRecaptcha
                        ref="recaptchaRef" class="mb-2"
                        @verify="onRecaptchaVerify" @expired="onRecaptchaExpired"
                        @error="onRecaptchaError"
                      />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </div>
                  </Field>
                </div>
                <div v-if="showInvoiceDetails" class="payment-invoice">
                  <!-- 发票详情 -->
                  <div class="col-1 md:col-4 pl-md-4">
                    <div class="invoice-details border-1 border-round h-full">
                      <h3 class="invoice-summary-title">
                        Amount
                      </h3>
                      <div class="flex justify-content-between mb-2 px-4 mt-2">
                        <span>Invoice Total: </span>
                        <span class="font-medium font-bold">{{ invoiceDetails.total }}</span>
                      </div>
                      <div class="flex justify-content-between mb-2 px-4">
                        <span>Invoice amount paid: </span>
                        <span class="font-medium font-bold">{{ invoiceDetails.amountPaid }}</span>
                      </div>
                      <div class="flex justify-content-between mb-2 px-4">
                        <span>Invoice amount <strong>DUE</strong>: </span>
                        <span class="font-medium font-bold">{{ invoiceDetails.amountDue }}</span>
                      </div>
                      <!-- 附加费 -->
                      <div class="flex justify-content-between mb-2 px-4">
                        <span class="mr-1">Surcharge: </span>
                        <span v-if="invoiceDetails.surcharge_type === '1'" class="font-medium font-bold">
                          {{ invoiceDetails.surcharge_rate }}%
                        </span>
                        <span v-else class="font-medium font-bold">
                          {{ invoiceDetails.surcharge_rate }}
                        </span>
                      </div>
                      <!-- 实际支付金额 -->
                      <div class="flex justify-content-between mb-2 px-4">
                        <span>Actual Payment Amount: </span>
                        <span class="font-medium ml-2 font-bold">
                          {{ getActualPaymentAmount() }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Submit Button -->
              <div class="field">
                <Button
                  type="submit" label="SUBMIT" class="w-full h-12 p-button-primary submit-button"
                  severity="warn"
                />
              </div>
            </div>
          </div>
        </VeeForm>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.payment-container {
  background-color: var(--bg-colors-white);

  .payment-content {
    max-width: 800px;
    margin: 0 auto;
    min-height: 100vh;
    padding: 0 1.5rem;
    padding-bottom: 24px;
    background-color: var(--color-white);
  }
}

.payment-forms {
  display: flex;
  justify-content: space-between;
  gap: 1rem;

  .payment-form {
    width: 70%;
    flex: 1;
  }

  .payment-invoice {
    width: 30%;
    border-radius: 8px;

    .invoice-details {
      color: var(--color-gray-600);
      border: 1px solid var(--color-gray-500);
      border-top: 0;
      border-radius: 12px;

      .invoice-summary-title {
        border: 1px solid var(--color-gray-500);
        border-radius: 12px;
        font-size: 2rem;
        padding: 1rem;
        color: var(--colors-primary);
        margin: 0 -1px;
        background-color: rgb(7, 222, 255);
      }
    }

  }
}

.rules-section {
  font-size: 0.9rem;
  border-color: #e9ecef;
}

.rules-section ol {
  padding-left: 1.5rem;
  margin-bottom: 0;
}

.fetch-button {
  min-width: 150px;
}

.loading-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
}

:deep(.p-progress-spinner) {
  width: 50px;
  height: 50px;
}

/* Payment Result Styles */
.payment-result {
  max-width: 600px;
  margin: 0 auto;
}

.result-container {
  background-color: #fff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  &.success {
    border-left: 5px solid #22c55e;

    .result-icon {
      color: #22c55e;
    }
  }

  &.error {
    border-left: 5px solid #ef4444;

    .result-icon {
      color: #ef4444;
    }
  }

  @media (prefers-color-scheme: dark) {
    background-color: var(--color-gray-900);
    border-color: var(--color-gray-800) !important;
  }
}

.field {
  label {
    font-weight: 600;
    color: var(--color-gray-500);
  }
}

.transaction-details {
  background-color: #f8f9fa;
  border-color: #e0e0e0 !important;

  @media (prefers-color-scheme: dark) {
    background-color: var(--color-gray-900);
    border-color: var(--color-gray-800) !important;
  }
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .payment-container {
    .payment-content {
      padding: 0 1.5rem;
    }
  }

  .pl-md-4 {
    padding-left: 0;
    margin-top: 0;
  }

  .payment-forms {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;

    .payment-form {
      width: 100%;
      flex: 1;
      order: 1;
    }

    .payment-invoice {
      flex: 1;
      width: 100%;
      border-radius: 8px;
    }
  }

  .invoice-header {
    flex-direction: column;
    gap: 1rem;

    h1 {
      margin: 0;
    }
  }
}

.invoice-header {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.invoice-parties,
.invoice-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
}

.meta-item,
.recipient,
.sender {
  h3 {
    font-size: 0.9rem;
    color: var(--color-gray-500);
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  p {
    font-size: 1rem;
    margin: 0;
  }
}

.invoice-items {
  margin: 1.5rem 0;
  overflow-x: auto;

  table {
    width: 100%;
    border-collapse: collapse;

    th {
      text-align: left;
      padding: 0.75rem;
      font-weight: 600;
      border-bottom: 1px solid var(--color-gray-200);
      color: var(--color-gray-500);
      font-size: 0.9rem;
    }

    td {
      padding: 0.75rem;
      border-bottom: 1px solid var(--color-gray-200);
    }
  }
}

.invoice-summary {
  margin-top: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: flex-end;

  .summary-row {
    display: flex;
    justify-content: space-between;
    width: 250px;
    padding: 0.5rem 0;
  }

  .amount-due {
    width: 250px;
    margin-top: 1rem;
    border-top: 2px solid var(--color-gray-200);
    padding-top: 1rem;

    h3 {
      font-size: 1rem;
      color: #4b5563;
      margin-bottom: 0.5rem;

      @media (prefers-color-scheme: dark) {
        color: var(--color-gray-400)
      }
    }

    h2 {
      font-size: 1.5rem;
      font-weight: 700;
      margin: 0;
      color: var(--color-dark);

      @media (prefers-color-scheme: dark) {
        color: var(--color-white);
      }
    }
  }
}

/* Small mobile devices */
@media screen and (max-width: 480px) {
  :deep(.p-float-label) {
    font-size: 0.9rem;
  }

  :deep(.p-inputtext) {
    font-size: 0.9rem;
    padding: 0.5rem;
  }
}

// Dark mode
@media (prefers-color-scheme: dark) {
  .payment-container {
    background-color: var(--p-surface-950);

    .payment-content {
      background-color: var(--p-surface-800);
    }
  }
}
</style>
