<script setup lang="ts">
import { ref } from 'vue'
import { integrations } from '@/services/api'

enum Step {
  InvoiceTemplates = 1,
  BankAccount = 2,
}

// Dummy data, replace with API data if needed
const invoiceTemplates = ref<Api.XeroSettingsBrandingThemeRes[]>([])

const bankAccounts = ref<Api.XeroSettingsBankAccountRes[]>([])

const selectedBank = ref<number | null>(null)

const logoUrl = ref<FileUpload.UploadFileItem[] | null>(null)

const submitLoading = ref(false)

const step = ref<Step>(Step.InvoiceTemplates)

function nextStep() {
  if (step.value === Step.InvoiceTemplates) { step.value = Step.BankAccount }
}

function prevStep() {
  if (step.value === Step.BankAccount) { step.value = Step.InvoiceTemplates }
}

const fetchXeroSettings = async () => {
  const { data } = await integrations.getXeroAccountSettings()

  invoiceTemplates.value = data.branding_themes || []
  bankAccounts.value = data.bank_accounts || []
  selectedBank.value = data.bank_account_id || null
  logoUrl.value = data.logo ? [{ url: data.logo }] : null
}

async function save() {
  if (step.value === Step.InvoiceTemplates) {
    try {
      submitLoading.value = true
      const { code } = await integrations.updateXeroSettings({ update_list: invoiceTemplates.value })
      if (code === 0) {
        window.$toast.add({
          severity: 'success',
          summary: 'Saved successfully',
          detail: 'Your configuration has been saved.',
        })
        fetchXeroSettings()
      }
    }
    finally {
      submitLoading.value = false
    }
  }
  else if (step.value === Step.BankAccount) {
    submitLoading.value = true

    const logo_url = logoUrl.value?.[0]?.url || ''

    // file_name 后端要求
    const { code } = await integrations.updateXeroBankAccount({ bank_account_id: selectedBank.value as number, logo: logo_url, file_name: 'xero' })

    if (code === 0) {
      window.$toast.add({
        severity: 'success',
        summary: 'Saved successfully',
        detail: 'Your configuration has been saved.',
      })
      fetchXeroSettings()
    }

    submitLoading.value = false
  }
}

fetchXeroSettings()
</script>

<template>
  <div class="xero-config">
    <!-- Progress Steps -->
    <div class="steps-container">
      <div
        class="step-item"
        :class="{ active: step === Step.InvoiceTemplates, completed: step > Step.InvoiceTemplates }"
      >
        <div class="step-circle">
          <i v-if="step > Step.InvoiceTemplates" class="pi pi-check" />
          <span v-else>1</span>
        </div>
        <div class="step-content">
          <h3>Configure BillBuddy Features</h3>
          <p>Set up invoice templates and payment options</p>
        </div>
      </div>
      <div class="step-divider" />
      <div class="step-item" :class="{ active: step === Step.BankAccount }">
        <div class="step-circle">
          <span>2</span>
        </div>
        <div class="step-content">
          <h3>Bank Account & Branding</h3>
          <p>Configure reconciliation and upload your logo</p>
        </div>
      </div>
    </div>

    <!-- Content Cards -->
    <div class="content-area">
      <!-- Step 1: Invoice Templates -->
      <Card v-if="step === Step.InvoiceTemplates" class="step-card">
        <template #header>
          <div class="card-header">
            <div class="header-icon">
              <i class="pi pi-file-edit" />
            </div>
            <div>
              <h2>Apply BillBuddy to your invoice templates</h2>
              <p>
                Select the invoice templates you would like BillBuddy to accept payments from. BillBuddy can be used for
                one-off invoice payments as well as variable recurring payments.
              </p>
            </div>
          </div>
        </template>
        <template #content>
          <DataTable :value="invoiceTemplates" class="templates-table" :paginator="false" striped-rows>
            <Column field="name" header="Invoice Template" class="template-name-col">
              <template #body="{ data }">
                <div class="template-name">
                  {{ data.name }}
                </div>
              </template>
            </Column>
            <Column header="Accept payments from BillBuddy?" class="checkbox-col">
              <template #body="{ data }">
                <div class="checkbox-wrapper">
                  <Checkbox v-model="data.accept_pinch_pay" :true-value="1" :false-value="0" binary />
                </div>
              </template>
            </Column>
            <Column header="Allow customer opt-in for auto debit?" class="checkbox-col">
              <template #body="{ data }">
                <div class="checkbox-wrapper">
                  <Checkbox v-model="data.allow_auto_debit" :true-value="1" :false-value="0" binary />
                </div>
              </template>
            </Column>
            <Column header="Monitor for auto debit?" class="checkbox-col">
              <template #body="{ data }">
                <div class="checkbox-wrapper">
                  <Checkbox v-model="data.monitor_debit" :true-value="1" :false-value="0" binary />
                </div>
              </template>
            </Column>
            <Column header="Send invoice notifications?" class="checkbox-col">
              <template #body="{ data }">
                <div class="checkbox-wrapper">
                  <Checkbox v-model="data.send_invoice_notify" :true-value="1" :false-value="0" binary />
                </div>
              </template>
            </Column>
          </DataTable>
        </template>
      </Card>

      <!-- Step 2: Bank Account & Logo -->
      <Card v-if="step === Step.BankAccount" class="step-card">
        <template #header>
          <div class="card-header">
            <div class="header-icon">
              <i class="pi pi-building" />
            </div>
            <div>
              <h2>Xero Bank Account and Logo</h2>
              <p>Configure which bank account to reconcile with and upload your company logo for payment pages.</p>
            </div>
          </div>
        </template>
        <template #content>
          <div class="form-grid">
            <div class="form-section">
              <h3 class="section-title">
                <i class="pi pi-university mr-2" />
                Bank Account Selection
              </h3>
              <div class="field">
                <label class="field-label">Xero Bank Account</label>
                <Select
                  v-model="selectedBank" :options="bankAccounts" option-label="name" option-value="id"
                  placeholder="Select a bank account" class="w-full"
                />
                <small class="field-hint">Now we just need to know which bank account in Xero we should update when you
                  receive payments.</small>
              </div>
            </div>

            <div class="form-section">
              <h3 class="section-title">
                <i class="pi pi-image mr-2" />
                Company Logo
              </h3>
              <div class="logo-upload-area">
                <div class="current-logo">
                  <div class="logo-preview">
                    <BaseFileUpload
                      v-model="(logoUrl as FileUpload.UploadFileItem[])"
                      :multiple="false"
                      :auto-upload="true"
                      accept="image/jpeg,image/png,.jpg,.jpeg,.png"
                      :max-size="1024 * 1024 * 10"
                      :max-files="1"
                      class="avatar-uploader"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </Card>
    </div>

    <!-- Action Buttons -->
    <div class="actions-bar">
      <div class="actions-right">
        <Button
          v-if="step > Step.InvoiceTemplates" label="Previous" icon="pi pi-arrow-left" class="p-button-outlined p-button-secondary"
          @click="prevStep"
        />
        <Button
          v-if="step < Step.BankAccount" label="Next" icon="pi pi-arrow-right" icon-pos="right"
          class="p-button-primary" @click="nextStep"
        />
        <Button
          label="Save Configuration" icon="pi pi-check" class="p-button-success" :loading="submitLoading"
          @click="save"
        />
      </div>
    </div>

    <Toast />
  </div>
</template>

<style scoped lang="scss">
.xero-config {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.config-header {
  text-align: center;
  margin-bottom: 3rem;

  .config-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .config-subtitle {
    font-size: 1.1rem;
    color: #7f8c8d;
    margin: 0;
  }
}

.steps-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 3rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}

.step-item {
  display: flex;
  align-items: center;
  gap: 1rem;

  &.active .step-circle {
    background: #3b82f6;
    color: white;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
  }

  &.completed .step-circle {
    background: var(--color-orange-500);
    color: white;
  }
}

.step-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #e5e7eb;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.step-content {
  h3 {
    margin: 0 0 0.25rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
  }

  p {
    margin: 0;
    font-size: 0.875rem;
    color: #6b7280;
  }
}

.step-divider {
  width: 100px;
  height: 2px;
  background: #e5e7eb;
  margin: 0 2rem;
}

.content-area {
  margin-bottom: 2rem;
}

.step-card {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border: none;
  border-radius: 16px;
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  .header-icon {
    font-size: 2rem;
    opacity: 0.9;
  }

  h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
  }

  p {
    margin: 0;
    opacity: 0.9;
    line-height: 1.5;
  }
}

.templates-table {
  border: none;

  :deep(.p-datatable-header) {
    background: #f8fafc;
    border: none;
    padding: 1rem;
  }

  :deep(.p-datatable-thead > tr > th) {
    background: #f1f5f9;
    color: #475569;
    font-weight: 600;
    border: none;
    padding: 1rem;
  }

  :deep(.p-datatable-tbody > tr > td) {
    padding: 1rem;
    border: none;
    border-bottom: 1px solid #e2e8f0;
  }

  :deep(.p-datatable-tbody > tr:hover) {
    background: #f8fafc;
  }
}

.template-name {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #1e293b;
}

.checkbox-wrapper {
  display: flex;
  justify-content: center;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  padding: 2rem;
}

.form-section {
  .section-title {
    display: flex;
    align-items: center;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e2e8f0;
  }
}

.field {
  margin-bottom: 1.5rem;

  .field-label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  .field-hint {
    display: block;
    color: #6b7280;
    margin-top: 0.5rem;
    line-height: 1.4;
  }
}

.logo-upload-area {
  .current-logo {
    margin-bottom: 1.5rem;
  }

  .logo-preview {
    margin-bottom: 1rem;
  }

  .logo-image {
    max-width: 200px;
    max-height: 120px;
    object-fit: contain;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    background: white;
  }

  .logo-placeholder {
    width: 200px;
    height: 120px;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
    background: #f9fafb;

    i {
      font-size: 2rem;
      margin-bottom: 0.5rem;
    }
  }
}

.actions-bar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);

  .actions-right {
    display: flex;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .xero-config {
    padding: 1rem;
  }

  .steps-container {
    flex-direction: column;
    gap: 1rem;

    .step-divider {
      width: 2px;
      height: 50px;
      margin: 1rem 0;
    }
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .actions-bar {
    flex-direction: column;
    gap: 1rem;

    .actions-left,
    .actions-right {
      width: 100%;
    }

    .actions-right {
      justify-content: center;
    }
  }
}
</style>
