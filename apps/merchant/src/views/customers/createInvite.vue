<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { usePermissions } from '@/composables/usePermissions'
import { Permissions } from '@/constants/permissions'
import CreateInviteConfirm from './components/createInviteConfirm.vue'
import CreateInviteSubscriptionForm from './components/createInviteSubscriptionForm.vue'
import { useCustomerForm } from './composables/useCustomerForm'

const { hasPermission } = usePermissions()

const route = useRoute()

const customerId = ref<string>(route.query.customer_id as string || '')

const {
  state,
  step,
  handleSubmit,
  submitting,
  setCustomerId,
  sendEmailInvite,
} = useCustomerForm(customerId.value ? 'invite' : 'create')

const isShowSendMail = ref(true)

if (customerId.value) {
  setCustomerId(customerId.value as string)
}

const subscriptionFormRef = ref<InstanceType<typeof CreateInviteSubscriptionForm>>()

const validateSubscription = async () => {
  if (!subscriptionFormRef.value) {
    return
  }
  const isValid = await subscriptionFormRef.value.validate()
  if (isValid) {
    const data = await subscriptionFormRef.value?.getFormData() as Api.PlanCreateReq
    if (Object.keys(data).length === 0) {
      isShowSendMail.value = false
    }
    handleSubmit(data)
  }
}

const sendEmail = async () => {
  if (!hasPermission(Permissions.CUSTOMER_SEND_INVITE_MAIL)) {
    return
  }
  await sendEmailInvite()
}
</script>

<template>
  <div class="invite-customer flex justify-between">
    <Stepper v-model:value="step" class="w-full" linear>
      <StepList class="!mb-6">
        <Step value="1">
          Create a subscription
        </Step>
        <Step value="2">
          Confirm the form
        </Step>
        <Step value="3">
          Sent
        </Step>
      </StepList>
      <StepPanels class="!p-0 !rounded-lg">
        <StepPanel class="!rounded-lg" value="1">
          <div class="py-0 px-2">
            <CreateInviteSubscriptionForm
              ref="subscriptionFormRef"
              :form-data-state="state"
              :surcharge-rate="state.surcharge_rate"
              :gst="state.gst"
              :customer-id="customerId"
              :is-submit-loading="submitting"
              @next="validateSubscription"
            />
          </div>
        </StepPanel>
        <StepPanel class="!p-0 !rounded-lg bg-white" value="2">
          <div class=" py-8 px-6">
            <CreateInviteConfirm v-model:form-data="state" />
            <div class="invite-confirm-button flex pt-6 justify-end gap-2">
              <Button
                label="Back" :loading="submitting" icon="pi pi-arrow-left" class="p-button-outlined"
                @click="$router.back()"
              />
              <Button
                v-if="isShowSendMail && hasPermission(Permissions.CUSTOMER_SEND_INVITE_MAIL)"
                label="Send Email"
                severity="warn"
                :loading="submitting"
                icon="pi pi-arrow-right"
                @click="sendEmail"
              />
            </div>
          </div>
        </StepPanel>
        <StepPanel value="3">
          <div class="py-8 px-6 flex flex-col items-center justify-center">
            <div class="success-icon mb-6">
              <i class="pi pi-check-circle text-green-500" style="font-size: 5rem;" />
            </div>
            <h2 class="text-2xl font-bold text-gray-800 mb-4 text-center">
              Invitation Sent Successfully!
            </h2>
            <p class="text-gray-600 mb-8 text-center max-w-lg">
              Your customer will receive the invitation email shortly. Once they confirm authorization, you will be
              notified and can begin processing payments.
            </p>
            <div class="success-details bg-gray-50 rounded-lg p-6 mb-8 w-full max-w-md">
              <div class="flex justify-between py-2 border-b border-gray-200">
                <span class="text-gray-600">Email Status:</span>
                <span class="text-green-600 font-medium">Sent</span>
              </div>
              <div class="flex justify-between py-2 border-b border-gray-200">
                <span class="text-gray-600">Sent Time:</span>
                <span class="text-gray-800">{{ new Date().toLocaleString() }}</span>
              </div>
              <div class="flex justify-between py-2">
                <span class="text-gray-600">Recipient:</span>
                <span class="text-gray-800">{{ state.email_primary }}</span>
              </div>
            </div>

            <div class="flex flex-wrap gap-4 justify-center">
              <Button label="View Customer List" @click="$router.push('/customers')" />
            </div>
          </div>
        </StepPanel>
      </StepPanels>
    </Stepper>
  </div>
</template>

<style scoped lang="scss">
:deep(.p-steppanel) {
  background-color: transparent;
}

.preview {
  .browser-bar {
    background-color: #333;
    color: white;
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
  }
}

/* 添加表单样式 */
.field {
  margin-bottom: 1.5rem;

  label {
    font-weight: 500;
    color: #374151;
    display: block;
    margin-bottom: 0.5rem;
  }
}

.w-full {
  width: 100%;
}

/* 响应式样式 */
@media screen and (max-width: 768px) {
  .invite-customer {
    flex-direction: column;
  }
}

.invite-confirm-button {
  :deep(.p-button) {
    width: 210px;
    height: 52px;
    border-radius: 8px;
    :deep(.p-button-label) {
      font-weight: 600;
      font-size: 16px;
    }
  }
}
</style>
