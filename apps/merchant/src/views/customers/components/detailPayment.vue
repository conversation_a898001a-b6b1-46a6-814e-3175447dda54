<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRequestList } from '@/composables/useRequestList'
import { transactions as transactionsApi } from '@/services/api'
import { useDictStore } from '@/store/modules/dict'
import { formatDate } from '@/utils/date'
import { getTransactionTagStatus } from '@/utils/tagStatus'

const props = withDefaults(defineProps<{ id: string }>(), {
  id: '',
})

const { t } = useI18n()

const { getDictByType, getDictLabel } = useDictStore()

// 使用 useRequestList 处理交易列表数据
const requestList = useRequestList<Transaction.Info[], Api.TransactionListReq>({
  requestFn: transactionsApi.getList,
  immediate: false,
  page_size: 20,
})

const columns = ref<TableColumnItem[]>([
  {
    field: 'date',
    header: 'Date',
    style: { minWidth: '150px' },
    template: 'date',
    sortField: 'date',
  },
  {
    field: 'Name of Subscription',
    header: 'Name of Subscription',
    style: { minWidth: '150px' },
    template: 'name',
    sortField: 'name',
  },
  {
    field: 'amount',
    header: 'Amount',
    style: { minWidth: '110px' },
    template: 'amount',
  },
  { field: 'status', header: t('customersPage.columns.status'), template: 'status' },
  // { field: 'details', header: 'Details', template: 'details' },
  {
    field: 'action',
    header: '',
    style: { width: '50px' },
    template: 'action',
  },
])

const {
  list,
  loading,
  total,
  setParams,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
  search,
} = requestList

// 格式化金额
const formatAmount = (amount: number) => {
  return new Intl.NumberFormat('en-AU', {
    style: 'currency',
    currency: 'AUD',
  }).format(amount)
}

setParams({
  customer_id: props.id,
})

search()

getDictByType('trans_status')

defineExpose({
  search,
})
</script>

<template>
  <BaseDataTable
    :value="list" :columns="columns" :scrollable="true" :show-multiple-column="false"
    :loading="loading" :paginator="true" :rows="20" :total-records="total" :lazy="true" data-key="id"
    sort-mode="single" :sort-field="$route.query.sort_by as string"
    :sort-order="$route.query.sort_order === 'desc' ? -1 : 1" search-placeholder="Search"
    type-placeholder="Filter By" :failed="failed" :failure-message="failureMessage"
    :show-search-bar="false"
    @page="handlePageChange"
  >
    <template #amount="{ data }">
      {{ formatAmount(data?.payment_amount) }} {{ data?.payment_currency }}
    </template>
    <template #name="{ data }">
      {{ data?.customer_plan?.plan_name }}
    </template>
    <template #date="{ data }">
      {{ formatDate(data.created_at) }}
    </template>
    <template #status="{ data }">
      <BaseTag :text="getDictLabel('trans_status', data.status)" :type="getTransactionTagStatus(data.status)" />
    </template>
  </BaseDataTable>
</template>
