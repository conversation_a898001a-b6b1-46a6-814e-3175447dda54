<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import { useDict } from '@/composables/useDict'
import { useRequestList } from '@/composables/useRequestList'
import { customer as customerApi } from '@/services/api'
import { formatDate } from '@/utils/date'
import { ref } from 'vue'

const props = defineProps<{
  customerId: string
}>()

// 使用 useRequestList 处理商户列表数据
const requestList = useRequestList<Customer.History, Api.CustomerHistoryListReq>({
  requestFn: customerApi.getCustomerHistory,
  immediate: true,
  defaultParams: {
    customer_id: props.customerId,
  },
})

const columns = ref<TableColumnItem[]>([
  {
    field: 'created_at',
    header: 'Date/Time',
    style: { width: '120px' },
    template: 'created_at',

  },
  {
    field: 'type',
    header: 'Type',
    style: { width: '80px' },
    template: 'type',
  },
  {
    field: 'detail',
    header: 'Detail',
    style: { width: '100px' },
  },
])

const {
  list,
  loading,
  total,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
} = requestList

const { getLabel: getCommunicationType } = useDict('communication_type')
</script>

<template>
  <BaseDataTable
    :columns="columns"
    :value="list"
    :loading="loading"
    :total-records="total"
    :paginator="true"
    :rows="50"
    :lazy="true"
    data-key="id"
    :show-search-bar="false"
    :show-multiple-column="false"
    :scrollable="true"
    :failed="failed"
    :failure-message="failureMessage"
    table-style="min-width: 15rem"
    @page="(e: DataTablePageEvent) => handlePageChange(e)"
  >
    <template #created_at="{ data }">
      {{ formatDate(data?.created_at) }}
    </template>

    <template #type="{ data }">
      {{ getCommunicationType(data?.type) }}
    </template>
  </BaseDataTable>
</template>

<style scoped>

</style>
