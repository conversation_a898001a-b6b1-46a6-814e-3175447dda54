<script setup lang="ts">
import type { PropType } from 'vue'
import type { PlanSubmitData } from '../../planSubscription/composables/usePlanForm'
import dayjs from 'dayjs'
import Decimal from 'decimal.js'
import InputNumber from 'primevue/inputnumber'
import { computed, ref, watch } from 'vue'
import { INFINITE } from '@/constants/customer'
import { BillingPeriodCustomType, BillingPeriodType, PlanEndDateType, RecurringPricingModel, RecurringTieredPaymentMethod, ScheduleType, UnitBasedPricingModel } from '@/constants/plan'
import { SurchargeRate } from '@/constants/user'

defineOptions({
  name: 'CustomerSubscriptionFormPreview',
})

const props = defineProps({
  formData: {
    type: Object as PropType<PlanSubmitData>,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  planName: {
    type: String,
    required: false,
    default: '',
  },
  startDate: {
    type: Date,
    required: true,
  },
  unitQuantity: {
    type: Number,
    required: true,
  },
  surchargeRate: {
    type: Object,
    default() {
      return {
        /**
         * 类型，1-百分比 2-固定值
         */
        fee_rate: '',
        /**
         * 费率
         */
        fee_value: '',
      }
    },
  },
  gst: {
    type: Object,
    default() {
      return {
        fee_rate: '',
        fee_value: '',
      }
    },
  },
  isShowForecast: {
    type: Boolean,
    default: false,
  },
})

// 预测月数
const forecastMonths = ref(1)

const standardPeriodMap: Record<BillingPeriodType, { unit: dayjs.ManipulateType, multiplier: number }> = {
  [BillingPeriodType.Daily]: { unit: 'day', multiplier: 1 },
  [BillingPeriodType.Weekly]: { unit: 'week', multiplier: 1 },
  [BillingPeriodType.Fortnightly]: { unit: 'week', multiplier: 2 },
  [BillingPeriodType.Monthly]: { unit: 'month', multiplier: 1 },
  [BillingPeriodType.Yearly]: { unit: 'year', multiplier: 1 },
  [BillingPeriodType.Every3Months]: { unit: 'month', multiplier: 3 },
  [BillingPeriodType.Every6Months]: { unit: 'month', multiplier: 6 },
  [BillingPeriodType.Custom]: { unit: 'day', multiplier: 0 },
}

// 单位的映射
const customPeriodMap: Record<BillingPeriodCustomType, dayjs.ManipulateType> = {
  [BillingPeriodCustomType.Day]: 'day',
  [BillingPeriodCustomType.Week]: 'week',
  [BillingPeriodCustomType.Month]: 'month',
  [BillingPeriodCustomType.Year]: 'year',
}

const periodMap: Record<string, string> = {
  [BillingPeriodType.Daily]: 'Daily',
  [BillingPeriodType.Weekly]: 'Weekly',
  [BillingPeriodType.Monthly]: 'Monthly',
  [BillingPeriodType.Yearly]: 'Yearly',
  [BillingPeriodType.Every3Months]: 'Every 3 months',
  [BillingPeriodType.Every6Months]: 'Every 6 months',
  [BillingPeriodType.Custom]: 'Custom',
}

// 格式化金额
const formatAmount = (amount: number | string | Decimal | null, _: string | null) => {
  if (!amount || amount === '') { return '$0.00' }
  const decimalAmount = amount instanceof Decimal ? amount.toFixed(2) : new Decimal(amount).toFixed(2)
  return `$${decimalAmount}`
}

// 获取计费周期文本
const getBillingPeriodText = (period: BillingPeriodType | null) => {
  if (!period) { return '' }

  if (period === BillingPeriodType.Custom) {
    const customType = props.formData.schedule_type === ScheduleType.Recurring
      ? props.formData.recurringBillingPeriodCustomUnit
      : props.formData.unitBasedBillingPeriodCustomUnit

    const customCycle = props.formData.schedule_type === ScheduleType.Recurring
      ? props.formData.recurringBillingPeriodCustom
      : props.formData.unitBasedBillingPeriodCustom

    return `Every ${customCycle} ${customPeriodMap[customType as BillingPeriodCustomType]}`
  }

  return periodMap[period] || period
}

// 计算分层定价的金额
const calculateTieredAmount = () => {
  if (!props.formData.unitBasedTieredPricing?.length) { return new Decimal(0) }

  let totalAmount = new Decimal(0)
  // 计算实际的单位数量 = 用户输入的数量 * 增量值
  const rawQuantity = new Decimal(props.unitQuantity)

  // Volume pricing: 整个数量都使用同一个价格区间
  if (props.formData.unitBasedTieredCurrencyPaymentMethod === RecurringTieredPaymentMethod.Volume) {
    let applicableTier = props.formData.unitBasedTieredPricing[0]

    for (const tier of props.formData.unitBasedTieredPricing) {
      const firstUnit = new Decimal(tier.firstUnit)
      const lastUnit = tier.lastUnit === INFINITE ? new Decimal(Infinity) : new Decimal(tier.lastUnit)

      // 使用原始数量(rawQuantity)来确定适用层级，而不是增量调整后的数量
      if (rawQuantity.gte(firstUnit) && rawQuantity.lte(lastUnit)) {
        applicableTier = tier
        break
      }
    }

    const perUnit = new Decimal(applicableTier.perUnit || 0)
    const flatFee = new Decimal(applicableTier.flatFee || 0)
    const result = perUnit.mul(rawQuantity).plus(flatFee)
    return result
  }

  // Graduated pricing: 每个区间的数量使用不同的价格
  const tiers = props.formData.unitBasedTieredPricing || []
  const sortedTiers = [...tiers].sort((a, b) => a.firstUnit - b.firstUnit)

  // 遍历每个价格层级
  for (let i = 0; i < sortedTiers.length; i++) {
    const tier = sortedTiers[i]
    const firstUnit = new Decimal(tier.firstUnit)
    const lastUnit = tier.lastUnit === INFINITE ? new Decimal(Infinity) : new Decimal(tier.lastUnit)

    // 使用原始数量(rawQuantity)来确定适用层级，而不是增量调整后的数量
    if (rawQuantity.gte(firstUnit)) {
      let unitsInTier

      // 计算该层级实际应用的单位数
      if (rawQuantity.gte(lastUnit)) {
        // 如果原始数量超过层级上限，则层级内单位数 = 上限 - 下限
        const tierRawUnits = lastUnit.eq(new Decimal(Infinity))
          ? rawQuantity.minus(firstUnit).plus(1)
          : lastUnit.minus(firstUnit).plus(1)

        // 转换为实际计费单位
        unitsInTier = tierRawUnits
      }
      else {
        // 如果原始数量在层级范围内，则层级内单位数 = 原始数量 - 下限 + 1
        const tierRawUnits = rawQuantity.minus(firstUnit).plus(1)

        // 转换为实际计费单位
        unitsInTier = tierRawUnits
      }

      const perUnit = new Decimal(tier.perUnit || 0)
      const flatFee = new Decimal(tier.flatFee || 0)

      // 该层级的费用 = 单位数 * 单价 + 固定费用
      const tierAmount = unitsInTier.mul(perUnit).plus(flatFee)

      totalAmount = totalAmount.plus(tierAmount)
    }
  }

  return totalAmount
}

// 计算总金额
const subtotal = computed(() => {
  let amount = new Decimal(0)
  let unitQuantity = new Decimal(1)

  if (props.formData.schedule_type === ScheduleType.UnitBased) {
    if (props.formData.unitBasedModel === UnitBasedPricingModel.StandardPricing) {
      unitQuantity = new Decimal(props.unitQuantity || 1)
    }
  }

  if (props.formData.schedule_type === ScheduleType.Recurring) {
    if (props.formData.recurringPricingModel === RecurringPricingModel.StandardPricing) {
      // 将单价乘以数量
      amount = new Decimal(props.formData.recurringAmount || 0).mul(unitQuantity)
    }
  }
  else if (props.formData.schedule_type === ScheduleType.UnitBased) {
    if (props.formData.unitBasedModel === UnitBasedPricingModel.StandardPricing) {
      // 标准单位定价: 单价 * 数量
      const increment = new Decimal(props.formData.unitBasedIncrement || 1)
      amount = new Decimal(props.formData.unitBasedAmount || 0).mul(increment).mul(unitQuantity)
    }
    else if (props.formData.unitBasedModel === UnitBasedPricingModel.TieredPricing) {
      // 分层单位定价
      amount = calculateTieredAmount()
    }
  }
  else {
    // 一次性计费，也考虑数量
    amount = new Decimal(props.formData.oneOffAmount || 0)
  }

  return amount
})

// 获取当前使用的货币
const currentCurrency = computed(() => {
  if (props.formData.schedule_type === ScheduleType.Recurring) {
    return props.formData.recurringCurrency
  }
  else if (props.formData.schedule_type === ScheduleType.UnitBased) {
    if (props.formData.unitBasedModel === UnitBasedPricingModel.StandardPricing) {
      return props.formData.unitBasedCurrency
    }
    else {
      return props.formData.unitBasedTieredCurrency
    }
  }
  else {
    return props.formData.oneOffCurrency
  }
})

// 计算 GST 税费
const gstAmount = computed(() => {
  if (props.formData.is_inclusive_gst) {
    return new Decimal(0)
  }

  if (props.gst.fee_rate === SurchargeRate.PERCENTAGE) {
    return subtotal.value.mul(new Decimal(props.gst.fee_value).div(100))
  }
  else if (props.gst.fee_rate === SurchargeRate.FIXED) {
    return new Decimal(props.gst.fee_value)
  }

  return new Decimal(0)
})

// 计算手续费 (1%)
const surchargeAmount = computed(() => {
  const unitQuantity = new Decimal(1)

  if (!props.formData.is_surcharge) {
    return new Decimal(0)
  }

  // 计算附加费 - 根据类型处理
  if (props.surchargeRate.fee_rate === SurchargeRate.PERCENTAGE) {
    // 百分比费率
    const rate = Number.parseFloat(props.surchargeRate.fee_value || '0') / 100
    return subtotal.value.mul(new Decimal(rate)).mul(unitQuantity)
  }
  else if (props.surchargeRate.fee_rate === SurchargeRate.FIXED) {
    // 固定费率
    return new Decimal(props.surchargeRate.fee_value || '0').mul(unitQuantity)
  }
  return new Decimal(0)
})

// 计算总金额包含税费
const totalAmount = computed(() => {
  if (props.formData.is_inclusive_gst) {
    return subtotal.value.plus(surchargeAmount.value)
  }
  else {
    return subtotal.value.plus(gstAmount.value).plus(surchargeAmount.value)
  }
})

// 计算预测金额
const forecastSubtotal = computed(() => {
  let terms = new Decimal(1)
  let periodsCount = new Decimal(1)

  if (
    [ScheduleType.Recurring, ScheduleType.UnitBased].includes(props.formData.schedule_type as ScheduleType)
    && props.formData.end_date_type === PlanEndDateType.SpecifyByTerm
    && props.formData.end_terms
  ) {
    terms = new Decimal(props.formData.end_terms || 1)
  }

  // 当使用指定结束日期时，需要考虑周期数
  if (props.formData.schedule_type === ScheduleType.Recurring) {
    if (props.formData.end_date_type === PlanEndDateType.SpecifyByEndDate && props.formData.end_date) {
      periodsCount = new Decimal(
        getDisplayPeriodCount(
          props.formData.recurringBillingPeriod,
          props.formData.end_date,
          props.formData.recurringBillingPeriodCustom,
          props.formData.recurringBillingPeriodCustomUnit,
        ) || 1,
      )
    }
  }

  if (props.formData.schedule_type === ScheduleType.UnitBased) {
    if (props.formData.end_date_type === PlanEndDateType.SpecifyByEndDate && props.formData.end_date) {
      periodsCount = new Decimal(
        getDisplayPeriodCount(
          props.formData.unitBasedBillingPeriod,
          props.formData.end_date,
          props.formData.unitBasedBillingPeriodCustom,
          props.formData.unitBasedBillingPeriodCustomUnit,
        ) || 1,
      )
    }
  }

  let amount = subtotal.value.mul(forecastMonths.value).mul(terms).mul(periodsCount)

  if (
    [ScheduleType.Recurring, ScheduleType.UnitBased].includes(props.formData.schedule_type as ScheduleType)
    && props.formData.end_date_type === PlanEndDateType.SpecifyByTerm
    && props.formData.end_terms
  ) {
    amount = new Decimal(amount)
  }
  return amount
})

// 计算预测 GST
const forecastGst = computed(() => {
  const unitQuantity = new Decimal(1)
  let terms = new Decimal(1)

  if (
    [ScheduleType.Recurring, ScheduleType.UnitBased].includes(props.formData.schedule_type as ScheduleType)
    && props.formData.end_date_type === PlanEndDateType.SpecifyByTerm
    && props.formData.end_terms
  ) {
    terms = new Decimal(props.formData.end_terms || 1)
  }

  // 当使用指定结束日期时，需要考虑周期数
  if (props.formData.schedule_type === ScheduleType.Recurring) {
    if (props.formData.end_date_type === PlanEndDateType.SpecifyByEndDate && props.formData.end_date) {
      // 使用正确的周期数来计算
      const periodsCount = getDisplayPeriodCount(props.formData.recurringBillingPeriod, props.formData.end_date, props.formData.recurringBillingPeriodCustom, props.formData.recurringBillingPeriodCustomUnit)
      // 直接用周期数乘以GST
      return gstAmount.value.mul(new Decimal(periodsCount)).mul(unitQuantity).mul(terms)
    }
  }

  if (props.formData.schedule_type === ScheduleType.UnitBased) {
    if (props.formData.end_date_type === PlanEndDateType.SpecifyByEndDate && props.formData.end_date) {
      // 使用正确的周期数来计算
      const periodsCount = getDisplayPeriodCount(props.formData.unitBasedBillingPeriod, props.formData.end_date, props.formData.unitBasedBillingPeriodCustom, props.formData.unitBasedBillingPeriodCustomUnit)
      // 直接用周期数乘以GST
      return gstAmount.value.mul(unitQuantity).mul(terms).mul(new Decimal(periodsCount))
    }
  }

  let amount = gstAmount.value.mul(forecastMonths.value).mul(unitQuantity).mul(terms)

  if (
    [ScheduleType.Recurring, ScheduleType.UnitBased].includes(props.formData.schedule_type as ScheduleType)
    && props.formData.end_date_type === PlanEndDateType.SpecifyByTerm
  ) {
    amount = new Decimal(amount)
  }
  return amount
})

// 计算预测手续费
const forecastSurcharge = computed(() => {
  const unitQuantity = new Decimal(1)
  let terms = new Decimal(1)

  if (
    [ScheduleType.Recurring, ScheduleType.UnitBased].includes(props.formData.schedule_type as ScheduleType)
    && props.formData.end_date_type === PlanEndDateType.SpecifyByTerm
    && props.formData.end_terms
  ) {
    terms = new Decimal(props.formData.end_terms || 1)
  }

  // 当使用指定结束日期时，需要考虑周期数
  if (props.formData.schedule_type === ScheduleType.Recurring) {
    if (props.formData.end_date_type === PlanEndDateType.SpecifyByEndDate && props.formData.end_date) {
      // 使用正确的周期数来计算
      const periodsCount = getDisplayPeriodCount(props.formData.recurringBillingPeriod, props.formData.end_date, props.formData.recurringBillingPeriodCustom, props.formData.recurringBillingPeriodCustomUnit)
      // 直接用周期数乘以手续费
      return surchargeAmount.value.mul(new Decimal(periodsCount)).mul(unitQuantity).mul(terms)
    }
  }

  if (props.formData.schedule_type === ScheduleType.UnitBased) {
    if (props.formData.end_date_type === PlanEndDateType.SpecifyByEndDate && props.formData.end_date) {
      const periodsCount = getDisplayPeriodCount(props.formData.unitBasedBillingPeriod, props.formData.end_date, props.formData.unitBasedBillingPeriodCustom, props.formData.unitBasedBillingPeriodCustomUnit)
      return surchargeAmount.value.mul(new Decimal(periodsCount)).mul(unitQuantity).mul(terms)
    }
  }

  let amount = surchargeAmount.value.mul(forecastMonths.value).mul(unitQuantity).mul(terms)

  if (
    [ScheduleType.Recurring, ScheduleType.UnitBased].includes(props.formData.schedule_type as ScheduleType)
    && props.formData.end_date_type === PlanEndDateType.SpecifyByTerm
  ) {
    amount = new Decimal(amount)
  }
  return amount
})

// 计算预测总金额
const forecastTotal = computed(() => {
  if (props.formData.is_inclusive_gst) {
    // 如果包含GST，总额为预测小计加手续费
    // 确保返回值是Decimal类型
    return new Decimal(forecastSubtotal.value.toString()).plus(new Decimal(forecastSurcharge.value.toString()))
  }
  else {
    // 如果不包含GST，总额为预测小计加GST加手续费
    // 确保返回值是Decimal类型
    return new Decimal(forecastSubtotal.value.toString())
      .plus(new Decimal(forecastGst.value.toString()))
      .plus(new Decimal(forecastSurcharge.value.toString()))
  }
})

// 获取用于显示的周期数量
const getDisplayPeriodCount = (
  recurringBillingPeriod: BillingPeriodType | null | undefined,
  end_date: Date | string | null | undefined,
  recurringBillingPeriodCustom: number | string | null | undefined,
  recurringBillingPeriodCustomUnit: BillingPeriodCustomType | null | undefined,
) => {
  if (!recurringBillingPeriod || !end_date) {
    return 1
  }

  const startDate = dayjs(props.startDate)
  const endDate = dayjs(end_date)
  const billingPeriod = recurringBillingPeriod

  if (billingPeriod !== BillingPeriodType.Custom && billingPeriod) {
    // 标准周期计算
    const { unit, multiplier } = standardPeriodMap[billingPeriod as BillingPeriodType]

    if (unit === 'day') {
      const startDateOnly = startDate.startOf('day')
      const endDateOnly = endDate.startOf('day')
      const diffInDays = endDateOnly.diff(startDateOnly, 'day') + 1
      return Math.max(1, Math.ceil(diffInDays / multiplier))
    }
    else if (unit === 'week') {
      // 对于周计算，使用dayjs的直接周差异计算，但需要特殊处理
      const startDateOnly = startDate.startOf('day')
      const endDateOnly = endDate.startOf('day')

      // 计算周差异
      const diffInWeeks = endDateOnly.diff(startDateOnly, 'week')

      // 检查是否有剩余天数
      const daysInFullWeeks = diffInWeeks * 7
      const diffInDays = endDateOnly.diff(startDateOnly, 'day') + 1
      const hasRemainingDays = diffInDays > daysInFullWeeks

      // 总周数 = 完整周数 + (如果有剩余天数则+1)
      const totalWeeks = Math.max(1, diffInWeeks + (hasRemainingDays ? 1 : 0))

      // 考虑multiplier因素，比如Fortnightly (两周为一个周期)
      return Math.max(1, Math.ceil(totalWeeks / multiplier))
    }
    else if (unit === 'month') {
      // 对于月度计费，特殊处理
      const startMonth = startDate.month()
      const startYear = startDate.year()
      const endMonth = endDate.month()
      const endYear = endDate.year()

      // 计算年度差异
      const yearDiff = endYear - startYear
      // 计算月度差异
      const monthDiff = endMonth - startMonth + (yearDiff * 12)

      // 计算周期数，不足一个周期按一个完整周期计算
      if (monthDiff === 0 || (endDate.date() < startDate.date() && monthDiff > 0)) {
        return Math.max(1, Math.ceil(monthDiff / multiplier))
      }
      else if (endDate.date() === startDate.date()) {
        // 如果日期相同，不额外增加月份
        return Math.max(1, Math.ceil(monthDiff / multiplier))
      }
      return Math.max(1, Math.ceil((monthDiff + 1) / multiplier))
    }
    else if (unit === 'year') {
      // 对于年度计算，特殊处理
      const startMonth = startDate.month()
      const startDay = startDate.date()
      const endMonth = endDate.month()
      const endDay = endDate.date()
      const yearDiff = endDate.year() - startDate.year()

      // 判断是否跨了年
      if (yearDiff === 0) {
        return 1
      }
      else {
        // 判断是否跨过了开始日期对应的月和日
        // 如果结束日期的月日小于开始日期的月日，说明不满一年
        const hasCompleteYear = (endMonth > startMonth)
          || (endMonth === startMonth && endDay > startDay) // 注意：改为大于，而不是大于等于
        const totalYears = yearDiff + (hasCompleteYear ? 1 : 0)
        const result = Math.max(1, Math.ceil(totalYears / multiplier))
        return result
      }
    }
    else {
      // 其他周期类型的计算
      const diffInUnit = endDate.diff(startDate, unit as dayjs.OpUnitType)
      // 确保不足一个周期按一个完整周期算
      return Math.max(1, Math.ceil(diffInUnit / multiplier))
    }
  }
  else if (recurringBillingPeriodCustom && recurringBillingPeriodCustomUnit) {
    // 自定义周期计算
    const customCycle = recurringBillingPeriodCustom
    const customUnit = recurringBillingPeriodCustomUnit as BillingPeriodCustomType
    const unit = customPeriodMap[customUnit]

    if (unit) {
      const multiplierValue = Number(customCycle.toString())

      if (unit === 'day') {
        // 按天计算时，包含起始日和结束日
        // 首先确保只保留日期部分，忽略时间部分
        const startDateOnly = startDate.startOf('day')
        const endDateOnly = endDate.startOf('day')

        // 计算天数差异，并加1包含起始日和结束日
        const diffInDays = endDateOnly.diff(startDateOnly, 'day') + 1

        return Math.max(1, Math.ceil(diffInDays / multiplierValue))
      }
      else if (unit === 'week') {
        // 对于自定义周计算，特殊处理
        const startDay = startDate.day()
        const endDay = endDate.day()

        // 计算总天数 - 确保只保留日期部分
        const startDateOnly = startDate.startOf('day')
        const endDateOnly = endDate.startOf('day')
        const diffInDays = endDateOnly.diff(startDateOnly, 'day') + 1

        const fullWeeks = Math.floor(diffInDays / 7)
        const hasPartialWeek = diffInDays % 7 > 0 || endDay < startDay
        const totalWeeks = fullWeeks + (hasPartialWeek ? 1 : 0)

        // 按照用户自定义的周期倍数计算
        return Math.max(1, Math.ceil(totalWeeks / multiplierValue))
      }
      else if (unit === 'month') {
        // 对于自定义月度周期，特殊处理
        const startMonth = startDate.month()
        const startYear = startDate.year()
        const endMonth = endDate.month()
        const endYear = endDate.year()

        // 计算年度差异
        const yearDiff = endYear - startYear
        // 计算月度差异
        const monthDiff = endMonth - startMonth + (yearDiff * 12)

        // 计算周期数，不足一个周期按一个完整周期计算
        if (monthDiff === 0 || (endDate.date() < startDate.date() && monthDiff > 0)) {
          return Math.max(1, Math.ceil(monthDiff / multiplierValue))
        }
        else if (endDate.date() === startDate.date()) {
          // 如果日期相同，不额外增加月份
          return Math.max(1, Math.ceil(monthDiff / multiplierValue))
        }
        return Math.max(1, Math.ceil((monthDiff + 1) / multiplierValue))
      }
      else if (unit === 'year') {
        // 对于自定义年度计算，特殊处理
        const startMonth = startDate.month()
        const startDay = startDate.date()
        const endMonth = endDate.month()
        const endDay = endDate.date()
        const yearDiff = endDate.year() - startDate.year()

        // 判断是否跨了年
        if (yearDiff === 0) {
          return 1
        }
        else {
          // 判断是否跨过了开始日期对应的月和日
          // 如果结束日期的月日小于开始日期的月日，说明不满一年
          const hasCompleteYear = (endMonth > startMonth)
            || (endMonth === startMonth && endDay > startDay) // 注意：改为大于，而不是大于等于
          const totalYears = yearDiff + (hasCompleteYear ? 1 : 0)
          const result = Math.max(1, Math.ceil(totalYears / multiplierValue))
          return result
        }
      }
      else {
        // 其他周期类型的计算
        const diffInUnit = endDate.diff(startDate, unit as dayjs.OpUnitType)
        return Math.max(1, Math.ceil(diffInUnit / multiplierValue))
      }
    }
  }

  return 1
}

const formatSurcharge = () => {
  if (!props.surchargeRate || !props.surchargeRate.fee_rate) { return '' }

  if (props.surchargeRate.fee_rate === '1') {
    // 百分比费率
    const rate = Number.parseFloat(props.surchargeRate.fee_value || '0')
    return `${rate}%`
  }
  else if (props.surchargeRate.fee_rate === '2') {
    // 固定费率
    const rate = Number.parseFloat(props.surchargeRate.fee_value || '0')
    return `${formatAmount(rate, currentCurrency.value || 'AUD')}`
  }
  return ''
}

const getEndDateBillingPeriod = (end_terms: number) => {
  const now = dayjs()

  let billingPeriod
  let customUnit
  let customPeriod

  if (props.formData.schedule_type === ScheduleType.Recurring) {
    billingPeriod = props.formData.recurringBillingPeriod
    customUnit = props.formData.recurringBillingPeriodCustomUnit
    customPeriod = props.formData.recurringBillingPeriodCustom
  }
  else if (props.formData.schedule_type === ScheduleType.UnitBased) {
    billingPeriod = props.formData.unitBasedBillingPeriod
    customUnit = props.formData.unitBasedBillingPeriodCustomUnit
    customPeriod = props.formData.unitBasedBillingPeriodCustom
  }
  else {
    return undefined
  }

  // 处理标准周期类型
  if (billingPeriod && billingPeriod !== BillingPeriodType.Custom) {
    const period = billingPeriod as BillingPeriodType
    const { unit, multiplier } = standardPeriodMap[period]
    return now.add(end_terms * multiplier, unit).format('DD/MM/YYYY')
  }

  // 处理自定义周期类型
  if (!customUnit || !customPeriod) {
    return undefined
  }

  // 确保customUnit是有效的枚举值
  if (Object.values(BillingPeriodCustomType).includes(customUnit as BillingPeriodCustomType)) {
    const unit = customPeriodMap[customUnit as BillingPeriodCustomType]
    return now.add(customPeriod * end_terms, unit).format('DD/MM/YYYY')
  }

  return undefined
}

// 获取预测Unit-based的计费单位数量
const getUnitBasedVolumeNumber = computed(() => {
  let volumeNumber = 0

  for (const tier of props.formData.unitBasedTieredPricing) {
    if (props.unitQuantity >= tier.firstUnit && tier.lastUnit !== INFINITE && Number(tier.lastUnit) >= Number(props.unitQuantity)) {
      volumeNumber = tier.perUnit
    }
    else if (props.unitQuantity >= tier.firstUnit && tier.lastUnit === INFINITE) {
      volumeNumber = tier.perUnit
    }
  }

  return volumeNumber
})

// 获取预测 Unit-based的计费单位数量 附加费
const getUnitBasedVolumeNumberWithFlatFee = computed(() => {
  let volumeNumber = 0

  for (const tier of props.formData.unitBasedTieredPricing) {
    if (props.unitQuantity >= tier.firstUnit && tier.lastUnit !== INFINITE && Number(tier.lastUnit) >= Number(props.unitQuantity)) {
      volumeNumber = tier.flatFee
    }
    else if (props.unitQuantity >= tier.firstUnit && tier.lastUnit === INFINITE) {
      volumeNumber = tier.flatFee
    }
  }

  return volumeNumber
})

// 获取结束日期
const getEndDate = computed(() => {
  if (!props.formData.plan_id) {
    return ''
  }

  if (props.formData.schedule_type === ScheduleType.OneOff) {
    return 'NA'
  }

  switch (props.formData.end_date_type) {
    case PlanEndDateType.SpecifyByEndDate:
      return props.formData.end_date && dayjs(props.formData.end_date).format('DD/MM/YYYY')

    case PlanEndDateType.GoodTillCancel:
      return 'Infinite'

    case PlanEndDateType.SpecifyByTerm:
      return props.formData.end_terms && getEndDateBillingPeriod(props.formData.end_terms)

    default:
      break
  }

  return ''
})

watch([() => props.formData.end_date_type, () => props.formData.schedule_type], () => {
  forecastMonths.value = 1
})

// 计算实际计费单位
const actualBillingUnits = computed(() => {
  if (props.formData.schedule_type === ScheduleType.UnitBased
    && props.formData.unitBasedModel === UnitBasedPricingModel.StandardPricing
  ) {
    const increment = new Decimal(props.formData.unitBasedIncrement || 1)
    return increment.toNumber()
  }

  return 1
})

// 获取当前使用的单价(Unit-based时可用)
const currentUnitPrice = computed(() => {
  if (props.formData.schedule_type !== ScheduleType.UnitBased) {
    return new Decimal(0)
  }

  if (props.formData.unitBasedModel === UnitBasedPricingModel.StandardPricing) {
    return new Decimal(props.formData.unitBasedAmount || 0)
  }

  else if (props.formData.unitBasedModel === UnitBasedPricingModel.TieredPricing) {
    // 找到对应数量的价格档位 - 使用原始数量比较
    const rawQuantity = props.unitQuantity
    const tiers = props.formData.unitBasedTieredPricing || []
    let applicableTier = tiers[0] || { perUnit: 0 }

    for (const tier of tiers) {
      const firstUnit = tier.firstUnit
      const lastUnit = tier.lastUnit === INFINITE ? Infinity : Number(tier.lastUnit)

      if (rawQuantity >= firstUnit && rawQuantity <= lastUnit) {
        applicableTier = tier
        break
      }
    }

    return new Decimal(applicableTier.perUnit || 0)
  }

  return new Decimal(0)
})

// 获取当前的固定费用(Unit-based tiered时可用)
const currentFlatFee = computed(() => {
  if (props.formData.schedule_type !== ScheduleType.UnitBased
    || props.formData.unitBasedModel !== UnitBasedPricingModel.TieredPricing) {
    return new Decimal(0)
  }

  // 找到对应数量的价格档位 - 使用原始数量比较
  const rawQuantity = props.unitQuantity
  const tiers = props.formData.unitBasedTieredPricing || []
  let applicableTier = tiers[0] || { flatFee: 0 }

  for (const tier of tiers) {
    const firstUnit = tier.firstUnit
    const lastUnit = tier.lastUnit === INFINITE ? Infinity : Number(tier.lastUnit)

    if (rawQuantity >= firstUnit && rawQuantity <= lastUnit) {
      applicableTier = tier
      break
    }
  }

  return new Decimal(applicableTier.flatFee || 0)
})
</script>

<template>
  <div class="plan-subscription-preview flex-1">
    <h1 v-if="props.isShowForecast" class="plan-subscription-title">
      Subscription review
    </h1>
    <div class="preview-content">
      <!-- 客户信息卡片 -->
      <div class="preview-card mb-5 rounded-lg overflow-hidden">
        <!-- 客户信息部分 -->
        <div class="flex">
          <div class="preview-card-label">
            Customer name:
          </div>
          <div class="preview-card-content">
            {{ props.name }}
          </div>
        </div>

        <div v-if="props.isShowForecast" class="flex">
          <div class="preview-card-label">
            Plan name:
          </div>
          <div class="preview-card-content">
            {{ props.planName }}
          </div>
        </div>

        <div class="flex ">
          <div class="preview-card-label">
            Start date:
          </div>
          <div class="preview-card-content">
            {{ dayjs(props.startDate).format('DD/MM/YYYY') }}
          </div>
        </div>

        <div class="flex ">
          <div class="preview-card-label">
            End date:
          </div>
          <div class="preview-card-content">
            {{ getEndDate }}
          </div>
        </div>

        <div class="flex">
          <div class="preview-card-label">
            Frequency:
          </div>
          <div class="preview-card-content">
            {{ props.formData.schedule_type === ScheduleType.Recurring
              ? getBillingPeriodText(props.formData.recurringBillingPeriod)
              : props.formData.schedule_type === ScheduleType.UnitBased
                ? getBillingPeriodText(props.formData.unitBasedBillingPeriod)
                : 'One-off' }}
          </div>
        </div>
        <!-- Unit-based specific fields -->
        <template v-if="props.formData.schedule_type === ScheduleType.UnitBased">
          <div class="flex">
            <div class="preview-card-label">
              Unit type:
            </div>
            <div class="preview-card-content">
              {{ props.formData.unitBasedModel === UnitBasedPricingModel.StandardPricing
                ? (props.formData.unitBasedModelType === 'Custom'
                  ? props.formData.unitBasedModelTypeCustom
                  : props.formData.unitBasedModelType)
                : (props.formData.unitBasedTieredModelType === 'Custom'
                  ? props.formData.unitBasedTieredModelTypeCustom
                  : props.formData.unitBasedTieredModelType) }}
            </div>
          </div>
          <div
            v-if="props.formData.schedule_type === ScheduleType.UnitBased && props.formData.unitBasedModel === UnitBasedPricingModel.StandardPricing"
            class="flex"
          >
            <div class="preview-card-label">
              Increment:
            </div>
            <div class="preview-card-content">
              {{ props.formData.unitBasedIncrement }}
              {{ props.formData.unitBasedModelType }}
            </div>
          </div>
          <div class="flex">
            <div class="preview-card-label">
              Quantity:
            </div>
            <div class="preview-card-content">
              {{ props.unitQuantity }}
            </div>
          </div>
          <div class="flex">
            <div class="preview-card-label">
              Billable units:
            </div>
            <div class="preview-card-content">
              {{ actualBillingUnits }}
            </div>
          </div>
        </template>

        <div class="preview-card-divider" />
      </div>

      <!-- 费用信息 -->
      <div class="preview-card mb-5 rounded-lg overflow-hidden">
        <div class="flex">
          <div class="preview-card-label">
            Subtotal:
          </div>
          <div class="preview-card-content">
            <template v-if="props.formData.schedule_type === ScheduleType.Recurring">
              {{ formatAmount(subtotal, currentCurrency) }}
            </template>
            <template v-if="[ScheduleType.UnitBased].includes(props.formData.schedule_type as ScheduleType)">
              <template v-if="props.formData.schedule_type === ScheduleType.UnitBased">
                <div v-if="props.formData.unitBasedModel === UnitBasedPricingModel.StandardPricing">
                  <span>
                    {{ formatAmount(currentUnitPrice, currentCurrency) }}
                  </span>
                  <span>
                    × {{ actualBillingUnits }}
                  </span>
                  <span>
                    × {{ props.unitQuantity }}
                  </span>
                  <span>
                    = {{ formatAmount(subtotal, currentCurrency) }}
                  </span>
                </div>
                <div v-else-if="props.formData.unitBasedModel === UnitBasedPricingModel.TieredPricing">
                  <div
                    v-if="props.formData.unitBasedTieredCurrencyPaymentMethod === RecurringTieredPaymentMethod.Volume"
                  >
                    <span>
                      {{ formatAmount(currentUnitPrice, currentCurrency) }}
                    </span>
                    <span>
                      × {{ props.unitQuantity }}
                    </span>
                    <span>
                      + {{ formatAmount(currentFlatFee, currentCurrency) }}
                    </span>
                    <span>
                      (flat fee) = {{ formatAmount(subtotal, currentCurrency) }}
                    </span>
                  </div>
                  <div v-else>
                    {{ formatAmount(subtotal, currentCurrency) }}
                  </div>
                </div>
              </template>
              <template v-else-if="props.formData.schedule_type === ScheduleType.Recurring">
                {{ formatAmount(subtotal, currentCurrency) }}
              </template>
              <template v-else>
                {{ formatAmount(subtotal, currentCurrency) }}
              </template>
            </template>
            <template v-if="[ScheduleType.OneOff].includes(props.formData.schedule_type as ScheduleType)">
              {{ formatAmount(subtotal, currentCurrency) }}
            </template>
          </div>
        </div>

        <div class="flex">
          <div class="preview-card-label">
            GST:
          </div>
          <div class="preview-card-content">
            {{ formatAmount(gstAmount, currentCurrency) }} <template v-if="!gstAmount.eq(0)">
              <div class="preview-card-content-divider" />
              {{ props.gst.fee_rate === SurchargeRate.PERCENTAGE ? `${props.gst.fee_value}%` : props.gst.fee_value }}
            </template>
          </div>
        </div>

        <div class="flex">
          <div class="preview-card-label">
            Surcharge:
          </div>
          <div class="preview-card-content">
            {{ formatAmount(surchargeAmount, currentCurrency) }}
            <div class="preview-card-content-divider" />
            {{ formatSurcharge() }}
          </div>
        </div>

        <div class="flex">
          <div class="preview-card-label">
            Total:
          </div>
          <div class="preview-card-content font-bold primary-color">
            {{ formatAmount(totalAmount, currentCurrency) }}
          </div>
        </div>
      </div>

      <divide v-if="props.isShowForecast" class="support-divide" />

      <!-- 预测部分 -->
      <template v-if="props.isShowForecast">
        <div class="preview-card">
          <div class="forecast-header">
            <h3 class="forecast-title">
              Forecast
            </h3>
            <div class="forecast-disclaimer">
              Disclaimer: Forecasts are for reference only and may not be accurate. Use at your own discretion.
            </div>
          </div>

          <div class="preview-card-divider" />

          <div class="flex">
            <div class="preview-card-label">
              Subtotal:
            </div>
            <div class="preview-card-content">
              <template v-if="[ScheduleType.UnitBased].includes(props.formData.schedule_type as ScheduleType)">
                <template v-if="props.formData.unitBasedModel === UnitBasedPricingModel.StandardPricing">
                  <template v-if="props.formData.end_date_type === PlanEndDateType.SpecifyByEndDate">
                    <span class="mr-2">{{ formatAmount(props.formData.unitBasedAmount, currentCurrency) }}</span>
                    <span class="mr-2">×</span>
                    <span class="mr-2">{{ actualBillingUnits }}</span>
                    <span class="mr-2">×</span>
                    <span class="mr-2">{{ props.unitQuantity }}</span>
                    <span class="mr-2">×</span>
                    <span class="mr-2">{{
                      getDisplayPeriodCount(
                        props.formData.unitBasedBillingPeriod,
                        props.formData.end_date,
                        props.formData.unitBasedBillingPeriodCustom,
                        props.formData.unitBasedBillingPeriodCustomUnit,
                      )
                    }}</span>
                    <span class="mr-2">periods</span>
                    <span>= {{ formatAmount(forecastSubtotal, currentCurrency) }}</span>
                  </template>
                  <template
                    v-if="props.formData.end_date_type === PlanEndDateType.SpecifyByTerm && props.formData.end_terms"
                  >
                    <span class="mr-2">{{ formatAmount(props.formData.unitBasedAmount, currentCurrency) }}</span>
                    <span class="mr-2">×</span>
                    <span class="mr-2">{{ props.formData.end_terms }}</span>
                    <span class="mr-2">×</span>
                    <span class="mr-2">{{ props.unitQuantity }}</span>
                    <span>= {{ formatAmount(forecastSubtotal, currentCurrency) }}</span>
                  </template>
                  <template v-if="props.formData.end_date_type === PlanEndDateType.GoodTillCancel">
                    <InputNumber
                      v-model="forecastMonths" class="forecast-input mr-2" :min="1" :max="120"
                      input-class="text-center"
                    />
                    <span class="mr-2">months</span>
                    <span class="mr-2">×</span>
                    <span class="mr-2">{{ formatAmount(props.formData.unitBasedAmount, currentCurrency) }}</span>
                    <span class="mr-2">×</span>
                    <span class="mr-2">{{ actualBillingUnits }}</span>
                    <span class="mr-2">×</span>
                    <span class="mr-2">{{ props.unitQuantity }}</span>
                    <span>= {{ formatAmount(forecastSubtotal, currentCurrency) }}</span>
                  </template>
                </template>
                <template v-if="props.formData.unitBasedModel === UnitBasedPricingModel.TieredPricing">
                  <template
                    v-if="props.formData.unitBasedTieredCurrencyPaymentMethod === RecurringTieredPaymentMethod.Volume"
                  >
                    <template v-if="props.formData.end_date_type === PlanEndDateType.SpecifyByEndDate">
                      (
                      <span>{{ formatAmount(getUnitBasedVolumeNumber, currentCurrency) }}</span>
                      <span>×</span>
                      <span>{{ props.unitQuantity }}</span>
                      <span>+</span>
                      <span>{{ getUnitBasedVolumeNumberWithFlatFee }} (flat fee)</span>
                      )
                      <span>
                        × {{ getDisplayPeriodCount(
                          props.formData.unitBasedBillingPeriod,
                          props.formData.end_date,
                          props.formData.unitBasedBillingPeriodCustom,
                          props.formData.unitBasedBillingPeriodCustomUnit,
                        ) }}
                        periods
                      </span>
                      <span>= {{ formatAmount(forecastSubtotal, currentCurrency) }}</span>
                    </template>
                    <template
                      v-if="props.formData.end_date_type === PlanEndDateType.SpecifyByTerm && props.formData.end_terms"
                    >
                      (
                      <span class="mr-2">{{ formatAmount(getUnitBasedVolumeNumber, currentCurrency) }}</span>
                      <span class="mr-2">×</span>
                      <span class="mr-2">{{ props.unitQuantity }}</span>
                      <span class="mr-2">+</span>
                      <span class="mr-2">{{ getUnitBasedVolumeNumberWithFlatFee }} (flat fee)</span>
                      )
                      <span class="mr-2">×</span>
                      <span class="mr-2">{{ props.formData.end_terms }}</span>
                      <span>= {{ formatAmount(forecastSubtotal, currentCurrency) }}</span>
                    </template>
                    <template v-if="props.formData.end_date_type === PlanEndDateType.GoodTillCancel">
                      <InputNumber
                        v-model="forecastMonths" class="forecast-input mr-2" :min="1" :max="120"
                        input-class="text-center"
                      />
                      <span class="mr-2">months</span>
                      <span class="mr-2">×</span>
                      <span class="mr-2">{{ props.unitQuantity }}</span>
                      <span class="mr-2">×</span>
                      <span class="mr-2">{{ formatAmount(getUnitBasedVolumeNumber, currentCurrency) }}</span>
                      <span class="mr-2">+</span>
                      <span class="mr-2">{{ getUnitBasedVolumeNumberWithFlatFee }} (flat fee)</span>
                      <span>= {{ formatAmount(forecastSubtotal, currentCurrency) }}</span>
                    </template>
                  </template>
                  <template
                    v-if="props.formData.unitBasedTieredCurrencyPaymentMethod === RecurringTieredPaymentMethod.Graduated"
                  >
                    <div class="flex flex-col">
                      <div v-for="(tier, index) in props.formData.unitBasedTieredPricing" :key="index">
                        <template v-if="props.unitQuantity >= tier.firstUnit">
                          <div class="flex items-center">
                            (
                            <span>{{ formatAmount(new Decimal(tier.perUnit || 0), currentCurrency) }}</span>
                            <span>×</span>
                            <span>
                              {{ tier.lastUnit === INFINITE ? Number(props.unitQuantity) - Number(tier.firstUnit) + 1 : Math.min(Number(props.unitQuantity), Number(tier.lastUnit)) - Number(tier.firstUnit) + 1 }}
                            </span>
                            <span>+</span>
                            <span>{{ formatAmount(new Decimal(tier.flatFee || 0), currentCurrency) }}</span>
                            <span>(flat fee)</span>
                            )
                            <span>
                              × {{ getDisplayPeriodCount(
                                props.formData.unitBasedBillingPeriod,
                                props.formData.end_date,
                                props.formData.unitBasedBillingPeriodCustom,
                                props.formData.unitBasedBillingPeriodCustomUnit,
                              ) }}
                              periods
                            </span>
                            <span class="mr-2">=</span>
                            <span>
                              {{
                                formatAmount(new Decimal(tier.perUnit || 0)
                                  .mul(new Decimal(tier.lastUnit === INFINITE ? Number(props.unitQuantity) - Number(tier.firstUnit) + 1 : Math.min(Number(props.unitQuantity), Number(tier.lastUnit)) - Number(tier.firstUnit) + 1))
                                  .plus(new Decimal(tier.flatFee || 0))
                                  .mul(getDisplayPeriodCount(
                                    props.formData.unitBasedBillingPeriod,
                                    props.formData.end_date,
                                    props.formData.unitBasedBillingPeriodCustom,
                                    props.formData.unitBasedBillingPeriodCustomUnit,
                                  )), currentCurrency)
                              }}
                            </span>
                          </div>
                        </template>
                      </div>
                      <div class="mt-2 font-medium">
                        Total:
                        <template v-if="props.formData.end_date_type === PlanEndDateType.GoodTillCancel">
                          <InputNumber
                            v-model="forecastMonths" class="forecast-input mr-2" :min="1" :max="120"
                            input-class="text-center"
                          />
                          <span class="mr-2">months</span>
                          <span class="mr-2">×</span>
                        </template>
                        <template
                          v-if="props.formData.end_date_type === PlanEndDateType.SpecifyByTerm && props.formData.end_terms"
                        >
                          <span class="mr-2">
                            {{ formatAmount(subtotal, currentCurrency) }}
                          </span>
                          <span class="mr-2">×</span>
                          <span class="mr-2">{{ props.formData.end_terms }}</span>
                          <span class="mr-2">periods</span>
                        </template>
                        <span class="mr-2">
                          {{ formatAmount(forecastSubtotal, currentCurrency) }}
                        </span>
                      </div>
                    </div>
                  </template>
                </template>
              </template>
              <template v-if="[ScheduleType.Recurring].includes(props.formData.schedule_type as ScheduleType)">
                <template v-if="props.formData.end_date_type === PlanEndDateType.SpecifyByEndDate">
                  <span>
                    {{ formatAmount(subtotal, currentCurrency) }}
                  </span>
                  <span>
                    ×
                  </span>
                  <span
                    class="mr-2"
                  >{{ getDisplayPeriodCount(props.formData.recurringBillingPeriod, props.formData.end_date, props.formData.recurringBillingPeriodCustom, props.formData.recurringBillingPeriodCustomUnit) }}</span>
                  <span class="mr-2">periods</span>
                  <span class="mr-2">×</span>
                  <span>
                    {{ forecastMonths }}
                  </span>
                  <span>
                    =
                  </span>
                  <span>{{ formatAmount(forecastSubtotal, currentCurrency) }}</span>
                </template>
                <template
                  v-if="props.formData.end_date_type === PlanEndDateType.SpecifyByTerm && props.formData.end_terms"
                >
                  <span>
                    {{ formatAmount(subtotal, currentCurrency) }}
                  </span>
                  <span>
                    ×
                  </span>
                  <span>
                    {{ props.formData.end_terms }}
                  </span>
                  <span>
                    =
                  </span>
                  <span>
                    {{ formatAmount(forecastSubtotal, currentCurrency) }}
                  </span>
                </template>
                <template v-if="props.formData.end_date_type === PlanEndDateType.GoodTillCancel">
                  <InputNumber
                    v-model="forecastMonths" class="forecast-input" :min="1" :max="120"
                    input-class="text-center"
                  />
                  <span class="mx-2">
                    <span class="mr-2">months</span>
                    <span class="mr-2">×</span>
                    <span class="mr-2">{{ formatAmount(subtotal, currentCurrency) }}</span>
                    <span class="mr-2">=</span>
                    <span>{{ formatAmount(forecastSubtotal, currentCurrency) }}</span>
                  </span>
                </template>
              </template>
              <template v-if="[ScheduleType.OneOff].includes(props.formData.schedule_type as ScheduleType)">
                <span>{{ formatAmount(forecastSubtotal, currentCurrency) }}</span>
              </template>
            </div>
          </div>

          <div class="flex">
            <div class="preview-card-label">
              GST:
            </div>
            <div class="preview-card-content">
              {{ formatAmount(forecastGst, currentCurrency) }} <template v-if="!forecastGst.eq(0)">
                <div class="preview-card-content-divider" />
                {{ props.gst.fee_rate === SurchargeRate.PERCENTAGE ? `${props.gst.fee_value}%` : props.gst.fee_value }}
              </template>
            </div>
          </div>

          <div class="flex">
            <div class="preview-card-label">
              Surcharge:
            </div>
            <div class="preview-card-content">
              {{ formatAmount(forecastSurcharge, currentCurrency) }} <div class="preview-card-content-divider" /> {{ formatSurcharge() }}
            </div>
          </div>

          <div class="flex">
            <div class="preview-card-label">
              Total:
            </div>
            <div class="preview-card-content">
              <span class="font-bold primary-color">{{ formatAmount(forecastTotal, currentCurrency) }}</span>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.plan-subscription-preview {

  .plan-subscription-title {
    color: var(--colors-primary);
    font-size: 26px;
    font-weight: bold;
    padding-left: 1.75rem;
  }

  .preview-content {
    border: 1px solid var(--color-gray-500);
    border-radius: 8px;
  }

  .preview-content {
    background-color: var(--color-white);
    padding: 1.5rem 0;
    border-radius: 0.5rem;
  }

  .preview-card-divider {
    background-color: var(--colors-dark-gray);
    height: 2px;
    width: 93%;
    margin: 1.5rem auto;
  }

  :deep(.p-inputnumber) {
    .p-inputnumber-input {
      height: 2.25rem;
      font-size: 1rem;
    }

    .p-button.p-button-icon-only {
      width: 2rem;
      height: 1.125rem;
      padding: 0;
    }
  }

  :deep(.p-checkbox) {
    .p-checkbox-box {
      border-color: #64748b;
    }
  }

  .preview-card-label {
    padding: 0.5rem 1.75rem;
    flex: 2;
    font-size: 16px;
    font-weight: bold;
    color: var(--color-gray-500);
  }

  .preview-card-content {
    padding: 0.5rem 1.75rem;
    flex: 4;
    font-size: 16px;
    color: var(--color-gray-500);
    max-width: 1200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    .preview-card-content-divider {
      display: inline-block;
      height: 100%;
      width: 18%;
      margin: auto 0.5rem;
      position: relative;
      &::before {
        position: absolute;
        content: '';
        display: block;
        height: 2px;
        width: 100%;
        background-color: var(--color-gray-500);
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }

  :deep(.forecast-input) {
    max-width: 4rem;
    width: 4rem;

    .p-inputtext {
      max-width: 4rem;
      width: 4rem;
    }
  }

  .forecast-header {
    padding: 0.5rem 1.75rem 0;
  }

  .forecast-title {
    font-size: 18px;
    font-weight: bold;
    color: var(--colors-primary);
    margin-bottom: 0.5rem;
  }

  .forecast-disclaimer {
    font-size: 12px;
    color: var(--color-gray-500);
  }

  .support-divide {
    height: 2px;
    width: 93%;
    margin: 1.5rem auto;
  }
}
</style>
