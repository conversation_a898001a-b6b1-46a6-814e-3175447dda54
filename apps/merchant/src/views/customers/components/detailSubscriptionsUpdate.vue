<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import dayjs from 'dayjs'
import { Field, Form as VeeForm } from 'vee-validate'
import { ref } from 'vue'
import { z } from 'zod'
import { ScheduleType, UnitBasedPricingModel } from '@/constants/plan'
import { plan as planApi } from '@/services/api'
import PlanSubscriptionForm from '../../planSubscription/components/PlanSubscriptionForm.vue'
import { usePlanForm } from '../../planSubscription/composables/usePlanForm'

const props = withDefaults(defineProps<{
  planId: string
  customerId: string
}>(), {
  planId: '',
  customerId: '',
})

const emits = defineEmits<{
  (e: 'update:success'): void
}>()

const {
  formData: planFormData,
  formErrors: planFormErrors,
  submitting: planSubmitting,
  submitForm: planSubmitForm,
  options: planOptions,
  optionsLoading: planOptionsLoading,
  validateForm: planValidateForm,
  setPlanId: planSetPlanId,
  setCustomerId: planSetCustomerId,
  loading: planIsLoading,
} = usePlanForm('customerView')

const isLoading = ref(false)

const planFormRef = ref<InstanceType<typeof VeeForm>>()

const planFormState = ref({
  planStartDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
  units: 1,
})

planSetCustomerId(props.customerId)
planSetPlanId(props.planId)

const schemaFields: Record<string, any> = toTypedSchema(z.object({
  planStartDate: z.date({
    required_error: 'Start date is required',
    invalid_type_error: 'Invalid start date',
  }),
  units: z.number()
    .optional()
    .superRefine((val, ctx) => {
      if (planFormData.schedule_type === ScheduleType.UnitBased && planFormData.unitBasedModel === UnitBasedPricingModel.TieredPricing) {
        if (!val || val <= 0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Unit quantity must be greater than 0',
          })
        }
      }
    }),
}))

const handleConfirmPlanInfo = async () => {
  const isValid: boolean = await planValidateForm()

  if (isValid) {
    try {
      isLoading.value = true
      const formData = await planSubmitForm()

      const { code } = await planApi.updateCustomerPlanInfo({
        ...formData as Api.PlanCreateReq,
        units: planFormState.value.units,
        start_date: dayjs(planFormState.value.planStartDate).format('YYYY-MM-DD'),
        customer_id: props.customerId,
        plan_id: props.planId,
      } as Api.UpdateCustomerPlanInfoReq)

      if (code === 0) {
        window.$toast.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Subscription updated successfully',
        })
        emits('update:success')
      }
    }
    catch (error) {
      console.error(error)
    }
    finally {
      isLoading.value = false
    }
  }
}
</script>

<template>
  <div>
    <div v-if="planIsLoading" class="flex justify-center items-center">
      <ProgressSpinner />
    </div>
    <VeeForm
      v-else ref="planFormRef" :validation-schema="schemaFields" :initial-values="{
        planStartDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
      }" class="create-invite-base-form space-y-4 min-h-150 flex-1" @submit="handleConfirmPlanInfo"
    >
      <PlanSubscriptionForm
        :form-data="planFormData" :form-errors="planFormErrors" :options="planOptions"
        :options-loading="planOptionsLoading" :submitting="planSubmitting" mode="view" :customer-id="props.customerId"
        @submit="planSubmitForm"
        @cancel="$router.back()"
      />
      <Field
        v-if="planFormData.schedule_type === ScheduleType.UnitBased
          && planFormData.unitBasedModel === UnitBasedPricingModel.TieredPricing
        " v-slot="{ errorMessage, handleChange }" v-model="planFormState.units" name="units" as="div" class="flex flex-col gap-2"
      >
        <label class="create-invite-base-form-label font-medium mb-2">
          Unit Quantity
        </label>
        <InputNumber
          :model-value="planFormState.units"
          :min="1" :max="9999" placeholder="Please enter unit quantity"
          class="w-full" :class="{ 'p-invalid': errorMessage }"
          @value-change="handleChange"
        />
        <Message v-if="errorMessage" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </Field>
      <Field v-slot="{ errorMessage, handleChange }" name="planStartDate" as="div" class="flex flex-col gap-2">
        <label class="create-invite-base-form-label font-medium mb-2">
          Start Date (Required)
        </label>
        <DatePicker
          v-model="planFormState.planStartDate"
          :min-date="new Date(Date.now() + 24 * 60 * 60 * 1000)" placeholder="Please select a start date"
          date-format="dd/mm/yy" class="w-full"
          :input-class="{ 'p-invalid': errorMessage }"
          @value-change="handleChange"
        />
        <Message v-if="errorMessage" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </Field>
      <div class="flex pt-6 justify-end">
        <Button type="submit" :loading="isLoading" label="SUBMIT" />
      </div>
    </VeeForm>
  </div>
</template>
