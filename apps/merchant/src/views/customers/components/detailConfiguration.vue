<script setup lang="ts">
import { ref } from 'vue'
import { useDict } from '@/composables/useDict'
import { customer as customerApi } from '@/services/api'

const props = defineProps<{
  list: Customer.CommunicationConfig[]
  customerId: string
}>()

const { getLabel: getNotificationType } = useDict('notification_type')

const columns = ref<TableColumnItem[]>([
  {
    field: 'type',
    header: 'Notification type',
    style: { width: '120px' },
    template: 'type',
  },
  {
    field: 'email',
    header: 'Email',
    style: { width: '80px' },
    template: 'email',
  },
])

const handleUpdateEmail = (data: Customer.CommunicationConfig) => {
  window.$confirm.require({
    header: 'Confirmation',
    message: 'Are you sure you want to update the email?',
    acceptLabel: 'Yes',
    rejectLabel: 'No',
    accept: () => {
      data.__is_loading = true
      customerApi.updateCommunicationConfig({
        customer_id: props.customerId,
        notification_type: data.type as number,
        status: data.email?.toString() || '0',
      }).then(() => {
        data.__is_loading = false
      }).catch(() => {
        data.email = !data.email ? 1 : 0
        data.__is_loading = false
      })
    },
    reject: () => {
      data.email = !data.email ? 1 : 0
    },
    onHide: () => {
      data.__is_loading = false
      data.email = !data.email ? 1 : 0
    },
  })
}
</script>

<template>
  <BaseDataTable
    :columns="columns"
    :value="props.list"
    :total-records="props.list.length"
    :paginator="false"
    :lazy="false"
    data-key="id"
    :show-search-bar="false"
    :show-multiple-column="false"
    table-style="min-width: 15rem"
  >
    <template #type="{ data }">
      {{ getNotificationType(data?.type) }}
    </template>
    <template #email="{ data }">
      <i v-if="data.__is_loading" :class="{ 'pi pi-spin pi-spinner': data.__is_loading }" />
      <Checkbox v-else v-model="data.email" :false-value="0" :true-value="1" binary @change="handleUpdateEmail(data)" />
    </template>
  </BaseDataTable>
</template>

<style scoped>

</style>
