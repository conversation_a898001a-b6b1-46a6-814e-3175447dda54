<script setup lang="ts">
import { useCheckoutStore } from '@/store/modules/checkout'
import { toTypedSchema } from '@vee-validate/zod'
import { Field, Form as VeeForm } from 'vee-validate'
import { ref } from 'vue'
import { z } from 'zod'

defineProps({
  readonly: {
    type: Boolean,
    default: false,
  },
})

const store = useCheckoutStore()
const formRef = ref()

const schema = toTypedSchema(z.object({
  companyName: z.string().optional(),
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  addressLine1: z.string().min(5, 'Address must be at least 5 characters'),
  addressLine2: z.string().optional(),
  city: z.string().min(2, 'City must be at least 2 characters'),
  state: z.string().min(2, 'State is required'),
  postcode: z.string().regex(/^\d{4}$/, 'Postcode must be 4 digits'),
  email: z.string().email('Please enter a valid email address'),
  bsbNumber: z.string().regex(/^\d{3}-\d{3}$/, 'BSB number must be in the format 123-456'),
  accountNumber: z.string().regex(/^\d{6,10}$/, 'Account number must be between 6-10 digits'),
  bankAccountName: z.string().min(2, 'Bank account name must be at least 2 characters'),
}))

// 暴露方法给父组件
const validate = async () => {
  const result = await formRef.value?.validate()
  return result
}

const resetForm = () => {
  formRef.value?.resetForm()
}

const submitForm = async () => {
  const result = await validate()
  if (result.valid) {
    return result.values
  }
  return false
}

const getCurrentValues = () => {
  return formRef.value?.values
}

defineExpose({
  validate,
  resetForm,
  submitForm,
  getCurrentValues,
})
</script>

<template>
  <VeeForm
    ref="formRef"
    :validation-schema="schema"
    :initial-values="store.bankAccountDetails"
    class="space-y-6"
  >
    <!-- Company Name (Optional) -->
    <Field v-slot="{ field, errorMessage }" v-model="store.bankAccountDetails.companyName" name="companyName">
      <div class="field mb-4">
        <label class="mb-2 block">Company Name (Optional)</label>
        <InputText v-bind="field" placeholder="Company name" class="w-full" :disabled="readonly" />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <div class="form-row mb-4">
      <!-- First Name -->
      <Field v-slot="{ field, errorMessage }" v-model="store.bankAccountDetails.firstName" as="div" name="firstName" class="form-col">
        <div class="field mb-4">
          <label class="mb-2 block">First Name</label>
          <InputText v-bind="field" placeholder="First name" class="w-full" :disabled="readonly" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>

      <!-- Last Name -->
      <Field v-slot="{ field, errorMessage }" v-model="store.bankAccountDetails.lastName" as="div" name="lastName" class="form-col">
        <div class="field mb-4">
          <label class="mb-2 block">Last Name</label>
          <InputText v-bind="field" placeholder="Last name" class="w-full" :disabled="readonly" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>
    </div>

    <!-- Address Line 1 -->
    <Field v-slot="{ field, errorMessage }" v-model="store.bankAccountDetails.addressLine1" name="addressLine1">
      <div class="field mb-4">
        <label class="mb-2 block">Address Line 1</label>
        <InputText v-bind="field" placeholder="Address line 1" class="w-full" :disabled="readonly" />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <!-- Address Line 2 (Optional) -->
    <Field v-slot="{ field, errorMessage }" v-model="store.bankAccountDetails.addressLine2" name="addressLine2">
      <div class="field mb-4">
        <label class="mb-2 block">Address Line 2 (Optional)</label>
        <InputText v-bind="field" placeholder="Address line 2" class="w-full" :disabled="readonly" />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <div class="form-row mb-4">
      <!-- City -->
      <Field v-slot="{ field, errorMessage }" v-model="store.bankAccountDetails.city" as="div" name="city" class="form-col">
        <div class="field mb-4">
          <label class="mb-2 block">City</label>
          <InputText v-bind="field" placeholder="City" class="w-full" :disabled="readonly" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>

      <!-- State -->
      <Field v-slot="{ field, errorMessage }" v-model="store.bankAccountDetails.state" as="div" name="state" class="form-col">
        <div class="field mb-4">
          <label class="mb-2 block">State</label>
          <InputText v-bind="field" placeholder="State" class="w-full" :disabled="readonly" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>
    </div>

    <div class="form-row mb-4">
      <!-- Postcode -->
      <Field v-slot="{ field, errorMessage }" v-model="store.bankAccountDetails.postcode" as="div" name="postcode" class="form-col">
        <div class="field mb-4">
          <label class="mb-2 block">Postcode</label>
          <InputText v-bind="field" placeholder="Postcode" class="w-full" :disabled="readonly" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>

      <!-- Email -->
      <Field v-slot="{ field, errorMessage }" v-model="store.bankAccountDetails.email" as="div" name="email" class="form-col">
        <div class="field mb-4">
          <label class="mb-2 block">Email Address</label>
          <InputText v-bind="field" placeholder="Email address" class="w-full" :disabled="readonly" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>
    </div>

    <!-- BSB Number -->
    <Field v-slot="{ field, errorMessage, handleChange }" v-model="store.bankAccountDetails.bsbNumber" name="bsbNumber">
      <div class="field mb-4">
        <label class="mb-2 block">BSB Number</label>
        <InputMask :model-value="field.value" placeholder="Enter BSB number (no dashes, 6 digits)" mask="999-999" class="w-full" :disabled="readonly" @update:model-value="handleChange" />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <!-- Account Number -->
    <Field v-slot="{ field, errorMessage }" v-model="store.bankAccountDetails.accountNumber" name="accountNumber">
      <div class="field mb-4">
        <label class="mb-2 block">Account Number</label>
        <InputText v-bind="field" placeholder="Enter bank account number" class="w-full" :disabled="readonly" />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <!-- Bank Account Name -->
    <Field v-slot="{ field, errorMessage }" v-model="store.bankAccountDetails.bankAccountName" name="bankAccountName">
      <div class="field mb-4">
        <label class="mb-2 block">Bank Account Name</label>
        <InputText v-bind="field" placeholder="Name of bank account" class="w-full" :disabled="readonly" />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>
  </VeeForm>
</template>

<style scoped>
.form-row {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.form-col {
  flex: 1;
  min-width: 250px; /* Ensures columns don't get too narrow */
}

.w-full {
  width: 100%;
}

/* Responsive styles */
@media screen and (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
    margin-bottom: 0;
  }

  .form-col {
    min-width: 100%;
  }
}

/* Small mobile devices */
@media screen and (max-width: 480px) {
  :deep(.p-float-label) {
    font-size: 0.9rem;
  }

  :deep(.p-inputtext) {
    font-size: 0.9rem;
    padding: 0.5rem;
  }
}
</style>
