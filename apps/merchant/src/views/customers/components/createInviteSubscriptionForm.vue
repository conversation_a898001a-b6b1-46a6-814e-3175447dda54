<script setup lang="ts">
import type { DictItem } from '@/services/api/dict'
import { toTypedSchema } from '@vee-validate/yup'
import dayjs from 'dayjs'
import { Field, Form as VeeForm } from 'vee-validate'
import { computed, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import * as yup from 'yup'
import { ScheduleType, UnitBasedPricingModel } from '@/constants/plan'
import { plan as planApi } from '@/services/api'
import PlanSubscriptionForm from '../../planSubscription/components/PlanSubscriptionForm.vue'
import { usePlanForm } from '../../planSubscription/composables/usePlanForm'
import SubscriptionFormPreview from './subscriptionFormPreview.vue'

const props = defineProps({
  formDataState: {
    type: Object,
    required: true,
  },
  customerId: {
    type: String,
  },
  surchargeRate: {
    type: Object,
    default() {
      return {
        fee_rate: '',
        fee_value: '',
      }
    },
  },
  gst: {
    type: Object,
    default() {
      return {
        fee_rate: '',
        fee_value: '',
      }
    },
  },
  isSubmitLoading: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['next'])

const model = reactive({
  plan_id: '',
  planStartDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
})

const formRef = ref()

const planSubscriptionFormRef = ref<InstanceType<typeof PlanSubscriptionForm>>()

const route = useRoute()
const planOptions = ref<DictItem[]>([])
const planOriginList = ref<Record<string, Plan.Info>>({})

// 计算当前选择的计划名称
const selectedPlanName = computed(() => {
  if (!model.plan_id || !planOriginList.value[model.plan_id]) {
    return ''
  }
  return planOriginList.value[model.plan_id].plan_name || ''
})

const {
  formData,
  formErrors,
  submitting,
  submitForm,
  validateForm,
  options,
  optionsLoading,
  setPlanId,
  loading: planLoading,
} = usePlanForm('view')

// 添加表单验证 schema
const schema = toTypedSchema(yup.object({
  planStartDate: yup.date(),
  plan_id: yup.string().when([], {
    is: () => props.customerId,
    then: schema => schema.required('Subscription Plan Template is required'),
    otherwise: schema => schema.optional(),
  }),
  units: yup.number()
    .optional()
    .test('units-validation', 'Unit quantity must be greater than 0', (val) => {
      if (formData.schedule_type === ScheduleType.UnitBased && formData.unitBasedModel === UnitBasedPricingModel.TieredPricing) {
        if (!val || val <= 0) {
          return false
        }
      }
      return true
    }),
  // 根据是否有 customerId 来决定验证规则
  name: yup.string().optional(),
  email_primary: yup.string().when([], {
    is: () => !props.customerId,
    then: schema => schema
      .required('Email is required')
      .email('Invalid email format')
      .matches(
        /^[\w.%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i,
        'Please enter a valid email address with a proper domain',
      ),
    otherwise: schema => schema.optional(),
  }),
  phone_mobile: yup.string().optional().test('phone-validation', 'Phone number must be 6-12 digits', (val) => {
    if (!val || val.trim() === '') {
      return true // 空值通过校验
    }
    return /^\d{6,12}$/.test(val)
  }),
}))

const handlePlanChange = (planId: string) => {
  setPlanId(planId)
}

Promise.all([
  planApi.getList({
    page_size: 999,
    status: 1,
  }, {
    headers: {
      'Business-Id': route.query?.business_id as string,
    },
  }).then((res) => {
    planOptions.value = res.data.data?.map((item: Plan.Info) => {
      const plan_id: string = item.plan_id || ''
      planOriginList.value[plan_id] = item
      return {
        label: item.plan_name || '',
        value: plan_id,
      }
    })
  }),
])

defineExpose({
  validate: async () => {
    const { valid, errors } = await formRef.value?.validate()
    console.log('errors', errors)
    if (!props.customerId && !model.plan_id && valid) {
      return true
    }
    if (valid) {
      const isValid = await validateForm()
      return isValid
    }
    return false
  },
  getFormData: async () => {
    if (!props.customerId && !model.plan_id) {
      return {}
    }
    return {
      ...await submitForm(),
      start_date: dayjs(model.planStartDate).isValid() ? dayjs(model.planStartDate).format('YYYY-MM-DD') : '',
    }
  },
})
</script>

<template>
  <div class="flex gap-x-12 form-wrapper flex-col lg:flex-row">
    <VeeForm
      ref="formRef" :validation-schema="schema" :initial-values="{}"
      class="create-invite-base-form space-y-4 min-h-150 flex-1"
    >
      <template v-if="!customerId">
        <div class="flex flex-col gap-4 lg:flex-row">
          <Field
            v-slot="{ field }" v-model="formDataState.customer_name" name="customer_name" as="div"
            class="flex flex-col gap-2 w-full lg:w-1/2"
          >
            <label class="create-invite-base-form-label mb-2">
              Customer Name
            </label>
            <InputText
              v-bind="field" aria-describedby="name-help" placeholder="Please enter customer name"
              maxlength="100"
            />
          </Field>
          <Field
            v-slot="{ field, errorMessage }" v-model="formDataState.email_primary" name="email_primary" as="div"
            class="flex flex-col gap-2 w-full lg:w-1/2"
          >
            <label class="create-invite-base-form-label mb-2">
              Customer Email (Required)
            </label>
            <InputText
              v-bind="field" aria-describedby="name-help" :class="{ 'p-invalid': errorMessage }"
              placeholder="Please enter customer email"
            />
            <Message v-if="errorMessage" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>
        </div>
        <Field
          v-slot="{ field, errorMessage }" v-model="formDataState.phone_mobile" name="phone_mobile" as="div"
          class="flex flex-col gap-2"
        >
          <label class="create-invite-base-form-label mb-2">
            Phone Number
          </label>
          <InputText
            v-bind="field" aria-describedby="name-help" :class="{ 'p-invalid': errorMessage }"
            placeholder="Please enter phone number"
          />
          <Message v-if="errorMessage" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>
      </template>

      <Field v-slot="{ errorMessage, handleChange }" name="plan_id" as="div" class="flex flex-col gap-2">
        <div class="flex justify-between items-center mb-2">
          <label class="create-invite-base-form-label ">
            Subscription Plan Template ( {{ !props.customerId ? 'Required' : 'Optional' }})
          </label>
          <Button severity="secondary" icon="pi pi-plus" @click="$router.push({ name: 'planSubscriptionAdd' })" />
        </div>
        <Select
          v-model="model.plan_id" :options="planOptions" option-label="label" option-value="value"
          :class="{ 'p-invalid': errorMessage }" placeholder="Please select a plan" checkmark filter
          :highlight-on-select="false" @update:model-value="(e: string) => {
            handlePlanChange(e)
            handleChange(e)
          }"
        />
        <Message v-if="errorMessage" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </Field>

      <template v-if="model.plan_id">
        <Transition name="fade" mode="out-in">
          <!-- 加载中 -->
          <div v-if="planLoading" class="flex justify-center items-center h-64">
            <ProgressSpinner />
          </div>
          <PlanSubscriptionForm
            v-else ref="planSubscriptionFormRef" class="plan-subscription-form"
            :form-data="formData" :form-errors="formErrors" :options="options" :options-loading="optionsLoading"
            :submitting="submitting" mode="view" @submit="submitForm" @cancel="$router.back()"
          />
        </Transition>
      </template>

      <Field
        v-if="formData.schedule_type === ScheduleType.UnitBased" v-slot="{ errorMessage, handleChange }"
        v-model="formDataState.units" name="units" as="div" class="flex flex-col gap-2"
      >
        <label class="create-invite-base-form-label mb-2">
          Unit Quantity
        </label>
        <InputNumber
          :model-value="formDataState.units" :min="1" :max="9999" placeholder="Please enter unit quantity"
          class="w-full" :class="{ 'p-invalid': errorMessage }" @update:model-value="handleChange"
        />
        <Message v-if="errorMessage" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </Field>
      <Field
        v-slot="{ field, errorMessage, handleChange }" v-model="model.planStartDate" name="planStartDate" as="div"
        class="flex flex-col gap-2"
      >
        <label class="create-invite-base-form-label mb-2">
          Start Date
        </label>
        <DatePicker
          :model-value="field.value" :min-date="new Date(Date.now() + 24 * 60 * 60 * 1000)"
          placeholder="Please select a start date" class="w-full" date-format="dd/mm/yy" @update:model-value="handleChange"
        />
        <Message v-if="errorMessage" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </Field>
    </VeeForm>

    <div class="flex-1">
      <SubscriptionFormPreview
        :form-data="formData" :name="formDataState.customer_name || ''"
        :plan-name="selectedPlanName || ''" :start-date="model.planStartDate || new Date()"
        :unit-quantity="formDataState.units || 1" :surcharge-rate="props.surchargeRate"
        :gst="props.gst"
        :is-show-forecast="true"
      />
      <div class="flex justify-end mt-4">
        <Button class="submit-button" label="NEXT" severity="warn" :loading="props.isSubmitLoading" @click="emits('next')" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/primevue.scss';

:deep(.plan-subscription-form) {
  label {
    font-weight: 500;
  }
}

.create-invite-base-form-label {
  font-size: 16px;
  color: #181349;
  font-weight: 600;
}

.create-invite-base-form {
  background-color: #09deff;
  padding: 32px 24px;
  border-radius: 16px;
}

.plan-subscription-form {
  background-color: white;
  padding: 24px;
  border-radius: 16px;
}

.submit-button {
  width: 250px;
  height: 52px;
  border-radius: 8px;
  :deep(.p-button-label) {
    font-weight: 600;
    font-size: 16px;
  }
}
</style>
