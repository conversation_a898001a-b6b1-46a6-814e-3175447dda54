<script setup lang="ts">
import type { DictItem } from '@/services/api/dict'
import { toTypedSchema } from '@vee-validate/zod'
import dayjs from 'dayjs'
import { Field, Form as VeeForm } from 'vee-validate'
import { computed, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { z } from 'zod'
import BaseDataTableActions from '@/components/common/BaseDataTableActions.vue'
import { useDict } from '@/composables/useDict'
import { useListRefresh } from '@/composables/useListRefresh'
import { usePermissions } from '@/composables/usePermissions'
import { Permissions } from '@/constants/permissions'
import { customer as customerApi, plan as planApi } from '@/services/api'
import { getDictByType } from '@/services/api/dict'
import { useUserStore } from '@/store/modules/user'
import { useCopy } from '@/utils/clipboard'
import { formatDate } from '@/utils/date'
import { formatFrequencyTime } from '@/utils/format'
import { getCustomerPlanTagStatus } from '@/utils/tagStatus'
import detailConfiguration from './components/detailConfiguration.vue'
import detailHistory from './components/detailHistory.vue'
import detailPayment from './components/detailPayment.vue'
import detailSubscriptionsUpdate from './components/detailSubscriptionsUpdate.vue'

const { backWithRefresh } = useListRefresh('customersList', () => { })

const { hasPermission } = usePermissions()

const { t } = useI18n()

const copy = useCopy()

const userStore = useUserStore()

const route = useRoute()

const router = useRouter()

const detailPaymentRef = ref<InstanceType<typeof detailPayment>>()

// 添加loading状态
const loading = ref(true)
// 添加错误状态
const error = ref<string | null>(null)

// 添加支付表单验证schema
const paymentSchema = toTypedSchema(z.object({
  amount: z.number({
    required_error: t('customersPage.payments.enterAmount'),
  }).min(0.01, t('customersPage.payments.amountMustBePositive')),
  description: z.string(),
  date: z.date({
    required_error: t('customersPage.payments.selectDate'),
  }),
}))

// 添加客户编辑表单验证schema
const customerEditSchema = toTypedSchema(z.object({
  name: z.string().min(1, t('validation.required')),
  email_primary: z.string().email(t('validation.emailInvalid')),
  phone_mobile: z.string().min(1, t('validation.required')),
}))

const { getLabel: getPlanStatusLabel } = useDict('subscription_status')

const paymentFormRef = ref()
const customerEditFormRef = ref<any>(null)

const data = ref<Customer.Info | null>(null)

// 添加支付弹窗状态
const paymentDialogVisible = ref(false)
const paymentDialogConfirmLoading = ref(false)

// 添加客户编辑弹窗状态
const customerEditDialogVisible = ref(false)
const customerEditDialogConfirmLoading = ref(false)
const customerUpdateDialogVisible = ref(false)

const isShowCreateInvoice = ref(false)

// 添加支付表单数据
const paymentForm = reactive({
  amount: 0,
  description: '',
  date: new Date(),
})

// 添加客户编辑表单数据
const customerEditForm = reactive({
  name: '',
  email_primary: '',
  phone_mobile: '',
})

const editPlanInfoRow = reactive<{
  planId: string
  customerId: string
}>({
  planId: '',
  customerId: '',
})

const planProcessDict = ref<DictItem[]>([])

const handleCancelSubscription = (rowData: Plan.Info) => {
  if (!hasPermission(Permissions.SUBSCRIPTION_CANCEL)) {
    return
  }

  window.$confirm.require({
    message: `
            Are you sure you want to cancel?
            Last Payment Date: ${rowData?.last_payment_date || ''}
            `,
    header: 'WARNING: This can’t be undone.',
    icon: 'pi pi-exclamation-triangle',
    rejectProps: {
      label: 'NO, KEEP SUBSCRIPTION',
      severity: 'secondary',
      outlined: true,
    },
    acceptProps: {
      label: 'YES, CANCEL',
    },
    accept: async () => {
      const { code } = await customerApi.cancelCustomerPlanSubscription(rowData.plan_id as string, data.value?.customer_id as string)
      if (code === 0) {
        window.$toast.add({
          severity: 'success',
          summary: t('common.success'),
        })
        await getDetails()
      }
    },
    reject: () => {

    },
  })
}

const planId = ref('')

const handleUpdate = async (rowData: Plan.Info) => {
  if (!hasPermission(Permissions.SUBSCRIPTION_UPDATE)) {
    return
  }

  planId.value = rowData.plan_id as string
  editPlanInfoRow.customerId = route.params.id as string
  editPlanInfoRow.planId = planId.value
  customerUpdateDialogVisible.value = true
}

// 添加创建支付弹窗函数
// const handleCreatePayment = () => {
//   paymentForm.amount = 0
//   paymentForm.description = ''
//   paymentForm.date = new Date()
//   paymentDialogVisible.value = true
// }

// 添加确认创建支付函数
const handleConfirmPayment = async () => {
  if (!hasPermission(Permissions.TRANS_CREATE)) {
    return
  }

  const result = await paymentFormRef.value?.validate()
  if (!result.valid) { return }

  try {
    paymentDialogConfirmLoading.value = true
    const { code } = await customerApi.createPayment({
      customer_id: route.params.id as string,
      amount: paymentForm.amount,
      description: paymentForm.description,
      date: dayjs(paymentForm.date).format('YYYY-MM-DD'),
    })

    if (code === 0) {
      window.$toast.add({
        severity: 'success',
        summary: t('common.success'),
        detail: t('customersPage.payments.paymentCreated'),
        life: 3000,
      })
      paymentDialogVisible.value = false
      detailPaymentRef.value?.search()
      backWithRefresh()
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    paymentDialogConfirmLoading.value = false
  }
}

// 添加编辑客户信息弹窗函数
const handleEditCustomer = () => {
  if (!hasPermission(Permissions.CUSTOMER_UPDATE)) {
    return
  }

  userStore.activeBid = data.value?.business_id as string
  // 初始化表单数据
  customerEditForm.name = data.value?.name || ''
  customerEditForm.email_primary = data.value?.email_primary || ''
  customerEditForm.phone_mobile = data.value?.phone_mobile || ''
  customerEditDialogVisible.value = true
}

// 添加确认编辑客户信息函数
const handleConfirmEditCustomer = async () => {
  if (!hasPermission(Permissions.CUSTOMER_UPDATE)) {
    return
  }

  const result = await customerEditFormRef.value?.validate()
  if (!result.valid) { return }

  try {
    customerEditDialogConfirmLoading.value = true
    // 调用更新客户信息的API
    const { code } = await customerApi.updateCustomer({
      ...data.value,
      customer_id: route.params.id as string,
      customer_name: customerEditForm.name,
      email_primary: customerEditForm.email_primary,
      phone_mobile: customerEditForm.phone_mobile,
    })

    if (code === 0) {
      window.$toast.add({
        severity: 'success',
        summary: t('common.success'),
        detail: t('common.saveSuccess') || '保存成功',
      })
      customerEditDialogVisible.value = false
      // 刷新客户详情
      await getDetails()
    }
  }
  catch (error) {
    console.error('Failed to update customer:', error)
    window.$toast.add({
      severity: 'error',
      summary: t('common.error'),
      detail: 'Failed to update customer',
      life: 3000,
    })
  }
  finally {
    customerEditDialogConfirmLoading.value = false
  }
}

const getDetails = async () => {
  try {
    const { code, data: resData } = await customerApi.getCustomerDetail(String(route.params?.id))
    if (code === 0) {
      data.value = resData
    }
  }
  catch (error) {
    console.error('获取客户详情失败:', error)
  }
}

const handleUpdatePayMethod = (rowData: Plan.Info) => {
  if (!hasPermission(Permissions.SUBSCRIPTION_MOD_PAYMENT_METHOD)) {
    return
  }

  window.$confirm.require({
    message: 'Are you sure you want to update the payment method?',
    header: 'WARNING: This can’t be undone.',
    icon: 'pi pi-exclamation-triangle',
    acceptProps: {
      label: 'YES, UPDATE',
    },
    accept: async () => {
      try {
        const { code } = await planApi.updatePayMethod({
          plan_id: rowData.plan_id as string,
          customer_id: rowData.customer_id as string,
        })
        if (code === 0) {
          window.$toast.add({
            severity: 'success',
            summary: t('common.success'),
            detail: t('customersPage.payments.paymentUpdated'),
          })
        }
      }
      catch (error) {
        console.error('Failed to update payment method:', error)
        window.$toast.add({
          severity: 'error',
          summary: t('common.error'),
          detail: 'Failed to update payment method',
        })
      }
    },
  })
}

// 加载数据
const loadData = async () => {
  loading.value = true
  error.value = null
  try {
    await Promise.all([
      getDetails(),
      getDictByType('plan_process_type').then((res) => {
        if (res.code === 0) {
          planProcessDict.value = res.data
        }
      }),
    ])
  }
  catch {
    error.value = t('common.requestFailed')
    // 显示错误提示
    window.$toast?.add({
      severity: 'error',
      summary: t('common.error'),
      detail: t('common.requestFailedDescription'),
      life: 5000,
    })
  }
  finally {
    loading.value = false
  }
}

// 初始化加载数据
loadData()

const isNewCustomer = computed(() => {
  const createdAt = dayjs(data.value?.created_at)
  const today = dayjs()
  return today.diff(createdAt, 'day') <= 7
})

const handleCreateSubscription = () => {
  userStore.activeBid = data.value?.business_id as string
  router.push({
    path: '/customers/createInvite',
    query: {
      customer_id: route.params.id as string,
    },
  })
}

const handleCreateInvoice = () => {
  userStore.activeBid = data.value?.business_id as string
  router.push({
    path: `/customers/detail/${route.params.id}/createInvoice`,
    query: {
      name: data.value?.name,
      phone: data.value?.phone_mobile,
      email: data.value?.email_primary,
      address: data.value?.address,
      customer_id: route.params.id as string,
    },
  })
}
</script>

<template>
  <div v-if="route.name === 'customersDetail'" class="customer-detail rounded-lg">
    <!-- Loading 效果 -->
    <div v-if="loading" class="flex justify-center items-center py-20">
      <div class="flex flex-col items-center">
        <ProgressSpinner style="width: 50px; height: 50px" stroke-width="4" />
        <span class="mt-4 text-gray-500">{{ t('common.loading') }}</span>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-else-if="error" class="flex justify-center items-center py-20">
      <div class="flex flex-col items-center">
        <i class="pi pi-exclamation-triangle text-4xl text-red-500 mb-4" />
        <span class="text-red-500 font-medium mb-2">{{ error }}</span>
        <span class="text-gray-500 mb-4">{{ t('common.requestFailedDescription') }}</span>
        <Button icon="pi pi-refresh" :label="t('common.refresh')" @click="loadData" />
      </div>
    </div>

    <!-- 内容区域 -->
    <div v-else-if="data" class="customer-content flex justify-between items-start">
      <div class="customer-details bg-[#09deff] text-[#181349] rounded-lg px-6 py-6 w-1/4 mr-6">
        <div class="flex justify-end items-center">
          <Button variant="outlined" icon="pi pi-pen-to-square" label="Edit details" @click="handleEditCustomer" />
        </div>
        <div class="flex justify-between items-center mb-4 detail-left-title">
          <div class="text-[28px] font-semibold my-2">
            {{ data.name }}
          </div>
        </div>
        <div class="customer-details">
          <div class="details-edit">
            <div class="font-semibold mb-2">
              Customer ID
            </div>
            <div class="flex items-center gap-2 px-2 py-2 detail-left-item cursor-pointer min-h-8">
              <span @click="data?.customer_id && copy(data.customer_id)">{{ data?.customer_id }}</span>
            </div>
          </div>
          <div class="details-edit">
            <div class="font-semibold mb-2">
              Customer since
            </div>
            <div class="flex items-center gap-2 px-2 py-2 cursor-pointer detail-left-item min-h-8" :title="t('common.copy')">
              <span>{{ data?.created_at }}</span>
            </div>
          </div>
          <!-- <div class="details-edit">
            <div class="font-semibold mb-2">
              Description
            </div>
            <div class="flex items-center gap-2 px-2 py-2 cursor-pointer detail-left-item min-h-8" :title="t('common.copy')">
              <span>{{ data?.name }}</span>
            </div>
          </div> -->
          <div class="details-edit">
            <div class="font-semibold mb-2">
              Phone Number
            </div>
            <div class="flex items-center gap-2 px-2 py-2 cursor-pointer detail-left-item min-h-8" :title="t('common.copy')">
              <span>{{ data.phone_mobile }}</span>
            </div>
          </div>
          <div class="details-edit">
            <div class="font-semibold mb-2">
              Billing Email
            </div>
            <div class="flex items-center gap-2 px-2 py-2 cursor-pointer detail-left-item min-h-8" :title="t('common.copy')">
              <span>{{ data.email_primary }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="customer-content  rounded-lg w-3/4">
        <div class="flex justify-between items-center">
          <div class="tags bg-white rounded-lg w-2/3 px-6 py-4">
            Tags:
            <Tag v-if="isNewCustomer" severity="success" value="New Customer" />
          </div>
          <!-- <div class="btn flex pr-4">
            <div>
              <i style="font-size: 20px;" class="pi pi-trash" />
            </div>
          </div> -->
        </div>
        <div class="text-[28px] font-bold my-4 flex justify-between items-center">
          <span>Subscriptions</span>
          <div class="flex items-center gap-2">
            <Button round label="Create Invoice" severity="warn" @click="isShowCreateInvoice = true" />
            <Button icon="pi pi-plus" round @click="handleCreateSubscription" />
          </div>
        </div>
        <div class="customer-content-left bg-white rounded-lg p-4">
          <div class="customer-subscriptions mb-8">
            <div class="customer-subscriptions-table">
              <DataTable :value="data.customer_plan">
                <Column header="Plan">
                  <template #body="{ data: tableData }">
                    <span class="mr-4">{{ tableData.plan_name }}</span>
                  </template>
                </Column>
                <Column header="Status">
                  <template #body="{ data: tableData }">
                    <BaseTag :text="getPlanStatusLabel(tableData.status)" :type="getCustomerPlanTagStatus(tableData.status)" />
                  </template>
                </Column>
                <Column field="frequency" header="Frequency">
                  <template #body="{ data: tableData }">
                    <span class="mr-2">{{ formatFrequencyTime(tableData) }}</span>
                  </template>
                </Column>
                <Column header="Next payment date">
                  <template #body="{ data: tableData }">
                    <span class="mr-2">
                      {{ formatDate(tableData.next_process_date, 'MMM DD') }}
                      <template v-if="tableData?.amount">
                        For
                        $ {{ tableData.amount.amount }} {{ tableData.amount.currency }}
                      </template>
                    </span>
                  </template>
                </Column>
                <Column header="Pay method">
                  <template #body="{ data: tableData }">
                    <div v-if="tableData.account_no" class="flex items-center">
                      <BaseCardType
                        :card-type="tableData?.credit_brand"
                        :text="tableData?.account_no"
                        :is-show-card-number="true"
                      />
                    </div>
                  </template>
                </Column>
                <Column class="w-64 !text-end" style="width: 50px;">
                  <template #header>
                    <div class="flex items-center justify-center flex-1">
                      <i class="pi pi-cog !text-xl" />
                    </div>
                  </template>
                  <template #body="{ data: tableData }">
                    <div class="flex justify-end gap-2">
                      <BaseDataTableActions>
                        <div class="flex gap-2">
                          <Button label="Cancel" severity="danger" @click="handleCancelSubscription(tableData)" />
                          <Button
                            label="Update Pay method" severity="danger"
                            @click="handleUpdatePayMethod(tableData)"
                          />
                          <Button label="Update" severity="info" @click="handleUpdate(tableData)" />
                        </div>
                      </BaseDataTableActions>
                    </div>
                  </template>
                </Column>

                <template #empty>
                  <div class="text-center text-gray-500">
                    No plan
                  </div>
                </template>
              </DataTable>
            </div>
          </div>
          <div class="customer-payments mb-8">
            <div class="customer-payments-title flex justify-between items-center">
              <div class="text-[28px] font-bold my-4">
                Transactions
              </div>
            </div>
            <Divider />
            <div class="customer-payments-table relative">
              <!-- <div class="transaction-add">
                <Button icon="pi pi-plus" round variant="text" @click="handleCreatePayment" />
              </div> -->
              <detailPayment :id="(route.params.id as string)" ref="detailPaymentRef" />
            </div>
          </div>
          <div class="mb-8">
            <div class=" flex justify-between items-center">
              <div class="text-[28px] font-bold my-4">
                Communication
              </div>
            </div>
            <Divider />
            <div class="flex justify-center items-start px-4">
              <div class="configuration w-1/2 mr-6">
                <div class="text-[22px] font-bold my-4">
                  Configuration
                </div>
                <detailConfiguration :customer-id="(route.params.id as string)" :list="(data.communication_config as Customer.CommunicationConfig[])" />
              </div>
              <div class="history w-1/2">
                <div class="text-[22px] font-bold my-4">
                  History
                </div>
                <detailHistory :customer-id="(route.params.id as string)" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据为空时显示 -->
    <div v-else class="flex justify-center items-center py-20">
      <div class="flex flex-col items-center">
        <i class="pi pi-info-circle text-4xl text-gray-400 mb-4" />
        <span class="text-gray-500">{{ t('common.noDataFound') }}</span>
        <Button icon="pi pi-refresh" :label="t('common.refresh')" class="mt-4" @click="loadData" />
      </div>
    </div>

    <!-- Payment Dialog -->
    <Dialog
      v-model:visible="paymentDialogVisible" modal :header="t('customersPage.payments.createPayment')"
      :style="{ width: '550px' }"
    >
      <VeeForm ref="paymentFormRef" class="space-y-4" :validation-schema="paymentSchema">
        <Field v-slot="{ errorMessage }" v-model="paymentForm.amount" name="amount" as="div" class="flex flex-col">
          <label class="mb-2 block">{{ t('customersPage.payments.amount') }}</label>
          <InputNumber
            v-model="paymentForm.amount" :min="0.01" mode="currency" currency="USD" locale="en-US"
            class="w-full"
          />
          <Message v-if="errorMessage" severity="error" class="mt-2 w-full" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>

        <Field
          v-slot="{ errorMessage }" v-model="paymentForm.description" name="description" as="div"
          class="flex flex-col"
        >
          <label class="mb-2 block">{{ t('customersPage.payments.description') }}</label>
          <Textarea
            v-model="paymentForm.description" rows="3" class="w-full"
            :placeholder="t('customersPage.payments.enterDescription')"
          />
          <Message v-if="errorMessage" severity="error" class="mt-2" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>

        <Field v-slot="{ errorMessage }" v-model="paymentForm.date" name="date" as="div" class="flex flex-col">
          <label class="mb-2 block">{{ t('customersPage.payments.date') }}</label>
          <DatePicker v-model="paymentForm.date" class="w-full" />
          <Message v-if="errorMessage" severity="error" class="mt-2" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>
      </VeeForm>

      <template v-if="paymentFormRef" #footer>
        <div class="flex justify-end gap-2">
          <Button
            :label="t('customersPage.payments.cancelPayment')" severity="secondary"
            :loading="paymentDialogConfirmLoading" @click="paymentDialogVisible = false"
          />
          <Button
            :label="t('customersPage.payments.confirmPayment')" :loading="paymentDialogConfirmLoading"
            @click="handleConfirmPayment"
          />
        </div>
      </template>
    </Dialog>

    <!-- Customer Edit Dialog -->
    <Dialog
      v-model:visible="customerEditDialogVisible" :header="t('customersPage.dialogs.editCustomer')" :modal="true"
      class="p-fluid" :style="{ width: '550px' }"
    >
      <VeeForm ref="customerEditFormRef" :validation-schema="customerEditSchema" @submit="handleConfirmEditCustomer">
        <div class="grid gap-4">
          <Field
            v-slot="{ field, errorMessage }" v-model="customerEditForm.name" name="name" as="div"
            class="flex flex-col"
          >
            <label class="mb-2 block">{{ t('customersPage.form.customerName') }}</label>
            <InputText v-bind="field" :placeholder="t('customersPage.form.customerNamePlaceholder')" class="w-full" />
            <Message v-if="errorMessage" severity="error" class="mt-2" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>

          <Field
            v-slot="{ field, errorMessage }" v-model="customerEditForm.email_primary" name="email_primary" as="div"
            class="flex flex-col"
          >
            <label class="mb-2 block">Billing Email</label>
            <InputText v-bind="field" :placeholder="t('customersPage.form.emailPlaceholder')" class="w-full" />
            <Message v-if="errorMessage" severity="error" class="mt-2" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>

          <Field
            v-slot="{ field, errorMessage }" v-model="customerEditForm.phone_mobile" name="phone_mobile" as="div"
            class="flex flex-col"
          >
            <label class="mb-2 block">{{ t('customersPage.form.phoneNumber') }}</label>
            <InputText v-bind="field" :placeholder="t('customersPage.form.phoneNumberPlaceholder')" class="w-full" />
            <Message v-if="errorMessage" severity="error" class="mt-2" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>
        </div>
      </VeeForm>
      <template #footer>
        <Button :label="t('common.cancel')" icon="pi pi-times" text @click="customerEditDialogVisible = false" />
        <Button
          :label="t('common.save')" icon="pi pi-check" :loading="customerEditDialogConfirmLoading"
          @click="handleConfirmEditCustomer"
        />
      </template>
    </Dialog>

    <Dialog
      v-model:visible="customerUpdateDialogVisible" header="Update" :modal="true" class="p-fluid"
      :style="{ width: '850px' }"
    >
      <detailSubscriptionsUpdate
        :customer-id="editPlanInfoRow.customerId" :plan-id="editPlanInfoRow.planId"
        @update:success="() => {
          getDetails();
          customerUpdateDialogVisible = false
        }"
      />
    </Dialog>

    <Dialog
      v-model:visible="isShowCreateInvoice" :dismissable-mask="true" :modal="true"
      pt:root:class="!border-0 !bg-[#ffe3e8] !rounded-[20px]" pt:mask:class="backdrop-blur-sm"
      style="width: 500px;"
    >
      <template #container="{ closeCallback }">
        <div class="p-8 create-invoice-dialog">
          <div class="text-xl text-[#1B1548]">
            This invoice is through your PayMyInvoice service, as is used to receive once off payments from your customer. Would you like to proceed?
          </div>
          <div class="mt-6 flex justify-center gap-4">
            <Button label="CANCEL" severity="primary" class="btn-pop" @click="closeCallback" />
            <Button
              label="YES" severity="warn" class="btn-pop"
              @click="handleCreateInvoice"
            />
          </div>
        </div>
      </template>
    </Dialog>
  </div>
  <RouterView v-else />
</template>

<style scoped lang="scss">
.details-edit {
  margin-bottom: 15px;
  --p-tag-primary-background: #fff;
}

.detail-left-title {
  border-bottom: 1px solid white;
}

.detail-left-item {
  background-color: #ffffff;
  border-radius: 8px;
}

.transaction-add {
  position: absolute;
  z-index: 2;
  right: 0;
}

.create-invoice-dialog {
  font-weight: 600;
  .btn-pop {
    height: 50px;
    flex: 1;
    border-radius: 12px;
  }
}
</style>
