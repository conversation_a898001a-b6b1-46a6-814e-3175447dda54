<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { usePermissions } from '@/composables/usePermissions'
import { Permissions } from '@/constants/permissions'

import CreateInvoiceForm from '../payMyInvoice/components/createInvoiceForm.vue'

// import CreateInvoicePreview from '../payMyInvoice/components/createInvoicePreview.vue'
import { useCreateInvoice } from '../payMyInvoice/composables/useCreateInvoice'

const { hasPermission } = usePermissions()

const route = useRoute()

const { formData, handleSubmit, schema, options, config, loadings, updateCustomers } = useCreateInvoice('addCustomer')

const formWrapperRef = ref<InstanceType<typeof CreateInvoiceForm>>()

if (route.query?.name) {
  const query = route.query
  const names = String(query.name)?.split(' ')
  formData.value.firstName = names[0] || ''
  formData.value.lastName = names[1] || ''
  formData.value.email = query.email as string
  formData.value.phone = query.phone as string
  formData.value.address = query.address as string
  formData.value.selectCustomer = query.customer_id as string
}
</script>

<template>
  <div v-if="hasPermission(Permissions.TRANS_CREATE)" class="flex justify-between bg-white rounded-2xl p-4">
    <div
      class="w-full md:w-230"
    >
      <CreateInvoiceForm
        ref="formWrapperRef" :form-data="formData" :is-show-select-customer="!route.query?.name"
        :schema="schema" :options-loading="loadings" :options="options" :config="config" @submit="handleSubmit" @update-customer="updateCustomers"
      />
    </div>
  </div>
  <div v-else class="flex justify-center items-center h-64">
    <p class="text-gray-500">
      You don't have permission to create invoices.
    </p>
  </div>
</template>

<style scoped></style>
