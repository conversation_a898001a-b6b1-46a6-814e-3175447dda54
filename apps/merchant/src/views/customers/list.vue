<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import type { DictItem } from '@/services/api/dict'
import But<PERSON> from 'primevue/button'
import Dialog from 'primevue/dialog'
import Divider from 'primevue/divider'
import { computed, onActivated, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import BaseDataTable from '@/components/common/BaseDataTable.vue'
import BaseDataTableActions from '@/components/common/BaseDataTableActions.vue'
import BaseExportDialog from '@/components/common/BaseExportDialog.vue'
import BaseSearch from '@/components/common/BaseSearch.vue'
import { useDict } from '@/composables/useDict'
import { useExport } from '@/composables/useExport'
import { useListRefresh } from '@/composables/useListRefresh'
import { usePermissions } from '@/composables/usePermissions'
import { useRequestList } from '@/composables/useRequestList'
import { Permissions } from '@/constants/permissions'
import { SearchFieldType } from '@/constants/search'
import { customer as customerApi } from '@/services/api'
import { useUserStore } from '@/store/modules/user'
import { formatDate } from '@/utils/date'
import { addAllToDict } from '@/utils/dict'
import { getTransactionTagStatus } from '@/utils/tagStatus'

defineOptions({
  name: 'customersList',
})

const { hasPermission, hasAnyPermission } = usePermissions()

const { t } = useI18n()

const userStore = useUserStore()

const route = useRoute()
const router = useRouter()

// 列配置
const columns = ref<TableColumnItem[]>([
  { field: 'name', header: 'Customer Name', template: 'name', style: { minWidth: '160px' } },
  { field: 'email_primary', header: 'Email', style: { minWidth: '110px' } },
  { field: 'phone_mobile', header: 'Phone', style: { minWidth: '120px' } },
  { field: 'default_payment_method', header: 'Default Payment Method', template: 'default_payment_method', style: { minWidth: '200px' } },
  { field: 'created_at', header: 'Created', template: 'created_at', sortable: true, style: { minWidth: '200px' } },
  { field: 'total_spend', header: 'Total Spend', sortable: true, style: { minWidth: '160px' } },
  { field: 'payments', header: 'Payments', sortable: true, style: { minWidth: '160px' } },
  // { field: 'refunds', header: 'Refunds', sortable: true, style: { minWidth: '160px' } },
  // { field: 'dispute_losses', header: 'Dispute Losses', sortable: true, style: { minWidth: '200px' } },
  { field: 'last_payment', header: 'Last Payment', template: 'last_payment', sortable: true, style: { minWidth: '200px' } },
  // { field: 'status', header: t('customersPage.columns.status'), template: 'status' },
  { field: 'action', header: '', template: 'action', alignFrozen: 'right', frozen: true },
])

// 使用 useRequestList 处理客户列表数据
const {
  list,
  loading,
  total,
  refresh,
  search,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
  loading: isListLoading,
  setSearchParams,
} = useRequestList<Customer.Info[], Api.CustomerListReq>({
  requestFn: customerApi.getList,
  immediate: false,
})

// Setup export functionality
const { isExporting, handleExport } = useExport({
  exportFn: customerApi.exportCustomers,
  getParams: () => {
    return setSearchParams(searchModel.value)
  },
  onExportStart: () => {
    window.$toast.add({
      severity: 'info',
      summary: 'Export Started',
      detail: 'Preparing your export file...',
      life: 3000,
    })
  },
})

// 使用通用的列表刷新逻辑
useListRefresh('customersList', refresh)

const handleSort = (event: Record<string, any>) => {
  const { sortField, sortOrder } = event
  setSearchParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  search()
}

const searchModel = ref<Partial<Api.CustomerListReq>>({
  'keyword': '',
  'category': null,
  'status': null,
  'created_at[]': [],
})

const tableSelection = ref([])

const customerTable = ref()

const categoryOptions = ref<DictItem[]>([])

const statusOptions = ref<DictItem[]>([])

// Get customer status and type options
const { getLabel: getCustomerStatusLabel, loading: isCustomerStatusLoading } = useDict('customer_status', (res) => {
  statusOptions.value = addAllToDict(res, { label: 'All', value: null })
})

const { loading: isCustomerTypeLoading } = useDict('customer_type', (res) => {
  categoryOptions.value = addAllToDict(res, { label: 'All Customers', value: null })
})

// 配置搜索字段
const searchFields = computed(() => [
  {
    name: 'keyword',
    label: 'What are you looking for?',
    type: SearchFieldType.TEXT,
    placeholder: 'Search for Customer name, email, etc.',
    maxlength: 50,
    defaultValue: '',
  },
  {
    name: 'category',
    label: 'Category',
    type: SearchFieldType.SELECT,
    placeholder: 'All',
    options: categoryOptions.value,
    loading: isCustomerTypeLoading,
    defaultValue: '',
  },
  {
    name: 'status',
    label: 'Status',
    type: SearchFieldType.SELECT,
    placeholder: 'All',
    options: statusOptions.value,
    loading: isCustomerStatusLoading,
    defaultValue: null,
  },
])

const moreSearchFields = computed(() => [
  {
    name: 'created_at[]',
    label: 'Created At',
    type: SearchFieldType.DATE_RANGE,
    placeholder: 'Please select date range',
    defaultValue: [],
  },
])

// 对话框控制
const deleteCustomerDialog = ref(false)

const customer = ref<Partial<Customer.Info>>({
  id: '',
  name: '',
  email_primary: '',
})

const navigateToDetail = ({ data }: { data: any }) => {
  if (!hasPermission(Permissions.CUSTOMER_DETAIL)) {
    return
  }
  router.push({ name: 'customersDetail', params: { id: data.customer_id } })
}

const navigateToInviteCustomer = () => {
  if (!hasPermission(Permissions.CUSTOMER_CREATE)) {
    return
  }
  userStore.showSelectBid(() => {
    router.push({ name: 'customersCreateInvite' })
  })
}

// 确认删除对话框
const confirmDeleteCustomer = (editCustomer: Customer.Info) => {
  if (!hasPermission(Permissions.CUSTOMER_DELETE)) {
    return
  }
  customer.value = editCustomer
  deleteCustomerDialog.value = true
}

// 删除客户
const deleteCustomer = () => {
  if (!hasPermission(Permissions.CUSTOMER_DELETE)) {
    return
  }

  deleteCustomerDialog.value = false
  customerApi.remove([customer.value.customer_id!]).then((res) => {
    if (res.code === 0) {
      window.$toast.add({ severity: 'success', summary: t('common.success'), detail: t('customersPage.messages.customerDeleted'), life: 3000 })
      refresh()
    }
  }).catch((error) => {
    console.error('Failed to delete customer:', error)
    window.$toast.add({ severity: 'error', summary: t('common.error'), detail: 'Failed to delete customer', life: 3000 })
  })

  customer.value = {
    id: '',
    name: '',
    email_primary: '',
  } as Partial<Customer.Info>
}

// 搜索处理
const handleSearch = () => {
  setSearchParams(searchModel.value)
  search()
}

const removeSelectedCustomers = () => {
  if (!hasPermission(Permissions.CUSTOMER_DELETE)) {
    return
  }

  const ids = tableSelection.value.map((item: any) => item.customer_id)
  window.$confirm.require({
    header: 'Delete Customers',
    message: 'Are you sure you want to delete these customers?',
    accept: () => {
      customerApi.remove(ids).then((res) => {
        if (res.code === 0) {
          window.$toast.add({ severity: 'success', summary: t('common.success'), detail: 'Customers deleted successfully', life: 3000 })
          refresh()
        }
      }).catch((error) => {
        console.error('Failed to delete customers:', error)
        window.$toast.add({ severity: 'error', summary: t('common.error'), detail: 'Failed to delete customers', life: 3000 })
      })
    },
  })
}

onActivated(() => {
  const query = route.query
  if (query && Object.keys(query).length > 0) {
    // First update the searchModel with properly converted values
    searchModel.value = {
      'keyword': typeof query.keyword === 'string' ? query.keyword : '',
      'category': query.category ? Number(query.category) : '',
      'status': query.status ? Number(query.status) : null,
      'created_at[]': Array.isArray(query['created_at[]']) ? query['created_at[]'] as string[] : [],
    }

    // Then set search params and execute search
    setSearchParams(searchModel.value)
    refresh()
  }
  else {
    searchModel.value = {
      'keyword': '',
      'category': '',
      'status': null,
      'created_at[]': [],
    }
    setSearchParams(searchModel.value)
    refresh()
  }
})
</script>

<template>
  <div class="customer-page">
    <BaseSearch
      v-model="searchModel" :loading="isListLoading" :basic-search-fields="searchFields"
      :advanced-search-fields="moreSearchFields" @search="handleSearch"
    />

    <div class="flex items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8">
      <BaseExportDialog
        v-if="hasPermission(Permissions.CUSTOMER_EXPORT)"
        :loading="isListLoading"
        :export-loading="isExporting"
        @export="handleExport"
      />
      <Button
        v-if="hasPermission(Permissions.CUSTOMER_CREATE)"
        label="Add New Customer"
        icon="pi pi-plus"
        severity="warn"
        @click=" navigateToInviteCustomer"
      />
      <Divider v-if="hasAnyPermission([Permissions.CUSTOMER_CREATE, Permissions.CUSTOMER_DELETE])" layout="vertical" />
      <Button
        v-if="hasPermission(Permissions.CUSTOMER_DELETE)"
        icon="pi pi-trash"
        variant="text"
        :disabled="tableSelection.length === 0"
        rounded
        @click="removeSelectedCustomers"
      />
      <div class="flex-1" />
    </div>

    <!-- 客户表格 -->
    <BaseDataTable
      v-if="hasPermission(Permissions.CUSTOMER_LIST)"
      ref="customerTable"
      v-model:selection="tableSelection"
      :row-hover="hasPermission(Permissions.CUSTOMER_DETAIL)"
      :show-search-bar="false" :value="list"
      :columns="columns"
      :scrollable="true"
      :show-multiple-column="hasPermission(Permissions.CUSTOMER_DELETE)"
      :loading="loading"
      :paginator="true"
      :rows="50"
      :total-records="total"
      :lazy="true"
      data-key="id"
      :failed="failed"
      :failure-message="failureMessage"
      :striped-rows="true"
      style="--frozen-column-border-bottom : -8px"
      @change-search="handleSearch"
      @page="(e: DataTablePageEvent) => handlePageChange(e)"
      @row-click="navigateToDetail"
      @sort="handleSort"
    >
      <template #created_at="{ data }">
        {{ formatDate(data.created_at) }}
      </template>
      <template #last_payment="{ data }">
        {{ formatDate(data.last_payment) }}
      </template>
      <template #name="{ data }">
        <span>
          {{ data?.name }}
        </span>
      </template>
      <template #default_payment_method="{ data }">
        <div class="flex items-center">
          <BaseCardType
            :card-type="data?.customer_banking[0]?.credit_brand"
            :text="data?.customer_banking[0]?.account_no"
            :is-show-card-number="true"
          />
        </div>
      </template>
      <template #status="{ data }">
        <BaseTag :text="getCustomerStatusLabel(data.status)" :type="getTransactionTagStatus(data.status)" />
      </template>
      <template #action="{ data }">
        <BaseDataTableActions
          v-if="hasAnyPermission([Permissions.CUSTOMER_DELETE])"
          content-width="30px"
          :is-show-detail="false"
          :is-show-edit="false"
          :is-show-delete="hasPermission(Permissions.CUSTOMER_DELETE)"
          :loading="data.__loading"
          @detail="navigateToDetail(data)"
          @delete="confirmDeleteCustomer(data)"
        />
      </template>
    </BaseDataTable>

    <!-- 删除确认对话框 -->
    <Dialog
      v-model:visible="deleteCustomerDialog" :style="{ width: '450px' }"
      :header="t('customersPage.dialogs.confirmDelete')" :modal="true"
    >
      <div class="confirmation-content">
        <i class="pi pi-exclamation-triangle mr-3" style="font-size: 2rem" />
        <span v-if="customer">{{ t('customersPage.dialogs.deleteConfirmMessage', { name: customer.name }) }}</span>
      </div>
      <template #footer>
        <Button :label="t('common.no')" icon="pi pi-times" text @click="deleteCustomerDialog = false" />
        <Button :label="t('common.yes')" icon="pi pi-check" text @click="deleteCustomer" />
      </template>
    </Dialog>
  </div>
</template>

<style lang="scss" scoped>
.p-dialog .p-dialog-header {
  border-bottom: 1px solid #dee2e6;
  padding: 1.5rem;
}

.p-dialog .p-dialog-footer {
  border-top: 1px solid #dee2e6;
  padding: 1.5rem;
  text-align: right;
}

.p-dialog .p-dialog-content {
  padding: 2rem;
}

.confirmation-content {
  display: flex;
  align-items: center;
}
</style>
