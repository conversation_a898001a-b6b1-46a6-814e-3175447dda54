import type { UserConfig } from 'vite'
import * as path from 'node:path'
import * as process from 'node:process'
import { PrimeVueResolver } from '@primevue/auto-import-resolver'
import tailwindcss from '@tailwindcss/vite'
import vue from '@vitejs/plugin-vue'
import Components from 'unplugin-vue-components/vite'
import { defineConfig, loadEnv } from 'vite'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  return {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '@/': path.resolve(__dirname, 'src'),
        '~': path.resolve(__dirname, '/'),
        '@shared': path.resolve(__dirname, '../../packages/shared/src'),
        '@ui': path.resolve(__dirname, '../../packages/ui-components/src'),
      },
    },
    server: {
      port: 3003,
      proxy: {
        [env.VITE_API_BASE_URL]: {
          target: env.VITE_API_SERVER_URL,
          changeOrigin: true,
          rewrite: path => path.replace(/^\/api/, '/'),
        },
      },
    },
    build: {
      outDir: '../../dist/flexiratesMerchant',
      emptyOutDir: true,
      chunkSizeWarningLimit: 1024,
      rollupOptions: {
        output: {
          chunkFileNames: 'js/[name]-[hash].js',
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: '[ext]/[name]-[hash][extname]',
        },
      },
    },
    plugins: [
      vue(),
      tailwindcss(),
      Components({
        resolvers: [
          PrimeVueResolver(),
        ],
        dts: 'src/types/components.d.ts',
      }),
    ],
  } as UserConfig
})
