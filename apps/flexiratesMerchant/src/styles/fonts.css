/* Futura LT Pro 字体家族定义 */

/* Futura LT Pro Book */
@font-face {
    font-family: 'Futura';
    src: url('../assets/fonts/futura-lt-pro-book.otf') format('opentype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

/* Futura LT Pro Medium */
@font-face {
    font-family: 'Futura';
    src: url('../assets/fonts/futura-lt-pro-medium.otf') format('opentype');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

/* Futura LT Pro Heavy */
@font-face {
    font-family: 'Futura';
    src: url('../assets/fonts/futura-lt-pro-heavy.otf') format('opentype');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}


/* Futura LT Pro Bold */
@font-face {
    font-family: 'Futura';
    src: url('../assets/fonts/futura-lt-pro-bold.otf') format('opentype');
    font-weight: 800;
    font-style: normal;
    font-display: swap;
}

/* Futura LT Pro Extra Bold */
@font-face {
    font-family: 'Futura';
    src: url('../assets/fonts/futura-lt-pro-xbold.otf') format('opentype');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}