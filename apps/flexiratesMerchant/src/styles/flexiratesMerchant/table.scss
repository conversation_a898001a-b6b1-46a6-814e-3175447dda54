.layout-flexirates-merchant-wrapper {
    .p-datatable {

        &.p-datatable-striped {
            .p-datatable-table-container {
                padding: 1rem 2rem;
                background-color: white;

                .p-datatable-thead {
                    tr {
                        th {

                            &.p-datatable-frozen-column {
                                border: none;
                                padding: 0;
                                border-bottom: 1px solid var(--colors-gray);
                            }
                        }
                    }

                    .p-datatable-column-header-content {
                        padding: 8px 14px;

                        &:has(.p-checkbox) {
                            border-right: none;
                        }
                    }

                    .p-datatable-frozen-column {
                        .p-datatable-column-header-content {
                            border-right: none;
                        }
                    }
                }

                .p-datatable-tbody {
                    --p-datatable-row-striped-background: #f5f5ff;

                    .p-datatable-frozen-column {
                        background: white;
                    }

                    .p-row-odd {
                        .p-datatable-frozen-column {
                            background: #f5f5ff;
                        }
                    }

                    tr {
                        td {
                            border-bottom: none;

                            .p-row-odd {
                                background: #f5f5ff !important;
                            }
                        }
                    }
                }
            }
        }

        .p-datatable-table-container {
            padding: 0.75rem 2rem;
            background-color: white;
            border-radius: 16px;
            max-width: 100%;
            --p-datatable-header-cell-background: var(--colors-white);
            --p-datatable-header-cell-selected-background: var(--colors-white);

            .p-datatable-thead {
                .p-datatable-column-header-content {
                    padding: 8px 14px;

                    &:has(.p-checkbox) {
                        border-right: none;
                    }
                }

                .p-datatable-frozen-column {
                    .p-datatable-column-header-content {
                        border-right: none;
                    }
                }
            }

            .p-datatable-header-cell {
                padding-left: 0;
                padding-right: 0;
                border-bottom: 2px solid var(--colors-gray);

                &:last-child {
                    border-right: none;
                }
            }

            .p-datatable-tbody {
                tr:last-child {
                    td {
                        border-bottom: none;
                    }
                }
            }
        }
    }
}

.p-datatable-hoverable .p-datatable-tbody > tr:not(.p-datatable-row-selected):hover {
    cursor: pointer;
}