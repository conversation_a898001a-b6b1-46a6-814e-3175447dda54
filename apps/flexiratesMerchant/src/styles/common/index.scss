 
html {
    height: 100%;
    font-size: 14px;
    overflow-x: hidden;
}

body {
    --colors-primary: #181349;
    --colors-primary-1: #1B1548;
    --colors-info: #0073CF;
    --colors-pink: #FFE3E8;
    --colors-green: #E1FFA9;
    --colors-white: #ffffff;
    --colors-gray: #545454;
    --colors-blue: #031F73;
    --colors-dark-gray: #3b3b3b;
    --bg-colors-white: #f5f5ff;
    --sidebar-bg: var(--colors-white);
    --header-bg: var(--colors-white);
    --body-bg: var(--bg-colors-white);
    --text-primary: var(--p-primary-300);
    --surface-hover: var(--p-surface-200);
    --p-progressspinner-color-1: var(--p-primary-300);
    --menu-item-color: rgb(84, 84, 84);
    --swiper-pagination-color: var(--colors-info);
    --p-textarea-focus-border-color: var(--colors-info);
    --p-inputtext-focus-border-color: var(--colors-info);
    --p-select-focus-border-color: var(--colors-info);
    --p-radiobutton-checked-border-color: var(--colors-info);
    --p-radiobutton-checked-background: var(--colors-info);
    --p-radiobutton-checked-hover-border-colo: var(--colors-info);
    --p-radiobutton-checked-hover-background: var(--colors-info);
    --p-radiobutton-border-color: var(--colors-info);
    --p-radiobutton-checked-hover-border-color: var(--colors-info);
    --p-radiobutton-focus-ring-color: var(--colors-info);
    --p-inputtext-border-color: var(--colors-gray);
    --p-textarea-border-color: var(--colors-gray);
    --p-radiobutton-border-color: var(--colors-gray);
    --p-select-border-color: var(--colors-gray);
    --p-inputgroup-addon-border-color: var(--colors-gray);
    color: var(--colors-primary);

    --border-radius: 16px;
    margin: 0;
    font-family: 'Jost', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

.p-select-overlay {
    --p-select-overlay-border-radius: 8px;
    --p-select-overlay-border-color: transparent;
}

.p-select-list {
    background-color: transparent;
    --p-select-option-border-radius: 0;
    --p-select-option-selected-background: transparent;
    --p-select-list-padding: 8px 16px;
    border-radius: 8px;
    --p-select-option-padding: 0.75rem;

    .p-select-option {
        border-bottom: 1px solid var(--colors-gray);

        &:last-child {
            border-bottom: none;
        }

        &.p-select-option-selected {
            text-decoration: underline;
            font-weight: 600;
        }
    }
}