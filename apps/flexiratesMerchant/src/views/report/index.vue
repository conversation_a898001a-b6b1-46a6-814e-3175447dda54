<script setup lang="ts">
import { Format } from '@shared'
import dayjs from 'dayjs'
import { Decimal } from 'decimal.js'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from 'echarts/charts'
import {
  GraphicComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
} from 'echarts/components'
import * as echarts from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import Button from 'primevue/button'
import DatePicker from 'primevue/datepicker'
import Select from 'primevue/select'
import Skeleton from 'primevue/skeleton'
import { nextTick, onMounted, onUnmounted, ref } from 'vue'
import { useExport } from '@/composables/useExport'
import { report as reportApi } from '@/services/api'

// 注册 ECharts 组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>vas<PERSON><PERSON>er,
  GraphicComponent,
])

interface OverviewData extends Report.OverviewItem {
  title: string
}

const { isExporting, handleExport } = useExport({
  exportFn: reportApi.downloadReport,
  getParams: () => {
    return {
    }
  },
  onExportStart: () => {
    window.$toast.add({
      severity: 'info',
      summary: 'Export Started',
      detail: 'Preparing your export file...',
    })
  },
})

// 概览数据
const overviewData = ref<OverviewData[]>([
  {
    title: 'Total Payments Received',
    current: '0',
    last: '0',
  },
  {
    title: 'Total Number of Failed Payments',
    current: '0',
    last: '0',
  },
  {
    title: 'Total Number of Ratepayers',
    current: '0',
    last: '0',
  },
  {
    title: 'Total Amount in Arrears',
    current: '0',
    last: '0',
  },
])

// 底部统计数据
const bottomStats = ref<OverviewData[]>([
  {
    title: 'Total Active Registrations',
    current: '0',
    last: '0',
  },
  {
    title: 'Total Active Email Registrations',
    current: '0',
    last: '0',
  },
  {
    title: 'Total SMS Email Registrations',
    current: '0',
    last: '0',
  },
  {
    title: 'Total Refunds',
    current: '0',
    last: '0',
  },
])

const selectedDate = ref(new Date())
const displayDate = ref(new Date()) // 用于显示的日期

const monthlySelect = ref(new Date())

const annualSelect = ref(new Date().getFullYear())

const annualSelectOptions = ref<{ label: string, value: number }[]>([])

// 加载状态
const isOverviewLoading = ref(true)
const isMonthlyLoading = ref(true)
const isAnnualLoading = ref(true)

// 图表引用
const reportOverviewRef = ref<HTMLElement>()
const monthlyChartRef = ref<HTMLElement>()
const annualChartRef = ref<HTMLElement>()
const registrationsChartRef = ref<HTMLElement>()
const propertiesChartRef = ref<HTMLElement>()
const paymentsChartRef = ref<HTMLElement>()

let monthlyChart: echarts.ECharts | null = null
let annualChart: echarts.ECharts | null = null
let registrationsChart: echarts.ECharts | null = null
let propertiesChart: echarts.ECharts | null = null
let paymentsChart: echarts.ECharts | null = null
let resizeObserver: ResizeObserver | null = null

// 初始化月度图表
const initMonthlyChart = (data: Report.Monthly) => {
  if (!monthlyChartRef.value) { return }

  const xAxisData = []

  const seriesData = []

  for (const key in data) {
    xAxisData.push(dayjs(key).format('D'))
    seriesData.push(data[key])
  }

  monthlyChart = echarts.init(monthlyChartRef.value)
  const option = {
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#e0e0e0',
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
        },
      },
    },
    series: [{
      type: 'line',
      data: seriesData,
      smooth: true,
      lineStyle: {
        color: '#1890ff',
      },
      itemStyle: {
        color: '#1890ff',
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(24, 144, 255, 0.2)' },
          { offset: 1, color: 'rgba(24, 144, 255, 0.05)' },
        ]),
      },
    }],
  }
  monthlyChart.setOption(option)
}

// 初始化年度图表
const initAnnualChart = (data: Report.Annual) => {
  if (!annualChartRef.value) { return }

  const xAxisData = []

  const legendData = [String(annualSelect.value - 1), String(annualSelect.value)]

  const seriesData: {
    current: number[]
    last: number[]
  } = {
    current: [],
    last: [],
  }

  // 使用当前年度数据填充当前系列，并构建横坐标
  for (const key in data.current) {
    xAxisData.push(dayjs(key).format('MMM'))
    seriesData.current.push(Number(data.current[key]))
  }

  for (const key in data.last) {
    seriesData.last.push(Number(data.last[key]))
  }
  annualChart = echarts.init(annualChartRef.value)
  const option = {
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '10%',
      containLabel: true,
    },
    legend: {
      data: legendData,
      top: 0,
      right: 0,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: legendData[0],
        type: 'bar',
        data: seriesData.last,
        itemStyle: {
          color: '#1890ff',
        },
      },
      {
        name: legendData[1],
        type: 'bar',
        data: seriesData.current,
        itemStyle: {
          color: '#52c41a',
        },
      },
    ],
  }

  annualChart.setOption(option)
}

// 初始化注册饼图
const initRegistrationsChart = (data: { name?: string, value?: number }[]) => {
  if (!registrationsChartRef.value) { return }
  const colors = ['#B8D432', '#FF7043', '#E53E3E', '#1E88E5', '#9C27B0', '#FF9800']
  const legendData = data.map(item => item.name || '')
  const seriesData = data.map((item, index) => ({
    value: item.value || 0,
    name: item.name || '',
    itemStyle: {
      color: colors[index % colors.length],
    },
  }))

  registrationsChart = echarts.init(registrationsChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {d}%',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
      itemGap: 12,
      textStyle: {
        fontSize: 12,
        color: '#666',
      },
      data: legendData,
    },
    series: [
      {
        name: 'Registrations',
        type: 'pie',
        radius: ['0%', '70%'],
        center: ['65%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'outside',
          formatter: '{d}%',
          fontSize: 12,
          color: '#666',
          fontWeight: 'normal',
        },
        labelLine: {
          show: true,
          length: 8,
          length2: 5,
        },
        data: seriesData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.3)',
          },
        },
      },
    ],
  }
  registrationsChart.setOption(option)
}

// 初始化属性饼图
const initPropertiesChart = (data: { name?: string, value?: number }[]) => {
  if (!propertiesChartRef.value) { return }

  const colors = ['#B8D432', '#1E88E5', '#FF7043', '#E53E3E', '#9C27B0', '#FF9800']
  const legendData = data.map(item => item.name || '')
  const seriesData = data.map((item, index) => ({
    value: item.value || 0,
    name: item.name || '',
    itemStyle: {
      color: colors[index % colors.length],
    },
  }))

  propertiesChart = echarts.init(propertiesChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {d}%',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
      itemGap: 12,
      textStyle: {
        fontSize: 12,
        color: '#666',
      },
      data: legendData,
    },
    series: [
      {
        name: 'Properties',
        type: 'pie',
        radius: ['0%', '70%'],
        center: ['65%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'outside',
          formatter: '{d}%',
          fontSize: 12,
          color: '#666',
          fontWeight: 'normal',
        },
        labelLine: {
          show: true,
          length: 8,
          length2: 5,
        },
        data: seriesData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.3)',
          },
        },
      },
    ],
  }
  propertiesChart.setOption(option)
}

// 初始化支付饼图
const initPaymentsChart = (data: { name?: string, value?: number }[]) => {
  if (!paymentsChartRef.value) { return }

  const colors = ['#B8D432', '#1E88E5', '#E53E3E', '#FF7043', '#9C27B0', '#FF9800']
  const legendData = data.map(item => item.name || '')
  const seriesData = data.map((item, index) => ({
    value: item.value || 0,
    name: item.name || '',
    itemStyle: {
      color: colors[index % colors.length],
    },
  }))

  paymentsChart = echarts.init(paymentsChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {d}%',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
      itemGap: 12,
      textStyle: {
        fontSize: 12,
        color: '#666',
      },
      data: legendData,
    },
    series: [
      {
        name: 'Payments',
        type: 'pie',
        radius: ['0%', '70%'],
        center: ['65%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'outside',
          formatter: '{d}%',
          fontSize: 12,
          color: '#666',
          fontWeight: 'normal',
        },
        labelLine: {
          show: true,
          length: 8,
          length2: 5,
        },
        data: seriesData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.3)',
          },
        },
      },
    ],
  }
  paymentsChart.setOption(option)
}

// 响应式处理
const handleResize = () => {
  monthlyChart?.resize()
  annualChart?.resize()
  registrationsChart?.resize()
  propertiesChart?.resize()
  paymentsChart?.resize()
}

// 初始化容器尺寸监听
const initResizeObserver = () => {
  if (!reportOverviewRef.value) { return }

  resizeObserver = new ResizeObserver((entries) => {
    for (const entry of entries) {
      if (entry.target === reportOverviewRef.value) {
        handleResize()
      }
    }
  })

  resizeObserver.observe(reportOverviewRef.value)
}

const handleSearch = async () => {
  displayDate.value = new Date(selectedDate.value)
  await fetchOverview()
}

const fetchOverview = async () => {
  isOverviewLoading.value = true
  try {
    const { code, data } = await reportApi.getOverview({ month: dayjs(displayDate.value).format('YYYY-MM') })
    const transformData = (data: Report.OverviewItem, overviewData: OverviewData) => {
      return {
        title: overviewData.title,
        current: data.current,
        last: data.last,
      }
    }
    if (code === 0) {
      overviewData.value[0] = transformData(data.total_amount_received, overviewData.value[0])
      overviewData.value[1] = transformData(data.total_number_failed, overviewData.value[1])
      overviewData.value[2] = transformData(data.total_number_registered, overviewData.value[2])
      overviewData.value[3] = transformData(data.total_amount_arrears, overviewData.value[3])

      bottomStats.value[0] = transformData(data.total_active_registered, bottomStats.value[0])
      bottomStats.value[1] = transformData(data.total_self_registered, bottomStats.value[1])
      bottomStats.value[2] = transformData(data.total_merchant_registered, bottomStats.value[2])
      bottomStats.value[3] = transformData(data.total_number_registered, bottomStats.value[3])
    }
  }
  finally {
    isOverviewLoading.value = false
  }
}

const fetchMonthly = async () => {
  isMonthlyLoading.value = true
  try {
    const { code, data } = await reportApi.getMonthly({ month: dayjs(monthlySelect.value).format('YYYY-MM') })
    if (code === 0) {
      nextTick(() => {
        initMonthlyChart(data)
      })
    }
  }
  finally {
    isMonthlyLoading.value = false
  }
}

const fetchAnnual = async () => {
  isAnnualLoading.value = true
  try {
    const { code, data } = await reportApi.getAnnual({ year: String(annualSelect.value) })
    if (code === 0) {
      nextTick(() => {
        initAnnualChart(data)
      })
    }
  }
  finally {
    isAnnualLoading.value = false
  }
}

const fetchRegistrations = async () => {
  try {
    const { code, data } = await reportApi.getRegistrations()

    if (code === 0) {
      nextTick(() => {
        // Update Registrations chart
        if (data.registrations && data.registrations.length > 0) {
          initRegistrationsChart(data.registrations)
        }

        // Update Properties chart
        if (data.properties && data.properties.length > 0) {
          initPropertiesChart(data.properties)
        }

        // Update Payments chart
        if (data.payments && data.payments.length > 0) {
          initPaymentsChart(data.payments)
        }
      })
    }
  }
  catch (error) {
    console.error(error)
  }
}

const handleDownload = () => {
  window.$confirm.require({
    message: 'Are you sure you want to download the report?',
    header: 'Confirmation',
    accept: () => {
      handleExport('csv')
    },
  })
}

onMounted(() => {
  // 动态创建年度选择选项
  for (let i = 2023; i < new Date().getFullYear(); i++) {
    annualSelectOptions.value.push({ label: `${i}-${i + 1}`, value: i + 1 })
  }

  fetchOverview()
  fetchMonthly()
  fetchAnnual()

  fetchRegistrations()
  setTimeout(() => {
    initResizeObserver()
  }, 100)

  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  resizeObserver?.disconnect()
})
</script>

<template>
  <div ref="reportOverviewRef" class="report-overview">
    <!-- 头部标题和筛选 -->
    <div class="flex flex-col gap-4 bg-white mb-4 p-4 rounded-2xl">
      <div class="report-header">
        <div class="header-left">
          <h1>Overview</h1>
          <span class="date">as of {{ dayjs(displayDate).format('MMMM YYYY') }}</span>
        </div>
        <div class="header-right">
          <DatePicker
            v-model="selectedDate" placeholder="Select Date" class="date-picker" date-format="mm/yy"
            view="month" :max-date="new Date()"
          />
          <Button label="SEARCH" class="w-40" severity="info" @click="handleSearch" />
          <Button label="EXPORT" class="w-40" />
        </div>
      </div>

      <!-- 概览数据卡片 -->
      <div class="overview-cards">
        <!-- Skeleton 加载状态 -->
        <template v-if="isOverviewLoading">
          <div v-for="index in 4" :key="`skeleton-${index}`" class="overview-card">
            <div class="card-content">
              <Skeleton height="1.2rem" width="70%" class="mb-3" />
              <Skeleton height="2.5rem" width="60%" class="mb-2" />
              <div class="change-info">
                <Skeleton height="1rem" width="40%" />
                <Skeleton height="1rem" width="30%" />
              </div>
            </div>
          </div>
        </template>
        <!-- 实际数据 -->
        <template v-else>
          <div v-for="(item, index) in overviewData" :key="index" class="overview-card">
            <div class="card-content">
              <h3>{{ item.title }}</h3>
              <div class="value">
                {{ typeof item.current === 'string' ? Format.formatAmount(Decimal(Number(item.current) || 0).toFixed(2)) : Format.formatNumber(item.current) }}
              </div>
              <div class="change-info">
                <span>
                  <span class="arrow" :class="[item.current > item.last ? 'positive' : 'negative']">{{ item.current
                    > item.last
                    ? '↗' : '↘' }}</span>
                  <span class="change ml-4" :class="[item.current > item.last ? 'positive' : 'negative']">
                    <!-- 计算出百分比 -->
                    {{
                      Number(item?.last) === 0
                        ? (Number(item?.current) > 0 ? '100.00' : '0.00')
                        : Decimal(Number(item?.current) || 0).minus(Number(item?.last) || 0).div(Number(item?.last)
                          || 0).mul(100).toFixed(2)
                    }}%
                  </span>
                </span>
                <span class="date">as of {{ dayjs(displayDate).format('MMMM YYYY') }}</span>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <!-- 月度和年度图表 -->
      <div class="chart-row">
        <div class="chart-container">
          <div class="chart-header">
            <h3>Monthly</h3>
            <DatePicker
              v-model="monthlySelect" placeholder="Select Date" class="date-picker" date-format="mm/yy"
              view="month" :max-date="new Date()" @value-change="fetchMonthly"
            />
          </div>
          <div ref="monthlyChartRef" class="chart" />
          <!-- Skeleton 加载覆盖层 -->
          <!-- <div v-if="isMonthlyLoading" class="skeleton-overlay">
              <Skeleton height="100%" width="100%" />
            </div> -->
        </div>
        <div class="chart-container">
          <div class="chart-header">
            <h3>Annual</h3>
            <Select
              v-model="annualSelect" :options="annualSelectOptions" option-label="label" option-value="value"
              class="chart-dropdown" @value-change="fetchAnnual"
            />
          </div>
          <div ref="annualChartRef" class="chart" />
          <!-- Skeleton 加载覆盖层 -->
          <!-- <div v-if="isAnnualLoading" class="skeleton-overlay">
            <Skeleton height="100%" width="100%" />
          </div> -->
        </div>
      </div>

      <!-- 饼图区域 -->
      <div class="pie-charts-row">
        <div class="pie-chart-container">
          <h3>Registrations</h3>
          <div ref="registrationsChartRef" class="pie-chart" />
          <!-- Skeleton 加载覆盖层 -->
          <div v-if="isOverviewLoading" class="skeleton-overlay" />
        </div>
        <div class="pie-chart-container">
          <h3>Payments</h3>
          <div ref="paymentsChartRef" class="pie-chart" />
        </div>
        <div class="pie-chart-container">
          <h3>Properties</h3>
          <div ref="propertiesChartRef" class="pie-chart" />
        </div>
      </div>
    </div>

    <!-- 底部统计数据 -->
    <div class="bottom-stats">
      <div class="flex justify-end mb-4">
        <Button label="DOWNLOAD" severity="info" class="w-40" :loading="isExporting" @click="handleDownload" />
      </div>
      <div class="stats-cards">
        <!-- Skeleton 加载状态 -->
        <div v-if="isOverviewLoading" class="grid grid-cols-4 gap-4">
          <div v-for="index in 4" :key="`bottom-skeleton-${index}`" class="stat-card">
            <Skeleton height="1.2rem" width="70%" class="mb-3" />
            <Skeleton height="2.5rem" width="60%" class="mb-2" />
            <div class="change-info">
              <Skeleton height="1rem" width="40%" />
              <Skeleton height="1rem" width="30%" />
            </div>
          </div>
        </div>
        <!-- 实际数据 -->
        <template v-else>
          <div v-for="(item, index) in bottomStats" :key="index" class="stat-card">
            <h3>{{ item.title }}</h3>
            <div class="value">
              {{ Decimal(Number(item.current) || 0).toFixed(2) }}
            </div>
            <div class="change-info">
              <span>
                <span class="arrow" :class="[item.current > item.last ? 'positive' : 'negative']">{{ item.current
                  > item.last ? '↗' : '↘' }}</span>
                <span class="change ml-4" :class="[item.current > item.last ? 'positive' : 'negative']">
                  <!-- 计算出百分比 -->
                  {{
                    Number(item?.last) === 0
                      ? (Number(item?.current) > 0 ? '100.00' : '0.00')
                      : Decimal(Number(item?.current) || 0).minus(Number(item?.last) || 0).div(Number(item?.last)
                        || 0).mul(100).toFixed(2)
                  }}%
                </span>
              </span>
              <span class="date">as of {{ dayjs(displayDate).format('MMMM YYYY') }}</span>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped>
.report-overview {
  min-height: 100vh;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  padding-bottom: 8px;
  border-radius: 8px;
}

.header-left h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--colors-blue);
}

.date {
  color: #666;
  font-size: 14px;
  margin-left: 10px;
}

.header-right {
  display: flex;
  gap: 10px;
  align-items: center;
}

.period-dropdown {
  min-width: 150px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 12px;
  padding: 0 20px;
}

.overview-card {
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 16px;
}

.overview-card h3 {
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: 500;
}

.overview-card .value {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.change-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

.change {
  font-size: 14px;
  font-weight: 500;
}

.change.positive,
.arrow.positive {
  color: #52c41a;
}

.change.negative,
.arrow.negative {
  color: #ff4d4f;
}

.change-info .date {
  color: #999;
  font-size: 12px;
}

.charts-section {
  margin-bottom: 30px;
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-container {
  background: white;
  border-radius: 16px;
  padding: 24px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.chart-dropdown {
  min-width: 120px;
}

.chart {
  height: 300px;
  width: 100%;
  position: relative;
}

.pie-charts-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.pie-chart-container {
  background: white;
  border-radius: 16px;
  padding: 24px;
}

.pie-chart-container h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.pie-chart {
  height: 250px;
  width: 100%;
  position: relative;
}

.pie-total {
  text-align: center;
  margin-top: 10px;
  font-weight: 600;
  color: #333;
}

.bottom-stats {
  background: white;
  border-radius: 16px;
  padding: 20px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  border: 1px solid #e0e0e0;
  border-radius: 16px;
  padding: 20px;
}

.stat-card h3 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.stat-card .value {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

@media (max-width: 768px) {
  .report-header {
    flex-direction: column;
    gap: 15px;
  }

  .header-right {
    width: 100%;
    justify-content: center;
  }

  .chart-row {
    grid-template-columns: 1fr;
  }

  .pie-charts-row {
    grid-template-columns: 1fr;
  }
}
</style>
