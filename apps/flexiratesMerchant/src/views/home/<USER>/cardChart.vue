<script setup lang="ts">
import { LineChart } from 'echarts/charts'
import {
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
} from 'echarts/components'
import * as echarts from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

const props = defineProps({
  // 图表数据
  chartData: {
    type: Object,
    required: true,
    default: () => ({
      xAxis: [],
      series1: [],
      series2: [],
    }),
  },
  // 图表类型（用于控制颜色）
  chartType: {
    type: String,
    default: 'default',
    validator: (value: string) => ['default', 'blue', 'green', 'red'].includes(value),
  },
  // 图表标题
  title: {
    type: String,
    default: '',
  },
})

// 注册必要的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LineChart,
  Canvas<PERSON><PERSON><PERSON>,
  LegendComponent,
])

// 图表实例
let chartInstance: echarts.ECharts | null = null
// 图表容器引用
const chartRef = ref<HTMLElement | null>(null)

// 根据类型计算颜色
const chartColors = computed(() => {
  const colorMap = {
    default: ['#9E9E9E', '#FF7043'],
    blue: ['#9E9E9E', '#29B6F6'],
    green: ['#9E9E9E', '#66BB6A'],
    red: ['#9E9E9E', '#EF5350'],
  }
  return colorMap[props.chartType as keyof typeof colorMap] || colorMap.default
})

// 初始化图表
const initChart = () => {
  if (!chartRef.value) { return }

  // 创建 ECharts 实例
  chartInstance = echarts.init(chartRef.value)

  // 设置响应式
  window.addEventListener('resize', () => {
    chartInstance?.resize()
  })

  // 首次渲染
  updateChart()
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) { return }

  const { xAxis, series1, series2 } = props.chartData

  const option: echarts.EChartsCoreOption = {
    title: {
      text: props.title,
      left: 'center',
      show: !!props.title,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
    },
    legend: {
      data: ['Series 1', 'Series 2'],
      top: 0,
      left: 'center',
      itemGap: 40,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '0%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxis || [],
      offset: 8,
      axisLine: {
        lineStyle: {
          color: '#E0E0E0',
        },
      },
      axisLabel: {
        show: true,
        interval: 0,
        rotate: 60,
        fontSize: 12,
        align: 'right',
        margin: 8,
        color: '#000',
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: '#E0E0E0',
          type: 'dashed',
        },
      },
    },
    series: [
      {
        name: 'Series 1',
        type: 'line',
        stack: 'Total',
        data: series1 || [],
        showSymbol: false,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: echarts.color.modifyAlpha(chartColors.value[0], 0.5),
              },
              {
                offset: 1,
                color: echarts.color.modifyAlpha(chartColors.value[0], 0.1),
              },
            ],
          },
        },
        itemStyle: {
          color: chartColors.value[0],
        },
        lineStyle: {
          width: 2,
        },
        emphasis: {
          focus: 'series',
        },
        smooth: true,
      },
      {
        name: 'Series 2',
        type: 'line',
        stack: 'Total',
        data: series2 || [],
        showSymbol: false,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: echarts.color.modifyAlpha(chartColors.value[1], 0.5),
              },
              {
                offset: 1,
                color: echarts.color.modifyAlpha(chartColors.value[1], 0.1),
              },
            ],
          },
        },
        itemStyle: {
          color: chartColors.value[1],
        },
        lineStyle: {
          width: 2,
        },
        emphasis: {
          focus: 'series',
        },
        smooth: true,
      },
    ],
  }

  chartInstance.setOption(option)
}

// 监听数据变化
watch(
  () => [props.chartData, props.chartType],
  () => {
    updateChart()
  },
  { deep: true },
)

// 组件挂载后初始化图表
onMounted(() => {
  initChart()
})

// 组件卸载前销毁图表实例
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', () => {
    chartInstance?.resize()
  })
})

defineExpose({
  chartInstance,
})
</script>

<template>
  <div class="card-chart-wrapper">
    <div
      ref="chartRef"
      class="chart-container"
      style="height: 236px"
    />
  </div>
</template>

<style scoped>
.card-chart-wrapper {
  width: 100%;
  border-radius: 8px;
  box-sizing: border-box;
}

.chart-container {
  width: 100%;
}
</style>
