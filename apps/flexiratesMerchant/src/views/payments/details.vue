<script setup lang="ts">
import { Format } from '@shared'
import dayjs from 'dayjs'
import Skeleton from 'primevue/skeleton'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useDict } from '@/composables/useDict'
import { TransactionStatus } from '@/constants/transaction'
import { transactions as transactionApi } from '@/services/api'

const route = useRoute()
const transactionData = ref<Transaction.Info>({
  status: 0,
  timelines: [],
})
const loading = ref(true)

const { getLabel: getTransStatusLabel } = useDict('trans_status')

interface Product {
  id: number
  field: string
  isFieldBold: boolean
  amount: string
  isExpanded?: boolean
  details?: Transaction.SettlementDetail[]
}

const products = ref<Product[]>([
  {
    id: 1,
    field: 'Payment Amount',
    isFieldBold: false,
    amount: '',
  },
  {
    id: 2,
    field: 'Fees',
    isFieldBold: false,
    amount: '',
    isExpanded: false,
    details: [],
  },
  {
    id: 3,
    field: 'Net amount',
    isFieldBold: true,
    amount: '',
  },
])

const toggleFeeDetails = (product: Product) => {
  if (product.id === 2 && product.isExpanded !== undefined) {
    product.isExpanded = !product.isExpanded
  }
}

// const handleRefund = async () => {
//   window.$confirm.require({
//     message: 'Are you sure you want to refund this transaction?',
//     header: 'Warning',
//     icon: 'pi pi-exclamation-triangle',
//     rejectProps: {
//       label: 'Cancel',
//       severity: 'secondary',
//       outlined: true,
//     },
//     acceptProps: {
//       label: 'Confirm',
//     },
//     accept: async () => {
//       refundDialog.value = true
//       const { code, data } = await transactionsApi.refund({ ori_trans_no: transactionData.value.trans_no as string })
//       console.log(code, data)
//       if (code === 0) {
//         window.$toast.add({
//           severity: 'success',
//           summary: 'Success',
//           detail: 'Refund successful',
//           life: 3000,
//         })
//       }
//     },
//   })
// }

const fetchData = () => {
  loading.value = true
  transactionApi.getTransactionDetail(Number(route.params?.id)).then((res) => {
    products.value[0].amount = `${Format.formatAmount(res.data.payment_amount as string, res.data.payment_currency)} ${res.data.payment_currency}`
    products.value[1].amount = `${Format.formatAmount(res.data.fee_amount || 0, res.data.payment_currency)} ${res.data.payment_currency}`
    products.value[1].details = res.data.settlement_details
    products.value[2].amount = `${Format.formatAmount(res.data.payment_amount as string, res.data.payment_currency)} ${res.data.net_currency}`
    transactionData.value = res.data
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}

onMounted(() => {
  Promise.all([
    fetchData(),
  ])
})
</script>

<template>
  <div class="flex flex-col gap-y-6 lg:flex-row lg:gap-x-6 mt-4">
    <div class="transaction-content">
      <div v-if="loading">
        <div class="flex flex-col gap-y-4">
          <Skeleton width="60%" height="3rem" />
          <Skeleton width="40%" height="1.5rem" />
        </div>
        <div class="mt-10 mb-16">
          <Skeleton width="20%" height="2rem" class="mb-4" />
          <Skeleton width="100%" height="1rem" class="mb-2" />
          <Skeleton width="100%" height="1rem" class="mb-2" />
          <Skeleton width="80%" height="1rem" />
        </div>
        <div class="mb-4">
          <Skeleton width="30%" height="2rem" class="mb-4" />
          <Skeleton width="100%" height="3.5rem" class="mb-2" />
          <Skeleton width="100%" height="3.5rem" class="mb-2" />
          <Skeleton width="100%" height="3.5rem" />
        </div>
        <div class="mt-16">
          <Skeleton width="30%" height="2rem" class="mb-4" />
          <Skeleton width="100%" height="1.5rem" class="mb-2" />
          <Skeleton width="100%" height="1.5rem" class="mb-2" />
          <Skeleton width="100%" height="1.5rem" class="mb-2" />
          <Skeleton width="100%" height="1.5rem" class="mb-2" />
          <Skeleton width="100%" height="1.5rem" class="mb-2" />
        </div>
      </div>
      <div v-else>
        <div class="headLine flex justify-between items-center">
          <div class="flex flex-col gap-y-4">
            <div class="flex items-center">
              <span
                class="headLine-amount"
              >{{ Format.formatAmount(transactionData?.payment_amount as string, transactionData?.payment_currency) }}</span>
              <span class="headLine-currency">{{ transactionData?.payment_currency }}</span>
              <div
                class="headLine-tag" :class="transactionData.status === TransactionStatus.SUCCEEDED ? 'success'
                  : transactionData.status === TransactionStatus.FAILED ? 'failed' : 'default'"
              >
                {{ getTransStatusLabel(transactionData?.status as number) }}
              </div>
            </div>
            <div class="flex items-center gap-x-2">
              <span class="headLine-title">Charged to </span>
              <span class="headLine-content">{{ transactionData?.customer?.name }}</span>
            </div>
          </div>
          <!-- <div class=" flex items-center gap-x-2">
            <Button label="Refund" variant="outlined" icon="pi pi-reply" @click="handleRefund" />
          </div> -->
        </div>
        <div class="transactions-detail-left col-span-2 mt-10">
          <div class="time-line mb-16">
            <div class="flex justify-between items-center">
              <div class="time-line-title">
                Timeline
              </div>
            </div>
            <div class="notes-timeline mt-6">
              <div
                v-for="(note, index) in transactionData?.timelines" :key="note?.id" class="note-item flex gap-4"
                :class="{ first: index === 0, last: index === (transactionData?.timelines?.length || 1) - 1 }"
              >
                <!-- Timeline dot -->
                <div v-if="(transactionData?.timelines?.length || 1) > 1" class="timeline-dot" />
                <div v-if="(transactionData?.timelines?.length || 1) > 1" class="timeline-line" />

                <!-- Note content -->
                <div class="note-content flex-1 pb-6">
                  <div class="note-header mb-2">
                    <h4
                      class="text-xl font-semibold text-gray-900"
                      :style="{ color: index === 0 && note.status === 3 ? '#4ade80' : index === 0 && note.status === 2 ? '#ff0000' : '#545454' }"
                    >
                      {{ note.title }}
                    </h4>
                    <p class="text-sm text-gray-500 mt-2">
                      {{ dayjs(note.created_at).format('DD MMM YYYY, HH:mm') }}
                    </p>
                  </div>
                  <div class="note-body">
                    <p class="text-gray-600 leading-relaxed">
                      {{ note.remark }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="payment-breakdown">
            <div>
              <div class="payment-breakdown-title mb-6">
                Ratepayer
              </div>
            </div>
            <div class="w-full border-t border-gray-200">
              <div v-for="(product, index) in products" :key="product.id" class="w-full flex flex-wrap">
                <div
                  class="payment-detail py-5 px-2 md:w-full border-gray-200 flex items-center justify-between hover:bg-gray-100"
                  :class="{ 'border-b': index !== products.length - 1 || (product.id === 2 && product.isExpanded) }"
                  :style="product.id === 2 ? 'cursor: pointer' : ''"
                  @click="product.id === 2 ? toggleFeeDetails(product) : null"
                >
                  <div class="payment-label w-60" :class="{ 'font-semibold': product.isFieldBold }">
                    <i
                      v-if="product.id === 2 && product.details?.length" class="pi mr-2"
                      :class="product.isExpanded ? 'pi-chevron-down' : 'pi-chevron-right'"
                      style="margin-left: 8px; font-size: 12px;"
                    />
                    {{ product.field }}
                  </div>
                  <div class="payment-content" :class="{ 'font-semibold': product.isFieldBold }">
                    {{ product.amount }}
                  </div>
                </div>
                <!-- 费用详情展开区域 -->
                <div
                  v-if="product.id === 2 && product.isExpanded && product.details?.length"
                  class="w-full bg-gray-50 px-2 py-3 border-b border-gray-200"
                >
                  <div
                    v-for="(detail, idx) in product.details" :key="idx"
                    class="flex justify-between items-center py-2 pl-8"
                  >
                    <div class="text-sm text-gray-600">
                      {{ detail.amount_type_text }}
                    </div>
                    <div class="text-sm">
                      {{ Format.formatAmount(detail.settlement_amount, detail.settlement_currency) }}
                      {{ detail.settlement_currency }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="transactionData?.customer_banking" class="payment-method">
            <div class="mt-16">
              <div class="payment-method-title">
                Payment method
              </div>
              <Divider />
            </div>
            <div class="w-full flex flex-wrap">
              <div class="payment-detail  lg:w-1/2 h-10 md:w-full ">
                <div class="payment-label font-semibold w-60">
                  ID
                </div>
                <div class="content">
                  {{ transactionData?.trans_no }}
                </div>
              </div>
              <div class="payment-detail  lg:w-1/2 h-10 md:w-full">
                <div class="payment-label font-semibold w-60">
                  Owner
                </div>
                <div class="content">
                  {{ transactionData?.customer_banking?.account_name }}
                </div>
              </div>
              <div class="payment-detail  lg:w-1/2 h-10 md:w-full">
                <div class="payment-label font-semibold w-60">
                  Number
                </div>
                <div class="content">
                  <BaseCardType
                    v-if="transactionData?.customer_banking"
                    :text="transactionData?.customer_banking?.account_no" :is-show-card-number="true"
                  />
                </div>
              </div>
              <div class="payment-detail  lg:w-1/2 h-10 md:w-full">
                <div class="payment-label font-semibold w-60">
                  Address
                </div>
                <div class="content">
                  {{ transactionData?.customer_banking?.bill_address || 'No address' }}
                </div>
              </div>
              <div class="payment-detail  lg:w-1/2 h-10 md:w-full">
                <div class="payment-label font-semibold w-60">
                  Fingerprint
                </div>
                <div class="content text-[#9400ff]">
                  {{ transactionData?.customer_banking?.account_no }}
                </div>
              </div>
              <div class="payment-detail  lg:w-1/2 h-10 md:w-full">
                <div class="payment-label font-semibold w-60">
                  Expires
                </div>
                <div v-if="transactionData?.customer_banking?.expiration_month" class="content">
                  {{ `${transactionData?.customer_banking?.expiration_month}/${transactionData?.customer_banking?.expiration_year}` }}
                </div>
                <div v-else class="content">
                  -
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="transactions-detail">
      <div v-if="loading">
        <Skeleton width="60%" height="3rem" class="mb-4" />
        <Skeleton width="100%" height="2.75rem" class="mb-2" />
        <Skeleton width="100%" height="2.75rem" class="mb-2" />
        <Skeleton width="100%" height="2.75rem" class="mb-2" />
        <Skeleton width="100%" height="2.75rem" class="mb-2" />
        <Skeleton width="100%" height="2.75rem" class="mb-2" />
        <Skeleton width="100%" height="2.75rem" class="mb-4" />

        <Skeleton width="60%" height="3rem" class="mt-8 mb-4" />
        <Skeleton width="100%" height="2.75rem" class="mb-2" />
        <Skeleton width="100%" height="2.75rem" class="mb-2" />
        <Skeleton width="100%" height="2.75rem" />
      </div>
      <div v-else>
        <div class="transaction-details">
          <div class="title">
            Details
          </div>
          <Divider />
          <div class="transaction-details">
            <div class="details-edit">
              <div class="details-edit-title">
                Payment ID
              </div>
              <div class="details-edit-content">
                {{ transactionData?.trans_no }}
              </div>
            </div>
            <div class="details-edit">
              <div class="details-edit-title">
                Payment method
              </div>
              <div class="details-edit-content flex items-center">
                <BaseCardType
                  v-if="transactionData?.customer_banking"
                  :card-type="transactionData?.customer_banking?.credit_brand"
                  :text="transactionData?.customer_banking?.account_no" :is-show-card-number="true"
                />
              </div>
            </div>
            <div class="details-edit">
              <div class="details-edit-title">
                Last updated
              </div>
              <div class="details-edit-content">
                <span>{{ transactionData?.plan?.updated_at || '-' }}</span>
              </div>
            </div>
            <!-- <div class="details-edit">
              <div class="details-edit-title">
                Risk evaluation
              </div>
              <div class="details-edit-content">
                <i class="pi pi-ban" style="color: #9400ff;" />
                <span> Normal</span>
              </div>
            </div> -->
          </div>
        </div>
        <div v-if="transactionData?.customer" class="customer-info">
          <div class="title">
            Ratepayer
          </div>
          <Divider />
          <div>
            <div class="details-edit">
              <div class="details-edit-title">
                ID
              </div>
              <div class="details-edit-content">
                {{ transactionData?.customer?.customer_id }}
              </div>
            </div>
            <div class="details-edit">
              <div class="details-edit-title">
                Name
              </div>
              <div class="details-edit-content">
                {{ transactionData?.customer?.name }}
              </div>
            </div>
            <div class="details-edit">
              <div class="details-edit-title">
                Description
              </div>
              <div class="details-edit-content">
                {{ transactionData?.customer?.description || '-' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints' as *;

:deep(.transaction-timeline .p-timeline-event-opposite) {
  display: none;
}

.time-line-title,
.payment-breakdown-title,
.payment-method-title {
  font-size: 26px;
  font-weight: 800;
  color: var(--colors-blue);
}

.payment-label,
.payment-content {
  color: var(--colors-gray);
}

.transaction-timeline-title {
  color: var(--colors-gray);
  font-weight: 600;
}

.transaction-timeline-content {
  font-size: 12px;
  color: var(--colors-gray);
}

.headLine {
  margin-bottom: 8px;
  font-size: 15px;

  &-title {
    color: #666;
  }

  &-content {
    text-decoration: underline;
    font-weight: 600;
    color: var(--colors-warn);
  }

  &-amount {
    font-size: 30px;
    font-weight: 800;
    color: var(--colors-gray);
  }

  &-currency {
    font-size: 30px;
    font-weight: 800;
    color: var(--colors-gray);
    margin-left: 8px;
  }

  &-tag {
    font-size: 18px;
    font-weight: 600;
    color: var(--colors-gray);
    margin-left: 16px;
    padding: 6px 32px;
    border-radius: 8px;

    &.default {
      color: #fff;
      background-color: #031F73;
    }

    &.success {
      color: #fff;
      background-color: #008000;
    }

    &.failed {
      color: #fff;
      background-color: #ff0000;
    }
  }
}

.transactions-detail {

  background-color: #031F73;
  border-radius: 16px;
  padding: 24px;
  width: 398px;
  color: white;

  @include media-breakpoint-down(lg) {
    width: 100%;
  }

  .title {
    font-size: 27px;
    font-weight: 800;
  }

  .details-edit {
    margin-bottom: 12px;

    &-title {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    &-content {
      background-color: #ffffff;
      border-radius: 8px;
      padding: 0 16px;
      line-height: 44px;
      min-height: 44px;
      font-size: 15px;
      color: #3B3B3B;
    }
  }

}

.notes-timeline {
  position: relative;

  .note-item {
    position: relative;
    --left: 2rem;

    .timeline-line {
      position: absolute;
      top: 0;
      left: calc(var(--left) - 1px);
      width: 2px;
      height: 100%;
      background-color: var(--colors-blue);
    }

    .timeline-dot {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: calc(var(--left) - 8px);
      width: 16px;
      height: 16px;
      background-color: var(--colors-blue);
      border-radius: 50%;
    }

    &.first {
      .timeline-line {
        top: 40%;
      }

      // 绘制向上的三角形
      .timeline-dot {
        top: 35%;
        left: calc(var(--left) - 12px);
        border-radius: 0;
        width: 0;
        height: 0;
        background-color: transparent;
        border: 12px solid transparent;
        border-bottom-color: var(--colors-blue);
      }
    }

    &.last {
      .timeline-line {
        height: 50%;
      }
    }

    .note-content {
      background-color: #F5F5FF;
      padding: 16px;
      padding-left: calc(var(--left) + 24px);
      border-radius: 12px;
      margin-bottom: 12px;
    }

  }
}

.payment-detail {
  display: flex;

  @include media-breakpoint-down(lg) {
    width: 100%;

    .payment-label {
      width: 120px;
    }
  }
}

.transaction-content {
  background-color: #fff;
  border-radius: 16px;
  padding: 24px;
  flex: 1;
}
</style>
