<script setup lang="ts">
import RatepayerForm from './components/ratepayerForm.vue'
import { useRatepayerForm } from './composables/useRatepayerForm'

defineOptions({
  name: 'flexiratesMerchantRatepayerCreate',
})

const {
  initialValues,
  schema,
  loading,
  verifyLoading,
  submitForm,
  cancel,
  scheduleData,
  handleVerify,
} = useRatepayerForm('create')
</script>

<template>
  <div class="ratepayer-create-page">
    <div class="px-6 py-8 bg-white rounded-2xl">
      <RatepayerForm
        :schedule-data="scheduleData"
        :initial-values="initialValues" :verify-loading="verifyLoading" :validation-schema="schema"
        :loading="loading"
        @submit="submitForm" @cancel="cancel" @verify="handleVerify"
      />
    </div>
  </div>
</template>
