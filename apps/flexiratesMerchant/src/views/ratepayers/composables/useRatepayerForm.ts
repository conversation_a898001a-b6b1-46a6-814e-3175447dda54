import { toTypedSchema } from '@vee-validate/yup'
import { useToast } from 'primevue/usetoast'
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import * as yup from 'yup'
import { useListRefresh } from '@/composables/useListRefresh'
import { ratepayer as ratepayerApi } from '@/services/api'

export interface PropertyDetails {
  id?: string
  address: string
  type: string
  value: number
  // 可以根据需要添加更多属性
}

export interface PaymentMethod {
  id?: string
  type: 'bank_transfer' | 'credit_card' | 'direct_debit'
  account_name: string
  account_number: string
  // 可以根据需要添加更多属性
}

export interface Schedule {
  id?: string
  frequency: 'weekly' | 'monthly' | 'quarterly' | 'annually'
  amount: number
  due_date: string
  // 可以根据需要添加更多属性
}

export interface ScheduleFormData {
  plan: number
  first_payment_date: string
  amount: number
}

export interface RatepayerFormData {
  first_name: string
  last_name: string
  mobile_number: string
  email_address: string
  street_address: string
  city: string
  state: string
  postcode: string

  payment_method: 'credit_card' | 'bank_account'

  // property Details
  property_number: string
  verification_code: string

  payment_frequency: {
    payment_plan: number
    first_payment_date: string
    amount: number
  }

  payment_detail: {
    type: number
    bank: {
      bsb_number: string
      account_name: string
      account_number: string
    }
    card: {
      card_number: string
      expiry_month: string
      expiry_year: string
      name_on_card: string
      cvv: string
    }
    payment_method_type: string
  }

  schedule: {
    plan: number
    first_payment_date: string
    amount: number
  }

  is_default_payment_method: boolean
}

export interface RatepayerDetailData extends RatepayerFormData {
  id?: string
  properties?: PropertyDetails[]
  payment_methods?: PaymentMethod[]
  schedules?: Schedule[]
  created_at?: string
  updated_at?: string
}

export function useRatepayerForm(mode: 'create' | 'edit' = 'create') {
  const toast = useToast()
  const router = useRouter()
  const loading = ref(false)
  const verifyLoading = ref(false)

  const scheduleData = ref<Ratepayer.Schedule>({
    address: '',
    default_payment_plan: 0,
    dict_payment_plan: [],
    last_payment_date: '',
    property_number: '',
    suburb: '',
    total_amount_due: '',
  })

  const { backWithRefresh } = useListRefresh('ratepayersList', () => { })

  // 表单初始值
  const getInitialValues = (): RatepayerFormData => ({
    first_name: '',
    last_name: '',
    mobile_number: '',
    email_address: '',
    street_address: '',
    city: '',
    state: '',
    postcode: '',
    property_number: '',
    verification_code: '',
    payment_method: 'bank_account',
    payment_frequency: {
      payment_plan: 0,
      first_payment_date: '',
      amount: 0,
    },
    payment_detail: {
      type: 0,
      bank: {
        bsb_number: '',
        account_name: '',
        account_number: '',
      },
      card: {
        card_number: '',
        expiry_month: '',
        expiry_year: '',
        name_on_card: '',
        cvv: '',
      },
      payment_method_type: '',
    },
    schedule: {
      plan: 0,
      first_payment_date: '',
      amount: 0,
    },
    is_default_payment_method: true,
  })

  const initialValues = reactive(getInitialValues())

  // 表单验证schema
  const schema = toTypedSchema(yup.object({
    first_name: yup.string()
      .min(2, 'First name must be at least 2 characters')
      .max(50, 'First name must be less than 50 characters')
      .required('First name is required'),
    last_name: yup.string()
      .min(2, 'Last name must be at least 2 characters')
      .max(50, 'Last name must be less than 50 characters')
      .required('Last name is required'),
    mobile_number: yup.string()
      .matches(/^\+?[\d\s\-()]+$/, 'Please enter a valid mobile number')
      .min(10, 'Mobile number must be at least 10 characters')
      .required('Mobile number is required'),
    email_address: yup.string()
      .email('Please enter a valid email address')
      .required('Email address is required'),
    street_address: yup.string()
      .min(5, 'Street address must be at least 5 characters')
      .max(200, 'Street address must be less than 200 characters')
      .required('Street address is required'),
    city: yup.string()
      .min(2, 'City must be at least 2 characters')
      .max(100, 'City must be less than 100 characters')
      .required('City is required'),
    state: yup.string()
      .min(2, 'State must be at least 2 characters')
      .max(50, 'State must be less than 50 characters')
      .required('State is required'),
    postcode: yup.string()
      .matches(/^\d{4,6}$/, 'Please enter a valid postcode (4-6 digits)')
      .required('Postcode is required'),

    // property Details
    property_number: yup.string()
      .required('Property number is required'),
    verification_code: yup.string()
      .required('Verification code is required'),

    is_default_payment_method: yup.boolean().optional(),
  }))

  // 重置表单
  const resetForm = () => {
    Object.assign(initialValues, getInitialValues())
  }

  // 设置表单值（用于编辑模式）
  const setFormValues = (data: Partial<RatepayerFormData>) => {
    Object.assign(initialValues, data)
  }

  // 提交表单
  const submitForm = async (data: Api.RatepayerCreateReq) => {
    console.log('data', data)

    loading.value = true

    try {
      const { code } = await ratepayerApi.createRatepayer(data)
      if (code === 0) {
        toast.add({
          severity: 'success',
          summary: 'Success',
          detail: mode === 'create' ? 'Ratepayer created successfully' : 'Ratepayer updated successfully',
          life: 3000,
        })
        backWithRefresh()
      }
    }
    catch (error) {
      console.error(`Failed to ${mode} ratepayer:`, error)
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: mode === 'create' ? 'Failed to create ratepayer' : 'Failed to update ratepayer',
        life: 3000,
      })
    }
    finally {
      loading.value = false
    }
  }

  // 取消操作
  const cancel = () => {
    router.back()
  }

  const handleVerify = async (data: { property_number: string, verification_code: string }) => {
    try {
      verifyLoading.value = true
      const { data: result, code } = await ratepayerApi.getSchedule(data)
      if (code === 0) {
        scheduleData.value = result
        verifyLoading.value = false
      }
    }
    finally {
      verifyLoading.value = false
    }
  }

  return {
    initialValues,
    scheduleData,

    schema,
    loading,
    verifyLoading,
    resetForm,
    setFormValues,
    submitForm,
    cancel,
    handleVerify,
  }
}
