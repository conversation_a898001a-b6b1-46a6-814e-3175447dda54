<script setup lang="ts">
// import type { RatepayerFormData } from './composables/useRatepayerForm'
import { onMounted } from 'vue'
// import { useRoute } from 'vue-router'
// import RatepayerForm from './components/ratepayerForm.vue'
// import { useRatepayerForm } from './composables/useRatepayerForm'

defineOptions({
  name: 'flexiratesMerchantRatepayerEdit',
})

// const route = useRoute()

// const {
//   initialValues,
//   schema,
//   loading,
//   setFormValues,
//   submitForm,
//   cancel,
// } = useRatepayerForm('edit')

// 获取ratepayer ID
// const ratepayerId = route.params.id as string

// 处理表单提交
// const handleSubmit = (formRef: any) => {
// 可以传入自定义提交处理函数
// submitForm(formRef)
// }

// 加载现有数据
const loadRatepayerData = async () => {
  try {
    // TODO: 从API获取ratepayer数据
    // const response = await ratepayerApi.getById(ratepayerId)
    // const data = response.data

    // 模拟数据
    // const mockData: RatepayerFormData = {
    //   first_name: 'John',
    //   last_name: 'Doe',
    //   mobile_number: '+**********',
    //   email_address: '<EMAIL>',
    //   street_address: '123 Main Street',
    //   state: 'NSW',
    //   postcode: '2000',
    //   payment_method: 'credit_card',
    //   property_number: '**********',
    //   verification_code: '123456',
    //   payment_frequency: {
    //     payment_plan: 1,
    //     first_payment_date: '2021-01-01',
    //     amount: 100,
    //   },
    //   payment_detail: {
    //     type: 1,
    //     bank: {
    //       bsb_number: '123456',
    //       account_name: 'John Doe',
    //       account_number: '**********',
    //     },
    //     card: {
    //       card_number: '**********',
    //       expiry_month: '01',
    //       expiry_year: '2021',
    //       name_on_card: 'John Doe',
    //       cvv: '123',
    //     },
    //     payment_method_type: 'credit_card',
    //   },
    // }

    // setFormValues(mockData)
  }
  catch (error) {
    console.error('Failed to load ratepayer data:', error)
  }
}

onMounted(() => {
  loadRatepayerData()
})
</script>

<template>
  <div class="ratepayer-edit-page">
    <div class="flex justify-between items-center mb-4">
      <h1 class="text-3xl">
        Edit Ratepayer
      </h1>
    </div>

    <div class="p-6 bg-white rounded-2xl">
      <!-- <RatepayerForm
        :initial-values="initialValues"
        :validation-schema="schema"
        :loading="loading"
        @submit="handleSubmit"
        @cancel="cancel"
      /> -->
    </div>
  </div>
</template>
