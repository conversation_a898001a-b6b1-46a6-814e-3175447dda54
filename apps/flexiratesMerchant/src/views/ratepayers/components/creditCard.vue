<script setup lang="ts">
import type { FormContext } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/yup'
import { storeToRefs } from 'pinia'
import { Field, Form as VeeForm } from 'vee-validate'
import { ref, watch } from 'vue'
import * as yup from 'yup'
import { useCountryStore } from '@/store/modules/country'

const formRef = ref<FormContext | null>(null)

const countryStore = useCountryStore()

const { countries: countryOptions, isLoading: isGetCountryLoading, isLoaded } = storeToRefs(countryStore)

// 州/领地选项
const stateOptions = ref()

const schema = toTypedSchema(yup.object({
  card_number: yup.string()
    .min(13, 'Card number must be between 13 and 19 digits')
    .max(19, 'Card number must be between 13 and 19 digits')
    .test('is-valid-card', 'Invalid card number', (value: string | undefined) => {
      if (!value) { return false }
      const cleanValue = value.replace(/\D/g, '')
      let sum = 0
      let shouldDouble = false
      for (let i = cleanValue.length - 1; i >= 0; i--) {
        let digit = Number.parseInt(cleanValue.charAt(i))
        if (shouldDouble) {
          digit *= 2
          if (digit > 9) { digit -= 9 }
        }
        sum += digit
        shouldDouble = !shouldDouble
      }
      return sum % 10 === 0
    }),
  expiry_month: yup.string()
    .matches(/^(0[1-9]|1[0-2])$/, 'Month must be 01-12')
    .required('Expiry month is required'),
  expiry_year: yup.string()
    .matches(/^\d{2}$/, 'Year must be 2 digits')
    .test('is-not-expired', 'Card has expired', (value: string | undefined, context) => {
      if (!value) { return false }
      const month = context.parent.expiry_month
      if (!month) { return true } // Let month validation handle missing month

      const currentDate = new Date()
      const currentYear = currentDate.getFullYear() % 100
      const currentMonth = currentDate.getMonth() + 1

      const expiryMonth = Number.parseInt(month)
      const expiryYear = Number.parseInt(value)

      return !(expiryYear < currentYear || (expiryYear === currentYear && expiryMonth < currentMonth))
    }),
  security_code: yup.string()
    .matches(/^\d{3,4}$/, 'Security code must be 3 or 4 digits'),
  name_on_card: yup.string()
    .min(2, 'Name must be at least 2 characters')
    .test('has-full-name', 'Please enter your full name as it appears on the card', (value: string | undefined) => {
      return value ? value.includes(' ') : false
    }),
  email: yup.string().email('Invalid email'),
  // Billing address fields
  first_name: yup.string().min(2, 'First name must be at least 2 characters').required('First name is required'),
  last_name: yup.string().min(2, 'Last name must be at least 2 characters').required('Last name is required'),
  street_address: yup.string().min(5, 'Address must be at least 5 characters').required('Address is required'),
  street_address_2: yup.string(),
  city: yup.string().min(2, 'City must be at least 2 characters').required('City is required'),
  state: yup.string().min(2, 'State is required').required('State is required'),
  postcode: yup.string().matches(/^\d{4}$/, 'Postcode must be 4 digits').required('Postcode is required'),
  country_iso2: yup.string().length(2, 'Country code must be 2 characters').required('Country is required'),
  phone: yup.string().min(10, 'Phone number must be at least 10 digits').required('Phone is required'),
  nickname: yup.string(),
}))

const setCountry = (value: string, isInit = false) => {
  if (!isInit) {
    formRef.value?.setFieldValue('state', '')
  }
  const country = countryOptions.value.find((item: any) => item.iso2 === value)
  if (country && country.state && country.state.length > 0) {
    stateOptions.value = country?.state
    formRef.value?.setFieldValue('state', country?.state[0]?.name)
  }
  else {
    stateOptions.value = []
  }
}

const countryHandler = watch(() => isLoaded, (newVal) => {
  if (newVal && formRef.value?.values?.country_iso2) {
    setCountry(formRef.value?.values?.country_iso2, true)
    countryHandler()
  }
}, {
  immediate: true,
})

defineExpose({
  validate: () => {
    return formRef.value?.validate()
  },
})
</script>

<template>
  <VeeForm ref="formRef" v-slot="{ values }" :validation-schema="schema">
    <!-- Card Number -->
    <Field v-slot="{ field, errorMessage }" name="card_number">
      <div class="field mb-4 mt-4">
        <InputText v-bind="field" :class="{ 'p-invalid': errorMessage }" placeholder="Card Number" class="w-full" />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <div class="form-row flex gap-4 mb-4">
      <!-- Expiry Month -->
      <Field v-slot="{ field, errorMessage }" as="div" name="expiry_month" class="form-col flex-1">
        <div class="field">
          <InputMask
            v-bind="field"
            placeholder="Month (MM)"
            mask="99"
            :class="{ 'p-invalid': errorMessage }" class="w-full"
          />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>

      <!-- Expiry Year -->
      <Field v-slot="{ field, errorMessage }" as="div" name="expiry_year" class="form-col flex-1">
        <div class="field">
          <InputMask
            v-bind="field"
            placeholder="Year (YY)"
            mask="99"
            :class="{ 'p-invalid': errorMessage }" class="w-full"
          />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>

      <!-- Security Code -->
      <Field v-slot="{ field, errorMessage }" as="div" name="security_code" class="form-col flex-1">
        <div class="field">
          <InputText v-bind="field" :class="{ 'p-invalid': errorMessage }" placeholder="CVV (Security code)" class="w-full" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>
    </div>

    <!-- Name on Card -->
    <Field v-slot="{ field, errorMessage }" name="name_on_card">
      <div class="field mb-4">
        <InputText v-bind="field" :class="{ 'p-invalid': errorMessage }" placeholder="Name on card" class="w-full" />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <!-- Email -->
    <Field v-slot="{ field, errorMessage }" name="email">
      <div class="field mb-4">
        <InputText v-bind="field" :class="{ 'p-invalid': errorMessage }" placeholder="Email" class="w-full" type="email" />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <!-- Phone -->
    <Field v-slot="{ field, errorMessage }" name="phone">
      <div class="field mb-4">
        <InputText v-bind="field" :class="{ 'p-invalid': errorMessage }" placeholder="Phone Number" class="w-full" type="tel" />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <!-- Billing Address Section -->
    <div class="billing-address-section mt-6">
      <h3 class="mb-4 text-lg font-semibold">
        Billing Address
      </h3>

      <div class="form-row flex gap-4 mb-4">
        <!-- First Name -->
        <Field v-slot="{ field, errorMessage }" as="div" name="first_name" class="form-col flex-1">
          <div class="field">
            <InputText v-bind="field" :class="{ 'p-invalid': errorMessage }" placeholder="First Name *" class="w-full" />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>

        <!-- Last Name -->
        <Field v-slot="{ field, errorMessage }" as="div" name="last_name" class="form-col flex-1">
          <div class="field">
            <InputText v-bind="field" :class="{ 'p-invalid': errorMessage }" placeholder="Last Name *" class="w-full" />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>
      </div>

      <!-- Address Line 1 -->
      <Field v-slot="{ field, errorMessage }" name="street_address">
        <div class="field mb-4">
          <InputText v-bind="field" :class="{ 'p-invalid': errorMessage }" placeholder="Address Line 1 *" class="w-full" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>

      <!-- Address Line 2 (Optional) -->
      <Field v-slot="{ field, errorMessage }" name="street_address_2">
        <div class="field mb-4">
          <InputText v-bind="field" :class="{ 'p-invalid': errorMessage }" placeholder="Address Line 2 (Optional)" class="w-full" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>

      <div class="form-row flex gap-4 mb-4">
        <!-- City -->
        <Field v-slot="{ field, errorMessage }" as="div" name="city" class="form-col flex-1">
          <div class="field">
            <InputText v-bind="field" :class="{ 'p-invalid': errorMessage }" placeholder="City *" class="w-full" />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>

        <!-- State -->
        <Field v-slot="{ field, errorMessage, handleChange }" as="div" name="state" class="form-col flex-1">
          <div class="field">
            <Select
              id="state"
              class="w-full"
              :model-value="field.value"
              :options="stateOptions"
              option-label="name"
              option-value="name"
              placeholder="Select state"
              :loading="isGetCountryLoading"
              :disabled="!values.country_iso2"
              filter
              @value-change="handleChange"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>

        <!-- Postcode -->
        <Field v-slot="{ field, errorMessage }" as="div" name="postcode" class="form-col flex-1">
          <div class="field">
            <InputText v-bind="field" :class="{ 'p-invalid': errorMessage }" placeholder="Postcode *" class="w-full" />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>
      </div>

      <!-- Country -->
      <Field v-slot="{ field, errorMessage, handleChange }" name="country_iso2">
        <div class="field mb-4">
          <Select
            id="country"
            v-model="field.value"
            class="w-full"
            :options="countryOptions"
            option-label="name"
            option-value="iso2"
            placeholder="Country Code (e.g., AU) *"
            :loading="isGetCountryLoading"
            filter
            show-clear
            @update:model-value="e => {
              handleChange(e)
              setCountry(e)
            }"
          />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>

      <!-- Nickname -->
      <Field v-slot="{ field, errorMessage }" name="nickname">
        <div class="field mb-4">
          <InputText v-bind="field" :class="{ 'p-invalid': errorMessage }" placeholder="Card Nickname (Optional)" class="w-full" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>
    </div>
  </VeeForm>
</template>

<style scoped>
.form-row {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.form-col {
  flex: 1;
  min-width: 200px;
}

.billing-address-section {
  border-top: 1px solid #e5e7eb;
  padding-top: 1.5rem;
}

/* Responsive styles */
@media screen and (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .form-col {
    min-width: 100%;
  }
}
</style>
