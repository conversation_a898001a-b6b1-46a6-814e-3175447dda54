<script setup lang="ts">
import type { FormContext } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/yup'
import { Field, Form as VeeForm } from 'vee-validate'
import { ref } from 'vue'
import * as yup from 'yup'

const formRef = ref<FormContext | null>(null)

const schema = toTypedSchema(yup.object({
  bsb_number: yup.string()
    .required('BSB Number is required')
    .matches(/^\d{3}-?\d{3}$/, 'BSB Number must be 6 digits (XXX-XXX or XXXXXX format)'),
  account_name: yup.string()
    .required('Account Name is required')
    .min(2, 'Account Name must be at least 2 characters'),
  account_number: yup.string()
    .required('Account Number is required')
    .matches(/^\d{6,10}$/, 'Account Number must be 6-10 digits'),
  nickname: yup.string()
    .min(2, 'Nickname must be at least 2 characters'),
}))

defineExpose({
  validate: () => {
    return formRef.value?.validate()
  },
})
</script>

<template>
  <VeeForm ref="formRef" :validation-schema="schema">
    <!-- BSB Number -->
    <Field v-slot="{ field, errorMessage }" name="bsb_number">
      <div class="field mb-4 mt-4">
        <InputText v-bind="field" :class="{ 'p-invalid': errorMessage }" placeholder="BSB Number *" class="w-full" />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <!-- Account Name -->
    <Field v-slot="{ field, errorMessage }" name="account_name">
      <div class="field mb-4">
        <InputText v-bind="field" :class="{ 'p-invalid': errorMessage }" placeholder="Account Name *" class="w-full" />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <!-- Account Number -->
    <Field v-slot="{ field, errorMessage }" name="account_number">
      <div class="field mb-4">
        <InputText v-bind="field" :class="{ 'p-invalid': errorMessage }" placeholder="Account Number *" class="w-full" />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <!-- Nickname -->
    <Field v-slot="{ field, errorMessage }" name="nickname">
      <div class="field mb-4">
        <InputText v-bind="field" :class="{ 'p-invalid': errorMessage }" placeholder="Nickname" class="w-full" />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>
  </VeeForm>
</template>
