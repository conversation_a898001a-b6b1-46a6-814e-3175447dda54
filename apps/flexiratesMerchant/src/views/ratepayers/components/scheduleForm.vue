<script setup lang="ts">
import type { FormContext } from 'vee-validate'
import { Constants } from '@shared'
import { toTypedSchema } from '@vee-validate/yup'
import dayjs from 'dayjs'
import { Decimal } from 'decimal.js'
import { Field, Form as VeeForm } from 'vee-validate'
import { onMounted, ref, watch } from 'vue'
import * as yup from 'yup'

const props = defineProps<{
  scheduleData: Ratepayer.Schedule
}>()

const paymentPlanRef = ref<FormContext>()

// Payment Plan 选项
const paymentPlanOptions = ref<{ label: string, value: number }[]>([
  {
    label: 'Calculated Installments',
    value: Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS,
  },
  {
    label: 'Custom Installment Amount',
    value: Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT,
  },
  {
    label: 'Pay Full Amount',
    value: Constants.Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW,
  },
])

const dictPaymentPlan = ref<{ label: string, value: number }[]>([
  {
    label: 'Weekly',
    value: Constants.Flexirates.Plan.WEEKLY,
  },
  {
    label: 'Fortnightly',
    value: Constants.Flexirates.Plan.FORTNIGHTLY,
  },
  {
    label: 'Monthly',
    value: Constants.Flexirates.Plan.MONTHLY,
  },
  {
    label: 'Quarterly',
    value: Constants.Flexirates.Plan.QUARTERLY,
  },
])

const regular_payment_amount = ref<string | null>(null)
const no_of_regular_payment = ref<number | null>(null)
const lastPaymentAmount = ref<string>('')

const paymentPlanSchema = toTypedSchema(yup.object({
  paymentPlan: yup.number().min(1, 'Payment plan is required'),
  paymentPlanSchedule: yup.number().when('paymentPlan', {
    is: (val: number) => [Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS, Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT].includes(val),
    then: schema => schema.min(1, 'Payment schedule is required'),
    otherwise: schema => schema.optional(),
  }),
  first_payment_date: yup.date()
    .min(dayjs().startOf('day').toDate(), 'First payment date must be today or in the future'),
  amount: yup.number().when('paymentPlan', {
    is: Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT,
    then: schema => schema.min(0.01, 'Amount is required'),
    otherwise: schema => schema.optional(),
  }),
  // 保持向后兼容的字段
  flexiRates: yup.number().optional(),
  plan: yup.number().optional(),
}))

const calculateLastPaymentAmount = ({ amount, count, totalAmountDue }: { amount: string, count: number, totalAmountDue: number }) => {
  const lastAmount = Decimal(totalAmountDue).sub(Decimal(amount).mul(count)).toFixed(2, Decimal.ROUND_HALF_UP)
  lastPaymentAmount.value = lastAmount
}

const calculateRegularPaymentAmount = ({
  first_payment_date,
  plan,
  last_payment_date,
}: {
  first_payment_date: string
  plan: number
  last_payment_date: string
}) => {
  if (!first_payment_date || !plan || !last_payment_date) {
    no_of_regular_payment.value = 0
    regular_payment_amount.value = '0'
    return
  }

  const totalAmountDue = props.scheduleData?.total_amount_due || 1

  const firstPaymentDateDate = dayjs(first_payment_date)
  const lastPaymentDateDate = dayjs(last_payment_date)

  let paymentCount = 0
  let amount = '0'

  switch (plan) {
    // 一次性付款
    case Constants.Flexirates.Plan.FULL_AMOUNT:
      paymentCount = 1
      amount = Decimal(totalAmountDue || 1).toFixed(2, Decimal.ROUND_HALF_UP)
      break
    // 季度付款
    case Constants.Flexirates.Plan.QUARTERLY:
      paymentCount = Math.abs(lastPaymentDateDate.diff(firstPaymentDateDate, 'quarter') || 1)
      amount = Decimal(totalAmountDue).div(paymentCount).toFixed(2, Decimal.ROUND_HALF_UP)
      break
    // 月度付款
    case Constants.Flexirates.Plan.MONTHLY:
      paymentCount = Math.abs(lastPaymentDateDate.diff(firstPaymentDateDate, 'month') || 1)
      amount = Decimal(totalAmountDue).div(paymentCount).toFixed(2, Decimal.ROUND_HALF_UP)
      break
    // 双周付款
    case Constants.Flexirates.Plan.FORTNIGHTLY: {
      const daysDiff = lastPaymentDateDate.diff(firstPaymentDateDate, 'day') + 1
      paymentCount = daysDiff <= 14 ? 1 : Math.ceil(daysDiff / 14)
      amount = Decimal(totalAmountDue).div(paymentCount).toFixed(2, Decimal.ROUND_HALF_UP)
      break
    }
    // 周度付款
    case Constants.Flexirates.Plan.WEEKLY: {
      const daysDiff = lastPaymentDateDate.diff(firstPaymentDateDate, 'day') + 1
      paymentCount = daysDiff <= 7 ? 1 : Math.ceil(daysDiff / 7)
      amount = Decimal(totalAmountDue).div(paymentCount).toFixed(2, Decimal.ROUND_HALF_UP)
      break
    }
  }

  regular_payment_amount.value = amount
  no_of_regular_payment.value = Math.floor(paymentCount)

  // 如果用户选择的是自定义金额，则计算最后一期金额
  if (paymentPlanRef?.value && paymentPlanRef?.value?.values?.amount && paymentPlanRef?.value?.values?.paymentPlan === Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT) {
    calculateLastPaymentAmount({
      amount: paymentPlanRef?.value?.values?.amount as string,
      count: no_of_regular_payment.value,
      totalAmountDue: Number(props.scheduleData?.total_amount_due || 0),
    })
  }
}

onMounted(() => {
  watch([
    () => paymentPlanRef.value?.values.amount,
    () => paymentPlanRef.value?.values.first_payment_date,
    () => paymentPlanRef.value?.values.plan,
    () => paymentPlanRef.value?.values.paymentPlanSchedule,
    () => props.scheduleData?.last_payment_date,
  ], () => {
    // 支持新的字段结构，同时保持向后兼容
    const plan = paymentPlanRef.value?.values.paymentPlanSchedule || paymentPlanRef.value?.values.plan
    calculateRegularPaymentAmount({
      first_payment_date: paymentPlanRef.value?.values.first_payment_date as string,
      plan: plan as number,
      last_payment_date: props.scheduleData?.last_payment_date as string,
    })
  }, {
    immediate: true,
  })
})

defineExpose({
  getValues: () => {
    return paymentPlanRef.value?.values
  },
  validate: () => {
    return paymentPlanRef.value?.validate()
  },
})
</script>

<template>
  <VeeForm
    ref="paymentPlanRef" v-slot="{ values }" :validation-schema="paymentPlanSchema" class="flex flex-col gap-x-8 gap-y-4"
    :initial-values="{
      paymentPlan: Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS,
      paymentPlanSchedule: props.scheduleData?.default_payment_plan || 0,
      plan: props.scheduleData?.default_payment_plan || 0,
      first_payment_date: new Date(),
      amount: 0,
    }"
  >
    <div class="flex flex-col gap-y-2">
      <div class="flex gap-x-2">
        <label class="form-item__label">Address:</label>
        <div class="form-item__content">
          {{ props.scheduleData?.address }} {{ props.scheduleData?.suburb }}
        </div>
      </div>
      <div class="flex gap-x-2">
        <label class="form-item__label">Total amount due:</label>
        <div class="form-item__content">
          ${{ new Decimal(props.scheduleData?.total_amount_due
            || 0).toFixed(2)
          }}
        </div>
      </div>
    </div>

    <Field v-slot="{ errorMessage, field, handleChange }" name="paymentPlan" as="div" class="form-item">
      <label class="form-item__label is-required">Payment Plan</label>
      <div class="form-item__content flex flex-col gap-y-2 mt-2">
        <Select
          v-model="field.value" :options="paymentPlanOptions" option-label="label"
          option-value="value" placeholder="Select a Payment Plan" class="w-full"
          :class="{ 'p-invalid': errorMessage }"
          @value-change="handleChange"
        />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>
    <Field
      v-if="[Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS, Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT].includes(values?.paymentPlan as number)"
      v-slot="{ errorMessage, field, handleChange }" name="paymentPlanSchedule" as="div" class="form-item"
    >
      <label class="form-item__label is-required">Payment Schedule</label>
      <div class="form-item__content flex flex-col gap-y-2 mt-2">
        <Select
          v-model="field.value" :options="dictPaymentPlan" option-label="label"
          option-value="value" placeholder="Select a Payment Schedule" class="w-full"
          :class="{ 'p-invalid': errorMessage }"
          @value-change="handleChange"
        />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>
    <!-- 保持向后兼容的字段，隐藏显示 -->
    <Field v-show="false" v-slot="{ errorMessage, field, handleChange }" name="plan" as="div" class="form-item">
      <label class="form-item__label is-required">Payment plan (Legacy)</label>
      <div class="form-item__content flex flex-col gap-y-2 mt-2">
        <Select
          v-model="field.value" :options="props.scheduleData.dict_payment_plan" :class="{ 'p-invalid': errorMessage }"
          option-label="label"
          option-value="value" placeholder="Select a Payment Plan" class="w-full" @value-change="handleChange"
        />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>
    <Field v-slot="{ errorMessage, field, handleChange }" name="first_payment_date" as="div" class="form-item">
      <label class="form-item__label is-required">First payment date</label>
      <div class="form-item__content flex flex-col gap-y-2 mt-2">
        <DatePicker
          v-model="field.value" :min-date="new Date()"
          :max-date="dayjs(props.scheduleData.last_payment_date).toDate()" date-format="dd/mm/yy" show-icon
          class="w-full" :class="{ 'p-invalid': errorMessage }"
          @value-change="handleChange"
        />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>
    <Field
      v-if="values?.paymentPlan === Constants.Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT"
      v-slot="{ errorMessage, field, handleChange }" name="amount" as="div" class="form-item"
    >
      <label class="form-item__label is-required">Amount</label>
      <div class="form-item__content flex flex-col gap-y-2 mt-2">
        <!-- 允许两位小数 -->
        <InputNumber
          v-model="field.value" :min="0" :min-fraction-digits="2" :max-fraction-digits="2"
          :max="Number(props.scheduleData?.total_amount_due || 0)"
          :class="{ 'p-invalid': errorMessage }"
          @value-change="handleChange"
        />
        <div v-if="values.amount > 0 && Number(lastPaymentAmount) > 0" class="text-sm text-gray-600 mt-2">
          The last payment amount is: ${{ lastPaymentAmount }}
        </div>
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>
    <div class="form-item flex">
      <label class="form-item__label">Last payment date :</label>
      <div class="form-item__content">
        <span class="ml-2">
          {{ props.scheduleData.last_payment_date ? dayjs(props.scheduleData.last_payment_date).format('DD/MM/YYYY') : '' }}
        </span>
      </div>
    </div>
    <div v-if="values?.paymentPlan === Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS" class="form-item flex">
      <label class="form-item__label">Regular payment amount :</label>
      <div class="form-item__content">
        <span class="ml-2">
          {{ regular_payment_amount ? `$${new Decimal(regular_payment_amount).toFixed(2)}` : '' }}
        </span>
      </div>
    </div>
    <div class="form-item flex">
      <label class="form-item__label">Number of payments during
        {{ dayjs().year() }}
        <template v-if="props.scheduleData.last_payment_date">
          - {{ dayjs(props.scheduleData.last_payment_date).year() }}
        </template>
        :</label>
      <span class="ml-2">
        <template v-if="values?.paymentPlan === Constants.Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW">
          1
        </template>
        <template v-else>
          {{ no_of_regular_payment ? no_of_regular_payment : '' }}
        </template>
      </span>
    </div>
  </VeeForm>
</template>
