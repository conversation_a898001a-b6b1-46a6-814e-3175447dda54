<script setup lang="ts">
import type { FormContext } from 'vee-validate'
import type { RatepayerFormData } from '../composables/useRatepayerForm'
import dayjs from 'dayjs'
import { Field, Form as VeeForm } from 'vee-validate'
import { ref } from 'vue'
import BankAccount from './bankAccount.vue'
import CreditCard from './creditCard.vue'
import ScheduleForm from './scheduleForm.vue'

interface Props {
  initialValues: RatepayerFormData
  validationSchema: any
  loading?: boolean
  verifyLoading?: boolean
  scheduleData: Ratepayer.Schedule
}

interface Emits {
  (e: 'submit', formRef: any): void
  (e: 'cancel'): void
  (e: 'verify', data: { property_number: string, verification_code: string }): void
}

const props = defineProps<Props>()

const emit = defineEmits<Emits>()

const formRef = ref<FormContext>()

const scheduleFormRef = ref<typeof ScheduleForm>()

const creditCardRef = ref<typeof CreditCard>()

const bankAccountRef = ref<typeof BankAccount>()

const handleSubmit = async () => {
  const result = await formRef.value?.validate()

  if (!result?.valid) {
    console.log('formValues', result?.errors)
    return
  }

  if (!scheduleFormRef.value) {
    return window.$toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Please get the schedule first',
    })
  }

  const { valid: scheduleFormValid, values: scheduleFormValues } = await scheduleFormRef.value?.validate()

  if (!scheduleFormValid) {
    return
  }

  const sendData = {
    first_name: result.values?.first_name,
    last_name: result.values?.last_name,
    mobile: result.values?.mobile_number,
    email: result.values?.email_address,
    street_address: result.values?.street_address,
    city: result.values?.city,
    state: result.values?.state,
    postcode: result.values?.postcode,
    property: {
      property_number: result.values?.property_number,
      verification_code: result.values?.verification_code,
    },
    payment_frequency: {
      payment_plan: scheduleFormValues?.plan,
      first_payment_date: scheduleFormValues?.first_payment_date ? dayjs(scheduleFormValues?.first_payment_date).format('YYYY-MM-DD') : null,
      amount: scheduleFormValues?.amount,
    },
    banking: {
      type: result.values?.payment_method === 'credit_card' ? 2 : 1,
      weight: result.values?.is_default_payment_method ? 1 : 0,
    },
  } as Api.RatepayerCreateReq

  if (result.values?.payment_method === 'credit_card') {
    const { valid: creditCardValid, values: creditCardValues } = await creditCardRef.value?.validate()
    if (!creditCardValid) {
      return
    }

    sendData.banking!.card = {
      card_number: creditCardValues?.card_number,
      city: creditCardValues?.city,
      country_iso2: creditCardValues?.country_iso2,
      email: creditCardValues?.email,
      expiration_month: creditCardValues?.expiry_month,
      expiration_year: creditCardValues?.expiry_year,
      name_on_card: creditCardValues?.name_on_card,
      security_code: creditCardValues?.security_code,
      state: creditCardValues?.state,
      first_name: creditCardValues?.first_name,
      last_name: creditCardValues?.last_name,
      line_1: creditCardValues?.street_address,
      line_2: creditCardValues?.street_address_2,
      postcode: creditCardValues?.postcode,
      phone: creditCardValues?.phone,
      nickname: creditCardValues?.nickname,
    }
  }

  if (result.values?.payment_method === 'bank_account') {
    const { valid: bankAccountValid, values: bankAccountValues } = await bankAccountRef.value?.validate()
    if (!bankAccountValid) {
      return
    }

    sendData.banking!.bank = {
      bsb: bankAccountValues?.bsb_number,
      account_name: bankAccountValues?.account_name,
      account_no: bankAccountValues?.account_number,
      nickname: bankAccountValues?.nickname,
    }
  }

  emit('submit', sendData)
}

const handleVerify = async () => {
  const propertyNumber = await formRef.value?.validateField('property_number')
  const verificationCode = await formRef.value?.validateField('verification_code')

  if (propertyNumber?.valid && verificationCode?.valid) {
    emit('verify', {
      property_number: formRef.value?.values.property_number as string,
      verification_code: formRef.value?.values.verification_code as string,
    })
  }
}
</script>

<template>
  <VeeForm
    ref="formRef" v-slot="{ values }" :validation-schema="props.validationSchema"
    class="ratepayer-form flex flex-col gap-4" :initial-values="initialValues" @submit="handleSubmit"
  >
    <div class="text-2xl font-bold">
      Ratepayer Details
    </div>
    <!-- 姓名行 -->
    <div class="form-row">
      <!-- 名字 -->
      <Field v-slot="{ field, errorMessage }" name="first_name" as="div" class="form-col">
        <div class="field">
          <label for="first_name" class="mb-2 block">First Name</label>
          <InputText
            id="first_name" v-bind="field" placeholder="First Name" class="w-full"
            :class="{ 'p-invalid': errorMessage }"
          />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>

      <!-- 姓氏 -->
      <Field v-slot="{ field, errorMessage }" name="last_name" as="div" class="form-col">
        <div class="field">
          <label for="last_name" class="mb-2 block">Last Name</label>
          <InputText
            id="last_name" v-bind="field" placeholder="Last Name" class="w-full"
            :class="{ 'p-invalid': errorMessage }"
          />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>
    </div>

    <!-- 联系信息行 -->
    <div class="form-row">
      <!-- 手机号码 -->
      <Field v-slot="{ field, errorMessage }" name="mobile_number" as="div" class="form-col">
        <div class="field">
          <label for="mobile_number" class="mb-2 block">Mobile Number</label>
          <InputText
            id="mobile_number" v-bind="field" placeholder="Mobile Number" class="w-full" type="tel"
            :class="{ 'p-invalid': errorMessage }"
          />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>

      <!-- 邮箱地址 -->
      <Field v-slot="{ field, errorMessage }" name="email_address" as="div" class="form-col">
        <div class="field">
          <label for="email_address" class="mb-2 block">Email Address</label>
          <InputText
            id="email_address" v-bind="field" placeholder="Email Address" class="w-full" type="email"
            :class="{ 'p-invalid': errorMessage }"
          />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>
    </div>

    <!-- 邮寄地址标题 -->
    <div class="mt-4">
      <h3 class="text-lg font-medium mb-2">
        Mailing Address
      </h3>
    </div>

    <!-- 街道地址 -->
    <Field v-slot="{ field, errorMessage }" name="street_address" as="div" class="form-col">
      <div class="field">
        <label for="street_address" class="mb-2 block">Street Address</label>
        <InputText
          id="street_address" v-bind="field" placeholder="Street Address" class="w-full"
          :class="{ 'p-invalid': errorMessage }"
        />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <!-- 郊区 -->
    <Field v-slot="{ field, errorMessage }" name="city" as="div" class="form-col">
      <div class="field">
        <label for="city" class="mb-2 block">City</label>
        <InputText id="city" v-bind="field" placeholder="city" class="w-full" :class="{ 'p-invalid': errorMessage }" />
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </div>
    </Field>

    <!-- 州和邮编行 -->
    <div class="form-row">
      <!-- 州 -->
      <Field v-slot="{ field, errorMessage }" name="state" as="div" class="form-col">
        <div class="field">
          <label for="state" class="mb-2 block">State</label>
          <InputText
            id="state" v-bind="field" placeholder="State" class="w-full"
            :class="{ 'p-invalid': errorMessage }"
          />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>

      <!-- 邮编 -->
      <Field v-slot="{ field, errorMessage }" name="postcode" as="div" class="form-col">
        <div class="field">
          <label for="postcode" class="mb-2 block">Postcode</label>
          <InputText
            id="postcode" v-bind="field" placeholder="Postcode" class="w-full"
            :class="{ 'p-invalid': errorMessage }"
          />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>
    </div>

    <div class="text-2xl font-bold">
      Property Details
    </div>

    <!-- 州和邮编行 -->
    <div class="form-row">
      <!-- 州 -->
      <Field v-slot="{ field, errorMessage }" name="property_number" as="div" class="form-col">
        <div class="field">
          <label for="property_number" class="mb-2 block">Property Number</label>
          <InputText
            id="property_number" v-bind="field" placeholder="Property Number" class="w-full"
            :class="{ 'p-invalid': errorMessage }"
          />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>

      <!-- 邮编 -->
      <Field v-slot="{ field, errorMessage }" name="verification_code" as="div" class="form-col">
        <div class="field">
          <label for="verification_code" class="mb-2 block">Verification Code</label>
          <InputText
            id="verification_code" v-bind="field" placeholder="Verification Code" class="w-full"
            :class="{ 'p-invalid': errorMessage }"
          />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>
    </div>

    <div class="form-row justify-end">
      <Button
        type="button" label="Get Schedule"
        :disabled="values.property_number === '' || values.verification_code === ''" :loading="verifyLoading"
        @click="handleVerify"
      />
    </div>

    <div v-if="props.scheduleData.dict_payment_plan.length > 0" class="text-2xl font-bold">
      Schedule
    </div>
    <ScheduleForm
      v-if="props.scheduleData.dict_payment_plan.length > 0" ref="scheduleFormRef"
      :schedule-data="props.scheduleData"
    />

    <div class="text-2xl font-bold">
      Payment Methods
    </div>

    <div class="form-row">
      <Field v-slot="{ field, errorMessage }" name="payment_method" as="div" class="form-col">
        <div class="field flex flex-col gap-2">
          <div class="flex items-center gap-2">
            <RadioButton v-model="field.value" input-id="credit_card" name="payment_method" value="credit_card" />
            <label for="credit_card" class="cursor-pointer">Credit Card</label>
            <RadioButton
              v-model="field.value" class="ml-4" input-id="bank_account" name="payment_method"
              value="bank_account"
            />
            <label for="bank_account" class="cursor-pointer">Bank Account</label>
          </div>
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </div>
      </Field>
    </div>
    <CreditCard v-if="values.payment_method === 'credit_card'" ref="creditCardRef" />
    <BankAccount v-if="values.payment_method === 'bank_account'" ref="bankAccountRef" />

    <Field v-slot="{ errorMessage, field, handleChange }" v-model="initialValues.is_default_payment_method" name="is_default_payment_method" as="div" class="form-item is-required">
      <div class="form-item__content flex flex-col gap-y-2 my-2">
        <div class="flex items-center gap-x-2">
          <RadioButton v-model="field.value" input-id="is_default_payment_method_primary" name="is_default_payment_method" :value="true" @change="handleChange" />
          <label for="is_default_payment_method_primary" class="cursor-pointer" @change="handleChange">Make Default
            <strong>Primary</strong>
            Payment Method?</label>
          <RadioButton
            v-model="field.value"
            class="ml-4" input-id="is_default_payment_method_secondary" name="is_default_payment_method"
            :value="false" @change="handleChange"
          />
          <label for="is_default_payment_method_secondary" class="cursor-pointer">Make Default
            <strong>Secondary</strong> Payment Method?</label>
        </div>
        <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
        <p class="text-sm text-gray-500 my-2">
          This will not affect any current active schedules.
          Your selected preference will be used to
          autofill future
          registrations.
        </p>
      </div>
    </Field>

    <!-- 表单按钮 -->
    <div class="flex justify-end mt-6 gap-2">
      <Button type="button" label="Cancel" icon="pi pi-times" class="p-button-text" @click="$emit('cancel')" />
      <Button type="button" label="Save" icon="pi pi-check" :loading="loading" @click="handleSubmit" />
    </div>
  </VeeForm>
</template>

<style scoped>
.ratepayer-form {
  max-width: 800px;
}

.form-row {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.form-col {
  flex: 1;
  min-width: 250px;
  /* 确保列不会太窄 */
}

.w-full {
  width: 100%;
}

/* 响应式样式 */
@media screen and (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 1rem;
  }

  .form-col {
    min-width: 100%;
  }
}

/* 小型移动设备 */
@media screen and (max-width: 480px) {
  :deep(.p-float-label) {
    font-size: 0.9rem;
  }

  :deep(.p-inputtext) {
    font-size: 0.9rem;
    padding: 0.5rem;
  }

  .ratepayer-form {
    max-width: 100%;
  }
}
</style>
