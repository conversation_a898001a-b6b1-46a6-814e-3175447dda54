<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useListRefresh } from '@/composables/useListRefresh'
import { ratepayer as ratepayerApi } from '@/services/api'
import RatepayerDetail from './components/ratepayerDetail.vue'

defineOptions({
  name: 'flexiratesMerchantRatepayerDetail',
})

const route = useRoute()
const router = useRouter()
const loading = ref(true)
const ratepayerData = ref<Ratepayer.DetailInfo | null>(null)

const customerId = ref<string>(route.query.customerId as string)

const { backWithRefresh: backWithRefreshRatepayers } = useListRefresh('flexiratesMerchantRatepayersList', () => {})

const { backWithRefresh: backWithRefreshProperties } = useListRefresh('flexiratesMerchantPropertiesList', () => {})

// 加载ratepayer数据
const loadRatepayerData = async () => {
  loading.value = true
  try {
    const { data, code } = await ratepayerApi.getDetail({ id: route.params.id as string })
    if (code === 0) {
      ratepayerData.value = data as Ratepayer.DetailInfo
    }
  }
  finally {
    loading.value = false
  }
}

// 处理编辑操作
const handleEdit = () => {
  router.push({ name: 'flexiratesMerchantRatepayersEdit', params: { id: route.params.id as string } })
}

// 处理返回操作
const handleBack = () => {
  router.back()
}

// 处理更新操作
const handleUpdate = () => {
  backWithRefreshRatepayers(false)
  backWithRefreshProperties(false)
  loadRatepayerData()
}

onMounted(() => {
  loadRatepayerData()
})
</script>

<template>
  <div class="ratepayer-detail-page">
    <!-- 加载状态 -->
    <div v-if="loading" class="p-6 flex justify-center items-center bg-white rounded-lg shadow-sm">
      <ProgressSpinner class="w-8 h-8" />
      <span class="ml-3 text-gray-600">Loading ratepayer details...</span>
    </div>

    <!-- 详情内容 -->
    <RatepayerDetail v-else :customer-id="customerId" :data="ratepayerData" :loading="loading" @edit="handleEdit" @back="handleBack" @update="handleUpdate" />
  </div>
</template>

<style scoped>
.ratepayer-detail-page {
  margin: 0 auto;
}

@media screen and (max-width: 768px) {
  .ratepayer-detail-page {
    padding: 0.5rem;
  }

  .flex.justify-between {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}
</style>
