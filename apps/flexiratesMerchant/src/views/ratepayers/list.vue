<script setup lang="ts">
import type { DataTablePageEvent, DataTableRowClickEvent } from 'primevue/datatable'
import type { DictItem } from '@/services/api/dict'
import { toTypedSchema } from '@vee-validate/yup'
import dayjs from 'dayjs'
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'
import { Field, Form as VeeForm } from 'vee-validate'
import { computed, onActivated, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import * as yup from 'yup'
import BaseDataTable from '@/components/common/BaseDataTable.vue'
// import BaseDataTableActions from '@/components/common/BaseDataTableActions.vue'
import BaseExportDialog from '@/components/common/BaseExportDialog.vue'
import BaseSearch from '@/components/common/BaseSearch.vue'
import CustomDialog from '@/components/customDialog/index.vue'
import { useDict } from '@/composables/useDict'
import { useExport } from '@/composables/useExport'
import { useListRefresh } from '@/composables/useListRefresh'
import { usePermissions } from '@/composables/usePermissions'
import { useRequestList } from '@/composables/useRequestList'
import { Permissions } from '@/constants/permissions'
import { SearchFieldType } from '@/constants/search'
import { ratepayer as ratepayerApi, support as supportApi } from '@/services/api'
import { formatDate } from '@/utils/date'
import { addAllToDict } from '@/utils/dict'

defineOptions({
  name: 'flexiratesMerchantRatepayersList',
})

const { t } = useI18n()

const route = useRoute()

const router = useRouter()

const { hasPermission, hasAnyPermission } = usePermissions()

// 列配置
const columns = ref<TableColumnItem[]>([
  {
    field: 'property.property_number',
    header: 'Property Number',
    template: 'property_number',
    style: { minWidth: '140px' },
  },
  { field: 'status', header: 'Status', template: 'status', style: { minWidth: '90px' } },
  { field: 'customer.user_profile.first_name', header: 'First Name', style: { minWidth: '120px' } },
  { field: 'customer.user_profile.last_name', header: 'Last Name', style: { minWidth: '120px' } },
  { field: 'property.street_address', header: 'Address', style: { minWidth: '200px' } },
  { field: 'property.postcode', header: 'Postcode', style: { minWidth: '100px' } },
  { field: 'customer.customer_user.email', header: 'Email', style: { minWidth: '120px' } },
  { field: 'customer.customer_user.mobile', header: 'Mobile', style: { minWidth: '120px' } },
  { field: 'action', header: '', template: 'action', alignFrozen: 'right', frozen: true, style: { width: '50px' } },
])

// 使用 useRequestList 处理客户列表数据
const {
  list,
  loading,
  total,
  refresh,
  search,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
  loading: isListLoading,
  setSearchParams,
} = useRequestList<Ratepayer.Info[], Api.RatepayerListReq>({
  requestFn: ratepayerApi.getList,
  immediate: false,
})

// Setup export functionality
const { isExporting, handleExport } = useExport({
  exportFn: ratepayerApi.exportRatepayers,
  getParams: () => {
    return setSearchParams(searchModel.value)
  },
  onExportStart: () => {
    window.$toast.add({
      severity: 'info',
      summary: 'Export Started',
      detail: 'Preparing your export file...',
    })
  },
})

// 使用通用的列表刷新逻辑
useListRefresh('flexiratesMerchantRatepayersList', refresh)

const handleSort = (event: Record<string, any>) => {
  const { sortField, sortOrder } = event
  setSearchParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  search()
}

const searchModel = ref<Partial<Api.CustomerListReq>>({
  'keyword': '',
  'status': null,
  'created_at[]': [],
})

const tableSelection = ref([])

const customerTable = ref()

const confirmVisible = ref(false)

const statusOptions = ref<DictItem[]>([])

// Get customer status and type options
const { loading: isCustomerStatusLoading } = useDict('subscription_status_filter', (res) => {
  statusOptions.value = addAllToDict(res, { label: 'All', value: null })
})

// 配置搜索字段
const searchFields = computed(() => [
  {
    name: 'keyword',
    label: 'What are you looking for?',
    type: SearchFieldType.TEXT,
    placeholder: 'Search by name, email, property number',
    maxlength: 50,
    defaultValue: '',
  },
  {
    name: 'status',
    label: 'Status',
    type: SearchFieldType.SELECT,
    placeholder: 'All',
    options: statusOptions.value,
    loading: isCustomerStatusLoading,
    defaultValue: null,
  },
])

const moreSearchFields = computed(() => [
  {
    name: 'created_at[]',
    label: 'Created At',
    type: SearchFieldType.DATE_RANGE,
    placeholder: 'Please select date range',
    defaultValue: [],
  },
])

// 对话框控制
const deleteCustomerDialog = ref(false)

const customer = ref<Partial<Customer.Info>>({
  id: '',
  name: '',
  email_primary: '',
})

const navigateToDetail = (data: DataTableRowClickEvent<Ratepayer.Info>) => {
  router.push({
    name: 'flexiratesMerchantRatepayersDetail',
    params: { id: data?.data?.id },
    query: {
      customerId: data?.data?.customer_id,
    },
  })
}

// const navigateToCreate = () => {
//   router.push({ name: 'flexiratesMerchantRatepayersCreate' })
// }

// 删除客户
const deleteCustomer = () => {
  deleteCustomerDialog.value = false
  // customerApi.remove([customer.value.customer_id!]).then((res) => {
  //   if (res.code === 0) {
  //     window.$toast.add({ severity: 'success', summary: t('common.success'), detail: t('customersPage.messages.customerDeleted'), life: 3000 })
  //     refresh()
  //   }
  // })

  customer.value = {
    id: '',
    name: '',
    email_primary: '',
  } as Partial<Customer.Info>
}

const currentPropertyData = ref()

const cancelLoading = ref(false)
const cancelRegistration = async () => {
  if (!currentPropertyData.value) {
    return
  }
  cancelLoading.value = true
  try {
    const res = await supportApi.cancelRequest({ property_id: currentPropertyData.value.related_id })
    if (res.code === 0) {
      confirmVisible.value = false
      window.$toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Cancel registration successful',
      })
      refresh()
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    cancelLoading.value = false
  }
}

const addNotesVisible = ref(false)
const notesModel = ref({
  customer_id: '',
  content: '',
  title: '',
})
const schema = toTypedSchema(yup.object({
  content: yup.string().required('Please enter Details'),
  title: yup.string().required('Please enter Title'),
}))

const saveNoteLoading = ref(false)
const onNotesSubmit = async (values: any) => {
  saveNoteLoading.value = true
  const sendData = {
    customer_id: currentPropertyData.value.customer_id,
    content: values.content,
    title: values.title,
  }
  try {
    const res = await ratepayerApi.createRatepayerNote(sendData)
    if (res.code === 0) {
      window.$toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Add note successful',
      })
    }
    addNotesVisible.value = false
  }
  catch (error) {
    console.log(error)
  }
  finally {
    saveNoteLoading.value = false
  }
}
// 搜索处理
const handleSearch = () => {
  setSearchParams(searchModel.value)
  search()
}

const getStatusColor = (status: number) => {
  if (status === 1) {
    return '#39B54A'
  }
  if (status === 2) {
    return '#FF5F00'
  }
  if (status === 3) {
    return '#0073CF'
  }
  if (status === 4) {
    return '#EB001B'
  }
  return '#545454'
}

onActivated(() => {
  const query = route.query
  if (query && Object.keys(query).length > 0) {
    // First update the searchModel with properly converted values
    searchModel.value = {
      'keyword': typeof query.keyword === 'string' ? query.keyword : '',
      'status': query.status ? Number(query.status) : null,
      'created_at[]': Array.isArray(query['created_at[]']) ? query['created_at[]'] as string[] : [],
    }

    // Then set search params and execute search
    setSearchParams(searchModel.value)
    refresh()
  }
  else {
    searchModel.value = {
      'keyword': '',
      'status': null,
      'created_at[]': [],
    }
    setSearchParams(searchModel.value)
    refresh()
  }
})
</script>

<template>
  <div class="customer-page">
    <BaseSearch
      v-model="searchModel" :loading="isListLoading" :basic-search-fields="searchFields"
      :advanced-search-fields="moreSearchFields" @search="handleSearch"
    />

    <div
      v-if="hasAnyPermission([Permissions.RATE_PAYER_CREATE, Permissions.RATE_PAYER_EXPORT])"
      class="flex justify-end gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8"
    >
      <BaseExportDialog
        v-if="hasPermission(Permissions.RATE_PAYER_EXPORT)" :loading="isListLoading"
        :export-loading="isExporting" @export="handleExport"
      />
      <!-- <Button
        v-if="hasPermission(Permissions.RATE_PAYER_CREATE)" label="ADD NEW CUSTOMER" severity="warn" class="!px-8"
        @click="navigateToCreate"
      /> -->
    </div>

    <!-- 客户表格 -->
    <BaseDataTable
      ref="customerTable" v-model:selection="tableSelection"
      :show-search-bar="false" :value="list" :columns="columns" :scrollable="true" :show-multiple-column="false"
      :loading="loading" :paginator="true" :rows="50" :total-records="total" data-key="id" :failed="failed"
      :failure-message="failureMessage" :striped-rows="false" style="--frozen-column-border-bottom : -8px"
      :is-show-expander="false" :row-hover="true" @change-search="handleSearch"
      @page="(e: DataTablePageEvent) => handlePageChange(e)" @row-click="navigateToDetail"
      @sort="handleSort"
    >
      <template #created_at="{ data }">
        {{ formatDate(data.created_at) }}
      </template>
      <template #property_number="{ data }">
        <span class="underline">
          {{ data?.property?.property_number }}
        </span>
      </template>
      <template #status="{ data }">
        <span
          class="font-medium"
          :style="{
            color: getStatusColor(data.status),
          }"
        >
          {{ data?.status_desc }}
        </span>
      </template>
      <template #last_payment="{ data }">
        {{ formatDate(data.last_payment) }}
      </template>
      <template #name="{ data }">
        <span>
          {{ data?.name }}
        </span>
      </template>
      <template #default_payment_method="{ data }">
        <div class="flex items-center">
          <BaseCardType
            :card-type="data?.customer_banking[0]?.credit_brand"
            :text="data?.customer_banking[0]?.account_no" :is-show-card-number="true"
          />
        </div>
      </template>
      <template #action="{ data }">
        <BaseDataTableActions
          content-width="150px" :is-show-detail="false"
          :loading="data.__loading"
        >
          <div
            v-if="hasAnyPermission([
              Permissions.RATE_PAYER_DETAIL,
              Permissions.RATE_PAYER_UPDATE,
            ])" class="flex flex-col gap-2"
          >
            <!-- <Button
              v-if="hasPermission(Permissions.RATE_PAYER_DETAIL)" severity="secondary"
              @click="navigateToDetail(data)"
            >
              Details
            </Button> -->
            <Button
              severity="secondary"
              @click="$router.push({ name: 'flexiratesMerchantPropertiesDetail', params: { id: data.id } })"
            >
              Edit Account
            </Button>
            <Button
              severity="secondary" @click="() => {
                currentPropertyData = data
                confirmVisible = true
              }"
            >
              Cancel Registration
            </Button>
            <Button severity="secondary" @click="addNotesVisible = true">
              Add Notes
            </Button>
          </div>
        </BaseDataTableActions>
      </template>
    </BaseDataTable>
    <CustomDialog :visible="confirmVisible" @update:visible="(val) => (confirmVisible = val)">
      <template #content>
        <div class="w-58">
          <div class="text-center text-[#031f73] text-2xl font-semibold">
            Are you sure you want to cancel Registration?
          </div>
          <div class="mt-8 flex flex-col gap-3">
            <Button label="YES" severity="warn" class="w-full" :loading="isExporting" @click="cancelRegistration" />
            <Button label="CANCEL" class="w-full" @click="confirmVisible = false" />
          </div>
        </div>
      </template>
    </CustomDialog>
    <CustomDialog
      :visible="addNotesVisible" title="Add Notes and Remarks"
      @update:visible="(val) => (addNotesVisible = val)"
    >
      <template #content>
        <div>
          <VeeForm
            :initial-values="notesModel" :validation-schema="schema" class="pt-4 text-xl flex flex-col gap-y-6"
            @submit="onNotesSubmit"
          >
            <div class="flex items-center">
              <div class="w-[150px]">
                Date and Time
              </div>
              <div>
                {{ dayjs().format('DD MMM YYYY hh:mm') }}
              </div>
            </div>
            <Field v-slot="{ field, handleChange, errorMessage }" v-model="notesModel.title" as="div" name="title">
              <div class="relative flex items-center">
                <div class="min-w-[150px]">
                  Title
                </div>
                <InputText
                  v-model="field.value" placeholder="Account Notes" type="text" class="w-full"
                  @value-change="handleChange"
                />
                <Message v-if="errorMessage" severity="error" variant="simple" class="absolute left-[150px] top-12">
                  {{ errorMessage }}
                </Message>
              </div>
            </Field>

            <Field v-slot="{ field, handleChange, errorMessage }" v-model="notesModel.content" as="div" name="content">
              <div>
                <div class="mb-2">
                  Details:
                </div>
                <Textarea
                  v-model="field.value" auto-resize rows="6" cols="50"
                  placeholder="Reg Cancelled due to 3 dishonours/declines" @value="handleChange"
                />
                <Message v-if="errorMessage" severity="error" variant="simple" class="mt-2">
                  {{ errorMessage }}
                </Message>
              </div>
            </Field>

            <div class="flex justify-end gap-4">
              <Button label="CANCEL" class="btn" @click="addNotesVisible = false" />
              <Button label="SAVE" class="btn" severity="warn" type="submit" :loading="saveNoteLoading" />
            </div>
          </VeeForm>
        </div>
      </template>
    </CustomDialog>
    <!-- 删除确认对话框 -->
    <Dialog
      v-model:visible="deleteCustomerDialog" :style="{ width: '450px' }"
      :header="t('customersPage.dialogs.confirmDelete')" :modal="true"
    >
      <div class="confirmation-content">
        <i class="pi pi-exclamation-triangle mr-3" style="font-size: 2rem" />
        <span v-if="customer">{{ t('customersPage.dialogs.deleteConfirmMessage', { name: customer.name }) }}</span>
      </div>
      <template #footer>
        <Button :label="t('common.no')" icon="pi pi-times" text @click="deleteCustomerDialog = false" />
        <Button :label="t('common.yes')" icon="pi pi-check" text @click="deleteCustomer" />
      </template>
    </Dialog>
  </div>
</template>

<style lang="scss" scoped>
.btn {
  width: 150px;
  display: block;
}

.p-dialog .p-dialog-header {
  border-bottom: 1px solid #dee2e6;
  padding: 1.5rem;
}

.p-dialog .p-dialog-footer {
  border-top: 1px solid #dee2e6;
  padding: 1.5rem;
  text-align: right;
}

.p-dialog .p-dialog-content {
  padding: 2rem;
}

.confirmation-content {
  display: flex;
  align-items: center;
}

.table-menu {
  font-size: 16px;

  .menu-item {
    cursor: pointer;
    padding: 10px 2px;
    border-bottom: 1px solid #545454;

    &:last-child {
      border-bottom: none;

    }

    &:hover {
      background-color: #f8f9fa;
    }
  }

  .cancel {
    color: #eb001b;
  }

  .add {
    color: #0073cf;
  }
}
</style>
