<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import type { DictItem } from '@/services/api/dict'
import { Format } from '@shared'
import { onActivated, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import BaseDataTable from '@/components/common/BaseDataTable.vue'
import { useListRefresh } from '@/composables/useListRefresh'
import { useRequestList } from '@/composables/useRequestList'
import { payout } from '@/services/api'
import { getPayoutTagStatus } from '@/utils/tagStatus'

defineOptions({
  name: 'payoutList',
})

const route = useRoute()

const router = useRouter()

const columns = ref<TableColumnItem[]>([
  { field: 'currency', header: 'Currency', style: { minWidth: '100px' } },
  { field: 'amount', header: 'Gross', style: { minWidth: '120px' }, template: 'amount' },
  { field: 'payment_amount', header: 'Payout', style: { minWidth: '120px' } },
  { field: 'account_name', header: 'Account name', style: { minWidth: '150px' } },
  { field: 'bsb', header: 'BSB', style: { minWidth: '100px' } },
  { field: 'account_no', header: 'Account', style: { minWidth: '150px' } },
  { field: 'arrive_by', header: 'Arrive by', style: { minWidth: '150px' } },
  { field: 'status', header: 'Status', template: 'status', style: { minWidth: '120px' } },
])

const {
  list,
  loading,
  total,
  refresh,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
  search,
  setSearchParams,
} = useRequestList<Payout.Info[], Api.PayoutListReq>({
  requestFn: payout.getList,
  immediate: false,
})

useListRefresh('payoutList', refresh)

const payoutStatusOptions = ref<DictItem[]>([
  {
    label: 'All',
    value: '',
  },
  {
    label: 'Pending',
    value: 0,
  },
  {
    label: 'Success',
    value: 1,
  },
  {
    label: 'Fail',
    value: 2,
  },
  {
    label: 'Processing',
    value: 3,
  },
])

const getPayoutStatusLabel = (value: number) => {
  return payoutStatusOptions.value.find(item => item.value === value)?.label || ''
}

const handleRowClick = (e: { data: Payout.Info }) => {
  router.push({
    name: 'transactionsList',
    query: {
      'created_at[]': [e.data.arrive_by, e.data.arrive_by],
    },
  })
}

onActivated(() => {
  const query = route.query
  if (query && query.status !== undefined) {
    setSearchParams({
      status: Number(query.status),
    })

    search()
  }
  else {
    setSearchParams({
      status: undefined,
    })
    search()
  }
})
</script>

<template>
  <div class="payout-list-page">
    <!-- 支付表格 -->
    <BaseDataTable
      :value="list" :columns="columns" :scrollable="true"
      :show-multiple-column="false" :loading="loading" :paginator="true" :rows="50" :total-records="total" :lazy="true"
      data-key="id" :show-search-bar="false" :failed="failed" :failure-message="failureMessage" :striped-rows="true" :row-hover="true"
      @page="(e: DataTablePageEvent) => handlePageChange(e)" @row-click="(e: any) => handleRowClick(e)"
    >
      <template #status="{ data }">
        <BaseTag :text="getPayoutStatusLabel(data.status)" :type="getPayoutTagStatus(data.status)" />
      </template>
      <template #amount="{ data }">
        {{ Format.formatAmount(data?.amount) }}
      </template>
    </BaseDataTable>
  </div>
</template>

<style scoped>
.p-datatable .p-datatable-tbody>tr>td {
  padding: 0.75rem 1rem;
}

.p-datatable .p-datatable-thead>tr>th {
  background-color: #f8f9fa;
  font-weight: 600;
}
</style>
