<script setup lang="ts">
import type { FormSubmitEvent } from '@primevue/forms'
import { useUserStore } from '@/store/modules/user'
import { Form } from '@primevue/forms'
import { zodResolver } from '@primevue/forms/resolvers/zod'
import { useToast } from 'primevue/usetoast'
import { onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { z } from 'zod'

const toast = useToast()
const userStore = useUserStore()
const { t } = useI18n()

const isLoading = ref(false)
const isSaving = ref(false)

const resolver = ref(zodResolver(
  z.object({
    name: z.string().min(1, t('user.profileSettings.nameRequired')),
    nickname: z.string().optional(),
    email: z.string().email(t('user.profileSettings.emailInvalid')),
    mfa_check: z.boolean().optional(),
  }),
))

const initialValues = ref({
  avatar: '',
  name: '',
  email: '',
  merchant_id: '',
  mfa_check: false,
})

const logo = ref<FileUpload.UploadFileItem[]>([])

const fetchUserInfo = async () => {
  isLoading.value = true
  try {
    const userData = await userStore.getUserInfo()
    if (userData) {
      initialValues.value.name = userData.name
      initialValues.value.email = userData.email as string
      initialValues.value.merchant_id = userData.merchant_id as string
      initialValues.value.mfa_check = !!userData.mfa_check
      initialValues.value.avatar = userData.avatar || ''
    }

    if (userData.avatar) {
      logo.value = [
        {
          url: userData.avatar,
        },
      ]
    }
    else {
      logo.value = []
    }
  }
  finally {
    isLoading.value = false
  }
}

const onSubmit = async ({ valid, values }: FormSubmitEvent) => {
  if (!valid) { return }
  isSaving.value = true
  try {
    await userStore.updateUserInfo({
      user_name: values.name,
      email: values.email,
      avatar: values.avatar,
      mfa_check: values.mfa_check ? 1 : 0,
    })
    fetchUserInfo()
    toast.add({ severity: 'success', summary: t('common.success'), detail: t('user.profileSettings.updateSuccess'), life: 3000 })
  }
  finally {
    isSaving.value = false
  }
}

const uploadAvatarSuccess = async (event: { response: CommonRes<{ file_path: [string] }> }) => {
  const url = event.response.data?.file_path[0] || ''

  if (!url) {
    toast.add({ severity: 'error', summary: t('common.error'), detail: t('user.profileSettings.uploadError'), life: 3000 })
    return
  }

  initialValues.value.avatar = url

  isSaving.value = true
  try {
    await userStore.updateUserInfo({
      user_name: initialValues.value.name,
      email: initialValues.value.email,
      mfa_check: initialValues.value.mfa_check ? 1 : 0,
      avatar: url,
    })
    await fetchUserInfo()
    initialValues.value.avatar = url
    toast.add({ severity: 'success', summary: t('common.success'), detail: t('user.profileSettings.updateSuccess') })
  }
  finally {
    isSaving.value = false
  }
}

const cancelEdit = () => {
  fetchUserInfo()
}

// 加载用户数据
onMounted(async () => {
  fetchUserInfo()
})
</script>

<template>
  <div class="profile-page">
    <!-- 顶部背景区域 -->
    <div class="profile-header">
      <div class="profile-info">
        <div class="avatar-wrapper">
          <BaseFileUpload
            v-model="logo"
            mode="avatar"
            :multiple="false"
            :auto-upload="true"
            accept="image/jpeg,image/png,.jpg,.jpeg,.png"
            :max-size="1024 * 1024 * 10"
            class="avatar-uploader"
            @success="uploadAvatarSuccess"
          />
          <div class="profile-name">
            <h2>{{ initialValues.name }}</h2>
            <p>{{ initialValues.email }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 表单区域 -->
    <div class="profile-form">
      <div v-if="isLoading" class="loading-container">
        <ProgressSpinner />
      </div>

      <template v-else>
        <Form :initial-values="initialValues" :resolver class="max-w-200 space-y-6" @submit="onSubmit">
          <FormField name="merchant_id" class="flex flex-col gap-3">
            <label for="merchant_id">merchant id</label>
            <InputText :model-value="initialValues.merchant_id" :disabled="true" maxlength="50" />
          </FormField>
          <FormField v-slot="$field" name="name" required class="flex flex-col gap-3">
            <label for="name">Name</label>
            <InputText :placeholder="t('user.profileSettings.namePlaceholder')" maxlength="50" />
            <Message v-if="$field?.invalid" severity="error" size="small" variant="simple">
              {{ $field.error?.message }}
            </Message>
          </FormField>
          <FormField v-slot="$field" name="email" required class="flex flex-col gap-3">
            <label for="email">{{ t('user.profileSettings.email') }}</label>
            <InputText :placeholder="t('user.profileSettings.emailPlaceholder')" maxlength="50" />
            <Message v-if="$field?.invalid" severity="error" size="small" variant="simple">
              {{ $field.error?.message }}
            </Message>
          </FormField>
          <FormField name="mfa_check" class="flex flex-col gap-3 col-span-2">
            <div class="flex align-items-center">
              <Checkbox input-id="mfa_check" name="mfa_check" binary />
              <label
                for="mfa_check"
                class="ml-2"
              >{{ t('user.profileSettings.enable2FA') || 'Enable Two-Factor Authentication' }}</label>
            </div>
            <small class="text-gray-500">
              {{ t('user.profileSettings.2FADescription') || 'Enable two-factor authentication to add an extra layer of security to your account.' }}
            </small>
          </FormField>

          <!-- 操作按钮 -->
          <div class="flex gap-4 flex-2">
            <Button :label="t('user.profileSettings.saveChanges')" severity="warn" :loading="isSaving" type="submit" />
            <Button :label="t('user.profileSettings.cancel')" severity="secondary" type="button" @click="cancelEdit" />
          </div>
        </Form>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.profile-page {
  min-height: 80vh;
}

:deep(.avatar-uploader) {
  .p-fileupload-avatar-dropzone {
    width: 85px;
    height: 85px;
    box-shadow: none;
  }

  span {
    text-align: center;
  }

  .pi-user {
    display: none;
  }
}

.profile-header {
  background: linear-gradient(90deg, #e3f2fd 0%, #fff3e0 100%);
  padding: 2rem;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;

  .profile-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 auto;

    .avatar-wrapper {
      display: flex;
      align-items: center;
      gap: 1rem;

      .profile-avatar {
        width: 100px;
        height: 100px;
        position: relative;
        cursor: pointer;

        :deep(.p-avatar) {
          border-radius: 50%;
          width: 100%;
          height: 100%;
        }

        .avatar-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          opacity: 0;
          transition: all 0.3s ease;
          border-radius: 50%;

          &.uploading {
            opacity: 1;
          }
        }

        &:hover {
          .avatar-overlay {
            opacity: 1;
          }
        }
      }

      .profile-name {
        h2 {
          margin: 0;
          font-size: 1.5rem;
          color: #1e293b;
        }

        p {
          margin: 0.5rem 0 0;
          color: #64748b;
        }
      }
    }
  }
}

.profile-form {
  margin: 0 auto;
  margin-top: -1rem;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .form-group {
    margin-bottom: 1.5rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      color: #475569;
    }

    .p-error {
      display: block;
      margin-top: 0.5rem;
      color: #ef4444;
    }
  }

  .email-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;

    h3 {
      color: #1e293b;
      margin-bottom: 1rem;
    }
  }

  .email-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f5f9;

    .email-text {
      flex: 1;
      color: #334155;
    }

    .email-date {
      color: #94a3b8;
      font-size: 0.875rem;
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    padding: 2rem;
  }
}
</style>
