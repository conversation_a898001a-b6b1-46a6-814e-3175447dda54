<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import type { DictItem } from '@/services/api/dict'
// import { toTypedSchema } from '@vee-validate/yup'
import dayjs from 'dayjs'
// import { useExport } from '@/composables/useExport'
// import { Field, Form as VeeForm } from 'vee-validate'
import { computed, ref } from 'vue'
// import * as yup from 'yup'
// import { useRoute } from 'vue-router'
import BaseDataTable from '@/components/common/BaseDataTable.vue'
// import BaseExportDialog from '@/components/common/BaseExportDialog.vue'
import BaseSearch from '@/components/common/BaseSearch.vue'
// import CustomDialog from '@/components/customDialog/index.vue'
import { useDict } from '@/composables/useDict'
import { useListRefresh } from '@/composables/useListRefresh'
import { useRequestList } from '@/composables/useRequestList'
import { SearchFieldType } from '@/constants/search'
import { support as supportApi } from '@/services/api'
import { addAllToDict } from '@/utils/dict'

defineOptions({
  name: 'flexiratesMerchantCancelList',
})

// const route = useRoute()

// const router = useRouter()

// 列配置
const columns = ref<TableColumnItem[]>([
  { field: 'customer_property.property_number', header: 'Property Number', style: { minWidth: '100px' }, template: 'property' },
  { field: 'customer_property.street_address', header: 'Address', style: { minWidth: '150px' }, template: 'address' },
  { field: 'customer_user.name', header: 'Customer', style: { minWidth: '150px' }, template: 'customer' },
  { field: 'customer_property.current_remaining_amount', header: 'Amount', style: { minWidth: '100px' }, template: 'amount' },
  { field: 'customer_plan.account_no', header: 'Account', style: { minWidth: '150px' }, template: 'account' },
  { field: 'status', header: 'Status', style: { minWidth: '100px' }, template: 'status' },
  { field: 'remark', header: 'Notes and Remarks', style: { minWidth: '200px' }, template: 'notes' },
  // { field: 'action', header: '', template: 'action', alignFrozen: 'right', frozen: true, style: { width: '50px' } },
])

// 使用 useRequestList 处理客户列表数据
const {
  list,
  loading,
  total,
  refresh,
  search,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
  loading: isListLoading,
  setSearchParams,
} = useRequestList({
  requestFn: supportApi.getCancelList,
  immediate: true,
})

// Setup export functionality
// const { isExporting, handleExport } = useExport({
//   exportFn: ratepayerApi.exportRatepayers,
//   getParams: () => {
//     return setSearchParams(searchModel.value)
//   },
//   onExportStart: () => {
//     window.$toast.add({
//       severity: 'info',
//       summary: 'Export Started',
//       detail: 'Preparing your export file...',
//       life: 3000,
//     })
//   },
// })

// 使用通用的列表刷新逻辑
useListRefresh('flexiratesMerchantRatepayersList', refresh)

const handleSort = (event: Record<string, any>) => {
  const { sortField, sortOrder } = event
  setSearchParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  search()
}

const searchModel = ref<any>({
  'keyword': '',
  'status': null,
  'created_at[]': [],
})

const customerTable = ref()

// const auditOptions = ref<DictItem[]>([
//   { label: 'Approved', value: 2 },
//   { label: 'Rejected', value: 3 },
// ])

// const auditModel = ref({
//   status: '',
//   remark: '',
// })
// const schema = toTypedSchema(yup.object({
//   status: yup.string().required('Please select the review result'),
// }))

const statusOptions = ref<DictItem[]>([])

const { getLabel: getStatusLabel } = useDict('property_cancellation_status', (res) => {
  statusOptions.value = addAllToDict(res, { label: 'All', value: null })
})

// 配置搜索字段
const searchFields = computed(() => [
  {
    name: 'keyword',
    label: 'Search',
    type: SearchFieldType.TEXT,
    placeholder: 'Property Number',
    maxlength: 50,
    defaultValue: '',
  },
  {
    name: 'status',
    label: 'Status',
    type: SearchFieldType.SELECT,
    placeholder: 'All',
    options: statusOptions.value,
    // loading: isCustomerStatusLoading,
    defaultValue: null,
  },
])

const moreSearchFields = computed(() => [
  {
    name: 'created_at[]',
    label: 'Date range',
    type: SearchFieldType.DATE_RANGE,
    placeholder: 'Please select date range',
    defaultValue: [],
  },
])

// 对话框控制
// const deleteCustomerDialog = ref(false)
// const addNotesVisible = ref(false)
// const confirmationVisible = ref(false)

// const auditLoading = ref(false)

// const notesData = ref()

// const handleAddNotes = (data: any) => {
//   notesData.value = data
//   addNotesVisible.value = true
// }

// const onAuditSubmit = () => {
//   confirmationVisible.value = true
// }

// const auditCancelRequest = async () => {
//   auditLoading.value = true
//   try {
//     const res = await supportApi.audit({
//       id: notesData.value.id,
//       status: Number(auditModel.value.status),
//       remark: auditModel.value.remark,
//     })
//     if (res.code === 0) {
//       confirmationVisible.value = false
//       addNotesVisible.value = false
//       auditModel.value = {
//         status: '',
//         remark: '',
//       }
//       window.$toast.add({
//         severity: 'success',
//         summary: 'Success',
//         detail: 'Audit cancel request successfully!',
//       })
//     }
//   }
//   catch (error) {
//     console.log(error)
//   }
//   finally {
//     auditLoading.value = false
//   }
// }

// 搜索处理
const handleSearch = () => {
  setSearchParams(searchModel.value)
  search()
}
</script>

<template>
  <div class="customer-page">
    <!-- :advanced-search-fields="moreSearchFields" -->
    <BaseSearch
      v-model="searchModel" :loading="isListLoading" :basic-search-fields="searchFields"
      :advanced-search-fields="moreSearchFields" @search="handleSearch"
    />

    <!-- <div class="flex justify-end gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8">
      <BaseExportDialog :loading="isListLoading" :export-loading="isExporting" @export="handleExport" />
    </div> -->

    <!-- 客户表格 -->
    <div class="bg-white rounded-[16px]">
      <BaseDataTable
        ref="customerTable" :show-search-bar="false" :value="list"
        :columns="columns" :scrollable="true" :show-multiple-column="false" :loading="loading" :paginator="false"
        :rows="50" :total-records="total" data-key="id" :failed="failed" :failure-message="failureMessage" :row-hover="true"
        :striped-rows="false" style="--frozen-column-border-bottom : -8px" :is-show-expander="false"
        @change-search="handleSearch" @page="(e: DataTablePageEvent) => handlePageChange(e)" @sort="handleSort"
      >
        <template #property="{ data }">
          <span class="underline">
            {{ data?.customer_property?.property_number }}
          </span>
        </template>
        <template #address="{ data }">
          <span>{{ data?.customer_property?.street_address }}</span>
        </template>
        <template #customer="{ data }">
          <div class="flex flex-col gap-2">
            <span>
              <span class="font-semibold">Name: </span>
              <span>{{ data?.customer_user?.name }}</span>
            </span>
            <span>
              <span class="font-semibold">Email: </span>
              <span>{{ data?.customer_user?.email }}</span>
            </span>
            <span>
              <span class="font-semibold">Mobile: </span>
              <span>{{ data?.customer_user?.mobile }}</span>
            </span>
          </div>
        </template>
        <template #amount="{ data }">
          $<span>{{ data?.customer_property?.current_remaining_amount }}</span>
        </template>
        <template #account="{ data }">
          <div class="flex flex-col gap-2">
            <span>
              <span class="font-semibold">Card No: </span>
              <span>{{ data?.customer_plan?.customer_banking?.account_no }}</span>
            </span>
            <span>
              <span class="font-semibold">Expiry: </span>
              <span>{{ data?.customer_plan?.customer_banking?.expiration_month }}/{{
                data?.customer_plan?.customer_banking?.expiration_year }}</span>
            </span>
            <span>
              <span class="font-semibold">Name: </span>
              <span>{{ data?.customer_plan?.customer_banking?.first_name }} {{
                data?.customer_plan?.customer_banking?.last_name }}</span>
            </span>
          </div>
        </template>
        <template #status="{ data }">
          {{ getStatusLabel(data?.status) }}
        </template>
        <template #notes="{ data }">
          <div class="flex flex-col gap-2">
            <span>
              <span class="font-semibold">Stopped Date: </span>
              <span>
                {{ data?.customer_plan?.stopped_date ? dayjs(data?.customer_plan?.stopped_date).format('DD MMM YYYY')
                  : ''
                }}</span>
            </span>
            <span>
              <span class="font-semibold">Reason: </span>
              <span>{{ data?.remark }}</span>
            </span>
          </div>
        </template>
        <!-- <template #action="{ data }">
          <Button v-if="data?.status === 1" icon="pi pi-plus" rounded variant="text" style="font-weight: 900;"
            @click="handleAddNotes(data)" />
        </template> -->
      </BaseDataTable>
      <div v-if="total > 10" class="flex justify-center py-6">
        <Button label="VIEW MORE" />
      </div>
    </div>
    <!-- <div class="dialog">
      <CustomDialog
        :visible="addNotesVisible" title="Add Notes and Remarks"
        @update:visible="(val) => (addNotesVisible = val)"
      >
        <template #content>
          <div>
            <VeeForm
              :initial-values="auditModel" :validation-schema="schema" class="pt-4 text-xl flex flex-col gap-y-6"
              @submit="onAuditSubmit"
            >
              <Field v-slot="{ field, handleChange, errorMessage }" v-model="auditModel.status" as="div" name="status">
                <Select
                  v-model="field.value" :options="auditOptions" option-label="label" option-value="value"
                  placeholder="Select a result" class="w-full" @value-change="handleChange"
                />
                <Message v-if="errorMessage" severity="error" variant="simple">
                  {{ errorMessage }}
                </Message>
              </Field>

              <Field v-slot="{ field, handleChange }" v-model="auditModel.remark" as="div" name="remark">
                <div>
                  <div class="mb-2">
                    Reason:
                  </div>
                  <Textarea
                    v-model="field.value" auto-resize rows="6" cols="50"
                    placeholder="Reg Cancelled due to 3 dishonours/declines" @value="handleChange"
                  />
                </div>
              </Field>

              <div class="flex justify-end gap-4">
                <Button label="CANCEL" class="btn" @click="addNotesVisible = false" />
                <Button label="SAVE" class="btn" severity="warn" type="submit" />
              </div>
            </VeeForm>
          </div>
        </template>
      </CustomDialog>
      <CustomDialog :visible="confirmationVisible" @update:visible="(val) => (confirmationVisible = val)">
        <template #content>
          <div class="w-[180px] ">
            <div class="font-bold text-2xl text-center text-[#031f73]">
              Are you sure you want to {{ Number(auditModel.status) === 2 ? 'Approved' : 'Rejected' }} this Cancel
              Request?
            </div>
            <div class="flex flex-col gap-y-4 mt-8">
              <Button class="w-full" label="CANCEL" @click="confirmationVisible = false" />
              <Button class="w-full" label="YES" severity="warn" :loading="auditLoading" @click="auditCancelRequest" />
            </div>
          </div>
        </template>
      </CustomDialog>
    </div> -->
  </div>
</template>

<style lang="scss" scoped>
.btn {
  display: block;
  padding: 8px 16px;
  width: 140px;
}
</style>
