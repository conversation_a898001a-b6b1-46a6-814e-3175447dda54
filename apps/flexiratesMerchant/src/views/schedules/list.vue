<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import type { DictItem } from '@/services/api/dict'

import { Format } from '@shared'
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import BaseDataTable from '@/components/common/BaseDataTable.vue'
import { useDict } from '@/composables/useDict'
import { useExport } from '@/composables/useExport'
import { useListRefresh } from '@/composables/useListRefresh'
import { usePermissions } from '@/composables/usePermissions'
import { useRequestList } from '@/composables/useRequestList'
import { Permissions } from '@/constants/permissions'
import { SearchFieldType } from '@/constants/search'
import { schedules as schedulesApi } from '@/services/api'
import { addAllToDict } from '@/utils/dict'

defineOptions({
  name: 'flexiratesMerchantScheduleList',
})

// 使用 useRequestList 处理商户列表数据
const requestList = useRequestList<Schedules.Info, Api.ScheduleListReq>({
  requestFn: schedulesApi.getList,
})

const router = useRouter()

const { hasPermission } = usePermissions()

const {
  list,
  loading,
  total,
  refresh,
  setSearchParams,
  search,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
} = requestList

// 使用通用的列表刷新逻辑
useListRefresh('flexiratesMerchantScheduleList', refresh)

// Setup export functionality
const { isExporting, handleExport } = useExport({
  exportFn: schedulesApi.exportSchedules,
  getParams: () => {
    return setSearchParams(searchModel.value)
  },
  onExportStart: () => {
    window.$toast.add({
      severity: 'info',
      summary: 'Export Started',
      detail: 'Preparing your export file...',
      life: 3000,
    })
  },
})

// 列配置
const columns = ref<TableColumnItem[]>([
  {
    field: 'property.property_number',
    header: 'Property Number',
    style: { minWidth: '80px' },
  },
  {
    template: 'status',
    field: 'status',
    header: 'Status',
    style: { minWidth: '80px' },
  },
  {
    template: 'process_type',
    field: 'process_type',
    header: 'Frequency',
    style: { minWidth: '80px' },
  },
  {
    field: 'customer.name',
    header: 'Customer Name',
    style: { minWidth: '80px' },
  },
  {
    field: 'property.street_address',
    header: 'Address',
    style: { minWidth: '80px' },
  },
  {
    template: 'total_rate_amount',
    field: 'property.initial_remaining_amount',
    header: 'Total Rate Amount',
    style: { minWidth: '80px' },
  },
  {
    template: 'amount_owing',
    field: 'amount_owing',
    header: 'Amount Owing',
    style: { minWidth: '80px' },
  },
  // {
  //   field: '',
  //   header: 'Upcoming Payment',
  //   style: { minWidth: '80px' },
  // },
])

const processTypeOptions = ref<DictItem[]>([])

const statusFilterOptions = ref<DictItem[]>([])

const searchModel = ref<Partial<Api.ScheduleListReq>>({})

// 配置搜索字段
const searchFields = computed(() => [
  {
    name: 'keyword',
    label: 'What are you looking for?',
    type: SearchFieldType.TEXT,
    placeholder: 'Search by name, email, property number',
    maxlength: 100,
    defaultValue: '',
  },
  {
    name: 'status',
    label: 'Status',
    type: SearchFieldType.SELECT,
    placeholder: 'All',
    options: statusFilterOptions.value,
    defaultValue: '',
  },
  {
    name: 'process_type',
    label: 'Process Type',
    type: SearchFieldType.SELECT,
    placeholder: 'All',
    options: processTypeOptions.value,
    defaultValue: '',
  },
])

// 排序处理
const handleSort = (event: any) => {
  const { sortField, sortOrder } = event
  requestList.setParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  requestList.search()
}

// 导航到详情页
const navigateToDetail = ({ data: row }: { data: Schedules.Info }) => {
  if (!hasPermission(Permissions.SCHEDULE_DETAIL)) {
    return
  }
  router.push({
    name: 'flexiratesMerchantSchedulesDetail',
    params: {
      id: row.id,
    },
    query: {
      address: row.property?.street_address,
      status: row.status,
    },
  })
}

// 搜索处理
const handleSearch = () => {
  setSearchParams(searchModel.value as Api.ScheduleListReq)
  search()
}

const getStatusType = (status: number) => {
  if (status === 1) {
    return 'paid'
  }
  if (status === 2) {
    return 'upcoming'
  }
  if (status === 3) {
    return 'info'
  }
  if (status === 4) {
    return 'failed'
  }
  return 'default'
}

const { getLabel: getPaymentPlanLabel } = useDict('payment_plan_show', (res) => {
  processTypeOptions.value = addAllToDict(res)
})

const { getLabel: getStatusLabel } = useDict('subscription_status_filter', (res) => {
  statusFilterOptions.value = addAllToDict(res)
})

// const { options, getLabel: getStatusLabel } = useDict('subscription_status')
</script>

<template>
  <div class="merchant-list-page">
    <BaseSearch v-model="searchModel" :loading="loading" :basic-search-fields="searchFields" @search="handleSearch" />

    <div v-if="hasPermission(Permissions.SCHEDULE_EXPORT)" class="flex justify-end gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8">
      <BaseExportDialog :loading="loading" :export-loading="isExporting" @export="handleExport" />
    </div>

    <BaseDataTable
      :show-multiple-column="false"
      :columns="columns" :value="list" :loading="loading"
      :total-records="total" :paginator="true" :rows="50" :lazy="true" data-key="id" :show-search-bar="false"
      :scrollable="true" search-placeholder="Search merchants..." :failed="failed" :failure-message="failureMessage"
      :striped-rows="false" :row-hover="true" @sort="handleSort" @page="(e: DataTablePageEvent) => handlePageChange(e)"
      @row-click="navigateToDetail"
    >
      <template #total_rate_amount="{ data }">
        <span v-if="data?.property?.initial_remaining_amount">
          {{ Format.formatAmount(data?.property?.initial_remaining_amount) }}
        </span>
      </template>
      <template #amount_owing="{ data }">
        <span v-if="data.amount_owing">
          {{ Format.formatAmount(data?.amount_owing) }}
        </span>
      </template>
      <template #status="{ data }">
        <BaseTag :text="getStatusLabel(data.status)" :type="getStatusType(data.status)" />
      </template>
      <template #process_type="{ data }">
        <BaseTag :text="getPaymentPlanLabel(data.process_type)" />
      </template>
    </BaseDataTable>
  </div>
</template>

<style scoped>
.p-dialog .p-dialog-header {
  border-bottom: 1px solid #dee2e6;
  padding: 1.5rem;
}

.p-dialog .p-dialog-footer {
  border-top: 1px solid #dee2e6;
  padding: 1.5rem;
  text-align: right;
}

.p-dialog .p-dialog-content {
  padding: 2rem;
}

.field {
  margin-bottom: 1.5rem;
}

.confirmation-content {
  display: flex;
  align-items: center;
}
</style>
