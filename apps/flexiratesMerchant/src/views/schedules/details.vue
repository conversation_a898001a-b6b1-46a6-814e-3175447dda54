<script setup lang="ts">
import { Format } from '@shared'
import dayjs from 'dayjs'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useExport } from '@/composables/useExport'
import { schedules as schedulesApi } from '@/services/api'

const route = useRoute()

const isLoading = ref(false)

const { handleExport, isExporting } = useExport({
  exportFn: schedulesApi.exportDetailSchedule,
  getParams: () => ({
    id: route.params.id,
  }),
})

const columns = ref<TableColumnItem[]>([
  {
    field: 'due_date',
    header: 'Due Date',
    style: {
      width: '14%',
    },
    template: 'dueDate',
  },
  {
    field: 'amount',
    header: 'Amount',
    template: 'amount',
    style: {
      width: '14%',
    },
  },
  {
    field: '',
    header: 'Payment Method',
    template: 'paymentMethod',
    style: {
      width: '14%',
    },
  },
  {
    field: 'frequency',
    header: 'Frequency',
    style: {
      width: '14%',
    },
  },
  {
    field: '',
    header: 'Status',
    template: 'status',
    style: {
      width: '14%',
    },
  },
  {
    field: '',
    header: 'Failed Payment',
    template: 'failedPayment',
    style: {
      width: '14%',
    },
  },
  {
    field: '',
    header: 'Edit Count',
    template: 'editCount',
    style: {
      width: '14%',
    },
  },
])

const details = ref<Api.ScheduleDetailRes | []>([])

const fetchDetail = async () => {
  isLoading.value = true
  try {
    const { code, data } = await schedulesApi.getDetail(Number(route.params.id))
    if (code === 0) {
      details.value = data
    }
  }
  finally {
    isLoading.value = false
  }
}

onMounted(() => {
  fetchDetail()
})
</script>

<template>
  <div class="schedule-detail">
    <div class="bg-white dark:bg-gray-800 p-6 px-8 rounded-2xl flex items-center ">
      <div class="header-title">
        <div class="title line-clamp-1">
          <span class="text-(color:--colors-blue)">
            Office:
          </span>
          <span class="text-(color:--colors-gray)">
            {{ route?.query?.address }}
          </span>
        </div>
        <div class="flex flex-col md:flex-row gap-2 md:gap-6">
          <Button class="w-35" label="BACK" severity="warn" @click="$router.back()" />
        </div>
      </div>
    </div>
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 px-8 mt-4">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center gap-2 flex-1">
          <span class="text-(color:--colors-blue) font-bold text-3xl">
            Schedules
          </span>
        </div>
        <div class="flex flex-col md:flex-row gap-2 md:gap-6">
          <Button label="EXPORT" class="w-35" severity="warn" :loading="isExporting" @click="handleExport('csv')" />
        </div>
      </div>

      <BaseDataTable
        :value="details" :columns="columns" :loading="isLoading" :show-multiple-column="false"
        :paginator="false" :show-search-bar="false" data-key="id" @refresh="fetchDetail"
      >
        <template #amount="{ data }">
          {{ Format.formatAmount(data?.amount) }}
        </template>
        <template #dueDate="{ data }">
          <span class="underline">
            {{ dayjs(data?.due_date).format('DD MMM YYYY') }}
          </span>
        </template>
        <template #paymentMethod="{ data }">
          <BaseCardType
            :card-type="data?.credit_brand" :is-show-card-number="true"
            :text="data?.account_no"
          />
        </template>
        <template #failTimes="{ data }">
          {{ Format.formatAmount(data?.fail_times) }}
        </template>
        <template #status="{ data }">
          <span
            class="font-medium" :class="{
              'text-red-500': data.status_desc === 'Failed',
              'text-green-500': data.status_desc === 'Success',
              'text-orange-500': data.status_desc === 'Upcoming',
            }"
          >
            {{ data?.status_desc }}
          </span>
        </template>
        <template #newDate="{ data }">
          {{ dayjs(data.next_process_date).format('DD MMM YYYY') }}
        </template>
        <template #failedPayment="{ data }">
          <span>
            {{ data.failed_times }} of {{ data.failable_times }}
          </span>
        </template>
        <template #editCount="{ data }">
          <span>
            {{ data.editable_count }} of {{ data.editable_count }}
          </span>
        </template>
        <template #empty-action>
          <Button label="Refresh" />
        </template>
      </BaseDataTable>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.schedule-detail {
    .header-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .title {
            flex: 1;
            font-size: 24px;
            font-weight: 600;
            max-width: calc(100% - 100px);
        }
    }
    :deep(.p-datatable-table-container) {
      padding: 0;
    }
}
</style>
