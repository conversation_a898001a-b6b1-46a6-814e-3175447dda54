<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import { Format } from '@shared'
import dayjs from 'dayjs'
import { computed, onActivated, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import BaseDataTable from '@/components/common/BaseDataTable.vue'
import BaseExportDialog from '@/components/common/BaseExportDialog.vue'
import BaseSearch from '@/components/common/BaseSearch.vue'
import { useExport } from '@/composables/useExport'
import { useListRefresh } from '@/composables/useListRefresh'
import { usePermissions } from '@/composables/usePermissions'
import { useRequestList } from '@/composables/useRequestList'
import { Permissions } from '@/constants/permissions'
import { SearchFieldType } from '@/constants/search'
import { properties as propertiesApi } from '@/services/api'

defineOptions({
  name: 'flexiratesMerchantPropertiesList',
})

const route = useRoute()
const router = useRouter()

// 列配置
const columns = ref<TableColumnItem[]>([
  { field: 'property_number', template: 'property_number', header: 'Property Number', style: { minWidth: '140px' } },
  { field: 'street_address', header: 'Address', style: { minWidth: '200px' } },
  { field: 'full_amount', header: 'Full Rate Amount', template: 'full_amount', style: { minWidth: '120px' } },
  { field: 'due_date', header: 'Due Date', style: { minWidth: '120px' } },
  { field: 'status', header: 'Status', template: 'status', style: { minWidth: '100px' } },
  { field: '', template: 'action', header: '', frozen: true, alignFrozen: 'right', style: { width: '160px' } },
])

const { hasPermission } = usePermissions()

// 使用 useRequestList 处理客户列表数据
const {
  list,
  loading,
  total,
  refresh,
  search,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
  loading: isListLoading,
  setSearchParams,
} = useRequestList<Properties.Info[], Api.PropertiesListReq>({
  requestFn: propertiesApi.getList,
  immediate: false,
})

// Setup export functionality
const { isExporting, handleExport } = useExport({
  exportFn: propertiesApi.exportProperties,
  getParams: () => {
    return setSearchParams(searchModel.value)
  },
  onExportStart: () => {
    window.$toast.add({
      severity: 'info',
      summary: 'Export Started',
      detail: 'Preparing your export file...',
    })
  },
})

// 使用通用的列表刷新逻辑
useListRefresh('flexiratesMerchantPropertiesList', refresh)

const handleSort = (event: Record<string, any>) => {
  const { sortField, sortOrder } = event
  setSearchParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  search()
}

const setupFlexiRates = (data: any) => {
  console.log(data)
}

const searchModel = ref<Partial<Api.PropertiesListReq>>({
  keyword: '',
  status: '',
})

const tableSelection = ref([])

const customerTable = ref()

// 配置搜索字段
const searchFields = computed(() => [
  {
    name: 'keyword',
    label: 'What are you looking for?',
    type: SearchFieldType.TEXT,
    placeholder: 'Search by name, email, property number',
    maxlength: 50,
    defaultValue: '',
  },
  {
    name: 'status',
    label: 'Status',
    type: SearchFieldType.SELECT,
    placeholder: 'Select Status',
    options: [
      {
        label: 'All',
        value: '',
      },
      {
        label: 'Registered',
        value: 1,
      },
      {
        label: 'Not Registered',
        value: 2,
      },
    ],
    defaultValue: [],
  },
])

const navigateToDetail = ({ data }: { data: any }) => {
  if (!hasPermission(Permissions.PROPERTIES_DETAIL)) {
    return
  }
  if (data?.status !== 1) {
    window.$toast.add({
      severity: 'info',
      summary: 'Info',
      detail: 'This property is not registered yet',
    })
    return
  }
  router.push({ name: 'flexiratesMerchantPropertiesDetail', params: { id: data.id } })
}

// 搜索处理
const handleSearch = () => {
  setSearchParams(searchModel.value)
  search()
}

onActivated(() => {
  const query = route.query
  if (query && Object.keys(query).length > 0) {
    // First update the searchModel with properly converted values
    searchModel.value = {
      keyword: typeof query.keyword === 'string' ? query.keyword : '',
    }

    // Then set search params and execute search
    setSearchParams(searchModel.value)
    refresh()
  }
  else {
    searchModel.value = {
      'keyword': '',
      'created_at[]': [],
    }
    setSearchParams(searchModel.value)
    refresh()
  }
})
</script>

<template>
  <div class="customer-page">
    <BaseSearch
      v-model="searchModel" :loading="isListLoading" :basic-search-fields="searchFields"
      @search="handleSearch"
    />

    <div v-if="hasPermission(Permissions.PROPERTIES_EXPORT)" class="flex justify-end items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8">
      <div class="flex-1" />
      <BaseExportDialog :loading="isListLoading" :export-loading="isExporting" @export="handleExport" />
    </div>

    <!-- 客户表格 -->
    <BaseDataTable
      ref="customerTable" v-model:selection="tableSelection"
      :show-search-bar="false" :value="list" :columns="columns" :scrollable="true" :show-multiple-column="false"
      :loading="loading" :paginator="true" :rows="50" :total-records="total" :lazy="true" data-key="id" :failed="failed"
      :failure-message="failureMessage" :striped-rows="false" style="--frozen-column-border-bottom : -8px" :row-hover="true"
      @change-search="handleSearch" @page="(e: DataTablePageEvent) => handlePageChange(e)" @row-click="navigateToDetail"
      @sort="handleSort"
    >
      <template #full_amount="{ data }">
        {{ Format.formatAmount(data?.full_amount) }}
      </template>
      <template #due_date="{ data }">
        <span class="underline">
          {{ dayjs(data?.due_date).format('DD MMM YYYY') }}
        </span>
      </template>
      <template #property_number="{ data }">
        <span class="underline ">
          {{ data?.property_number }}
        </span>
      </template>
      <template #status="{ data }">
        <span
          class="font-bold" :class="{
            'text-green-500': data?.status === 1,
            'text-red-500': data?.status === 2,
          }"
        >
          {{ data?.status_desc }}
        </span>
      </template>
      <template #action="{ data }">
        <div class="flex items-center justify-center min-h-10">
          <Button v-if="data?.status === 2" label="Setup FlexiRates" severity="warn" @click.stop="setupFlexiRates(data)" />
        </div>
      </template>
    </BaseDataTable>
  </div>
</template>

<style lang="scss" scoped>
.p-dialog .p-dialog-header {
  border-bottom: 1px solid #dee2e6;
  padding: 1.5rem;
}

.p-dialog .p-dialog-footer {
  border-top: 1px solid #dee2e6;
  padding: 1.5rem;
  text-align: right;
}

.p-dialog .p-dialog-content {
  padding: 2rem;
}

.confirmation-content {
  display: flex;
  align-items: center;
}
</style>
