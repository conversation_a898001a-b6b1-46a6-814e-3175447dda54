<script setup lang="ts">
import { Format } from '@shared'
import { CommonPage } from '@ui'
import { toTypedSchema } from '@vee-validate/yup'
import dayjs from 'dayjs'
import Button from 'primevue/button'
import Column from 'primevue/column'
import DataTable from 'primevue/datatable'
import InputText from 'primevue/inputtext'
import Message from 'primevue/message'
import { useToast } from 'primevue/usetoast'
import { Field, Form as VeeForm } from 'vee-validate'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import * as yup from 'yup'
import { useDict } from '@/composables/useDict'
import { usePermissions } from '@/composables/usePermissions'
import { Permissions } from '@/constants/permissions'
import { TransactionStatus } from '@/constants/transaction'
import { properties as propertiesApi, ratepayer as ratepayerApi } from '@/services/api'

defineOptions({
  name: 'flexiratesMerchantPropertiesDetail',
})

const route = useRoute()
const toast = useToast()

const { hasPermission } = usePermissions()

// 响应式数据
const propertyData = ref<Properties.DetailInfo | null>(null)
const loading = ref(true)
const error = ref<string | null>(null)

// 编辑状态管理
const isEditing = ref(false)
const updateLoading = ref(false)

// 表单数据
const formData = reactive({
  first_name: '',
  last_name: '',
  email: '',
  mobile: '',
  street_address: '',
  city: '',
  state: '',
  postcode: '',
})

// 验证模式
const validationSchema = toTypedSchema(yup.object({
  first_name: yup.string().required('First Name is required'),
  last_name: yup.string().required('Last Name is required'),
  email: yup.string().email('Invalid email format').matches(
    /^[\w.%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i,
    'Please enter a valid email address with a proper domain',
  ).required('Email is required'),
  mobile: yup.string().required('Mobile number is required'),
  street_address: yup.string().required('Street address is required'),
  city: yup.string().required('City is required'),
  state: yup.string().required('State is required'),
  postcode: yup.string().required('Postcode is required'),
}))

// 计算属性：获取当前显示的数据
const displayData = computed(() => {
  if (isEditing.value) {
    return formData
  }

  const userProfile = propertyData.value?.customer?.user_profile
  const customerUser = propertyData.value?.customer?.customer_user
  return {
    first_name: userProfile?.first_name || '',
    last_name: userProfile?.last_name || '',
    email: customerUser?.email || '',
    mobile: customerUser?.mobile || '',
    street_address: userProfile?.address_line_1 || '',
    city: userProfile?.city || '',
    state: userProfile?.state || '',
    postcode: userProfile?.postcode || '',
  }
})

// 获取属性详情数据
const fetchData = async () => {
  try {
    loading.value = true
    error.value = null

    const res = await propertiesApi.getDetail({
      id: route.params.id as string,
    })

    propertyData.value = res.data
  }
  catch (err: any) {
    error.value = err.message || 'Failed to load property details'
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to load property details',
      life: 3000,
    })
  }
  finally {
    loading.value = false
  }
}

// 初始化表单数据
const initFormData = () => {
  if (propertyData.value) {
    const userProfile = propertyData.value.customer?.user_profile
    const customerUser = propertyData.value.customer?.customer_user
    formData.first_name = userProfile?.first_name || ''
    formData.last_name = userProfile?.last_name || ''
    formData.email = customerUser?.email || ''
    formData.mobile = customerUser?.mobile || ''
    formData.street_address = userProfile?.address_line_1 || ''
    formData.city = userProfile?.city || ''
    formData.state = userProfile?.state || ''
    formData.postcode = userProfile?.postcode || ''
  }
}

// 开始编辑
const startEdit = () => {
  initFormData()
  isEditing.value = true
}

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false
  initFormData() // 重置表单数据
}

// 保存编辑
const saveEdit = async () => {
  if (!propertyData.value?.customer?.customer_id) {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Unable to get customer ID',
      life: 3000,
    })
    return
  }

  updateLoading.value = true

  try {
    const updateData: Api.RatepayerUpdateReq = {
      first_name: formData.first_name,
      last_name: formData.last_name,
      email: formData.email,
      mobile: formData.mobile,
      street_address: formData.street_address,
      city: formData.city,
      state: formData.state,
      postcode: formData.postcode,
    }

    const { code } = await ratepayerApi.updateRatepayer(propertyData.value.customer?.customer_id, updateData)

    if (code === 0) {
      toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Ratepayer information updated successfully',
        life: 3000,
      })
      isEditing.value = false
      // 重新获取数据以显示更新后的信息
      await fetchData()
    }
  }
  catch (error) {
    console.error('Failed to update ratepayer information:', error)
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to update ratepayer information',
      life: 3000,
    })
  }
  finally {
    updateLoading.value = false
  }
}

// Remove the navigation-based handleEdit function since we use inline editing

const { getLabel: getTransStatusLabel } = useDict('trans_status')

// 监听数据变化，初始化表单
watch(() => propertyData.value, (newData) => {
  if (newData) {
    initFormData()
  }
}, { immediate: true })

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<template>
  <CommonPage :loading="loading" :error="error" @retry="fetchData">
    <div v-if="propertyData" class="property-content">
      <div class="bg-white p-6 rounded-2xl my-3 text-gray-600">
        <div class="text-2xl font-bold">
          <span v-if="propertyData?.customer_property?.street_address">{{ propertyData?.customer_property?.street_address }} </span>
        </div>
      </div>

      <div class="bg-white p-8 py-6 rounded-2xl mt-8 text-gray-600">
        <!-- 页面标题 -->

        <div class="bg-white rounded-2xl">
          <div class="ratepayer-header flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">
              Ratepayer Details
            </h1>
            <!-- 页面级操作按钮 -->
            <div class="flex gap-2">
              <div class="flex flex-col text-(--colors-gray) mr-4">
                <div class="text-sm">
                  Logged in <span
                    class="font-medium text-(--colors-primary)"
                  >{{ propertyData?.customer?.customer_user?.login_times }}
                    times</span>
                  <template v-if="propertyData?.customer?.customer_user?.last_login_time">
                    | Last logged in: <span class="font-medium text-(--colors-primary)">
                      {{ dayjs(propertyData?.customer?.customer_user?.last_login_time).format('DD MMM YYYY HH:mm:ss') }}
                    </span>
                  </template>
                </div>
                <div class="text-sm">
                  Registered date: <span class="font-medium text-(--colors-primary)">
                    {{ dayjs(propertyData?.customer?.customer_user?.created_at).format('DD MMM YYYY HH:mm:ss') }}
                  </span>
                </div>
              </div>
              <Button
                v-if="!isEditing && hasPermission(Permissions.RATE_PAYER_UPDATE)" type="button" label="EDIT DETAILS"
                class="!px-6" @click="startEdit"
              />
              <Button
                v-if="isEditing && hasPermission(Permissions.RATE_PAYER_UPDATE)" type="button" label="SAVE"
                severity="success" :loading="updateLoading" @click="saveEdit"
              />
              <Button
                v-if="isEditing && hasPermission(Permissions.RATE_PAYER_UPDATE)" type="button" label="CANCEL"
                severity="secondary" outlined @click="cancelEdit"
              />
            </div>
          </div>

          <!-- 编辑模式表单容器 -->
          <VeeForm v-if="isEditing" :initial-values="formData" :validation-schema="validationSchema" class="edit-form">
            <!-- 姓名行 -->
            <div class="form-row">
              <!-- 名字 -->
              <div class="field">
                <label class="mb-2 block font-medium text-gray-700">First Name</label>
                <Field v-slot="{ field, errorMessage }" name="first_name">
                  <div class="field-input-container">
                    <InputText
                      v-bind="field" v-model="formData.first_name" placeholder="First Name" class="w-full"
                      :class="{ 'p-invalid': errorMessage }"
                    />
                    <Message v-if="errorMessage" class="mt-1" severity="error" variant="simple">
                      {{ errorMessage }}
                    </Message>
                  </div>
                </Field>
              </div>

              <div class="field">
                <label class="mb-2 block font-medium text-gray-700">Last Name</label>
                <Field v-slot="{ field, errorMessage }" name="last_name">
                  <div class="field-input-container">
                    <InputText
                      v-bind="field" v-model="formData.last_name" placeholder="Last Name" class="w-full"
                      :class="{ 'p-invalid': errorMessage }"
                    />
                    <Message v-if="errorMessage" class="mt-1" severity="error" variant="simple">
                      {{ errorMessage }}
                    </Message>
                  </div>
                </Field>
              </div>
            </div>

            <!-- 联系信息行 -->
            <div class="form-row">
              <!-- 邮箱地址 -->
              <div class="field">
                <label class="mb-2 block font-medium text-gray-700">Email Address</label>
                <Field v-slot="{ field, errorMessage }" name="email">
                  <div class="field-input-container">
                    <InputText
                      v-bind="field" v-model="formData.email" placeholder="Email Address" type="email"
                      class="w-full" :class="{ 'p-invalid': errorMessage }"
                    />
                    <Message v-if="errorMessage" class="mt-1" severity="error" variant="simple">
                      {{ errorMessage }}
                    </Message>
                  </div>
                </Field>
              </div>

              <!-- 手机号码 -->
              <div class="field">
                <label class="mb-2 block font-medium text-gray-700">Mobile Number</label>
                <Field v-slot="{ field, errorMessage }" name="mobile">
                  <div class="field-input-container">
                    <InputText
                      v-bind="field" v-model="formData.mobile" placeholder="Mobile Number" class="w-full"
                      :class="{ 'p-invalid': errorMessage }"
                    />
                    <Message v-if="errorMessage" class="mt-1" severity="error" variant="simple">
                      {{ errorMessage }}
                    </Message>
                  </div>
                </Field>
              </div>
            </div>

            <!-- 地址信息 -->
            <div class="form-row">
              <div class="field">
                <label class="mb-2 block font-medium text-gray-700">Mailing Address</label>
                <div class="flex flex-col gap-2 w-3/5">
                  <!-- 街道地址 -->
                  <Field v-slot="{ field, errorMessage }" name="street_address">
                    <div class="field-input-container">
                      <InputText
                        v-bind="field" v-model="formData.street_address" placeholder="Street Address"
                        class="w-full" :class="{ 'p-invalid': errorMessage }"
                      />
                      <Message v-if="errorMessage" class="mt-1" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </div>
                  </Field>

                  <!-- 城市 -->
                  <Field v-slot="{ field, errorMessage }" name="city">
                    <div class="field-input-container">
                      <InputText
                        v-bind="field" v-model="formData.city" placeholder="City" class="w-full"
                        :class="{ 'p-invalid': errorMessage }"
                      />
                      <Message v-if="errorMessage" class="mt-1" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </div>
                  </Field>

                  <!-- 州和邮编 -->
                  <div class="flex gap-4">
                    <Field v-slot="{ field, errorMessage }" name="state">
                      <div class="field-input-container flex-1">
                        <InputText
                          v-bind="field" v-model="formData.state" placeholder="State" class="w-full"
                          :class="{ 'p-invalid': errorMessage }"
                        />
                        <Message v-if="errorMessage" class="mt-1" severity="error" variant="simple">
                          {{ errorMessage }}
                        </Message>
                      </div>
                    </Field>

                    <Field v-slot="{ field, errorMessage }" name="postcode">
                      <div class="field-input-container flex-1">
                        <InputText
                          v-bind="field" v-model="formData.postcode" placeholder="Postcode" class="w-full"
                          :class="{ 'p-invalid': errorMessage }"
                        />
                        <Message v-if="errorMessage" class="mt-1" severity="error" variant="simple">
                          {{ errorMessage }}
                        </Message>
                      </div>
                    </Field>
                  </div>
                </div>
              </div>
            </div>
          </VeeForm>

          <!-- 只读模式 -->
          <div v-else>
            <!-- 姓名行 -->
            <div class="form-row">
              <!-- 名字 -->
              <div class="field">
                <label class="mb-2 block font-medium text-gray-700">First Name</label>
                <div class="detail-value">
                  {{ displayData.first_name || '-' }}
                </div>
              </div>

              <div class="field">
                <label class="mb-2 block font-medium text-gray-700">Last Name</label>
                <div class="detail-value">
                  {{ displayData.last_name || '-' }}
                </div>
              </div>
            </div>

            <!-- 联系信息行 -->
            <div class="form-row">
              <!-- 邮箱地址 -->
              <div class="field">
                <label class="mb-2 block font-medium text-gray-700">Email Address</label>
                <div class="detail-value">
                  {{ displayData.email || '-' }}
                </div>
              </div>

              <!-- 手机号码 -->
              <div class="field">
                <label class="mb-2 block font-medium text-gray-700">Mobile Number</label>
                <div class="detail-value">
                  {{ displayData.mobile || '-' }}
                </div>
              </div>
            </div>

            <!-- 地址信息 -->
            <div class="form-row">
              <div class="field">
                <label class="mb-2 block font-medium text-gray-700">Mailing Address</label>
                <div class="flex flex-col gap-2 w-3/5">
                  <div class="detail-value">
                    {{ displayData.street_address || '-' }}
                  </div>
                  <div class="detail-value">
                    {{ displayData.city || '-' }}
                  </div>
                  <div class="flex gap-4">
                    <div class="detail-value">
                      {{ displayData.state || '-' }}
                    </div>
                    <div class="detail-value">
                      {{ displayData.postcode || '-' }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Rates Information 部分 -->
        <div class="mt-8 bg-white py-6 rounded-2xl">
          <h3 class="sub-title">
            Rates Information
          </h3>

          <div class="flex flex-col lg:flex-row lg:gap-8 text-(--colors-gray) mt-8">
            <div class="flex flex-col justify-between gap-4 border border-(--colors-gray) rounded-xl p-6 flex-1">
              <div>
                <!-- 财年 -->
                Full Rate Amount for {{ propertyData?.fiscal_year }}
              </div>
              <div class="!mb-0 font-bold text-4xl ">
                {{ Format.formatAmount(propertyData?.statistics?.full_rate_amount) }}
              </div>
            </div>
            <div class="flex flex-col justify-between gap-4 border border-(--colors-gray) rounded-xl p-6 flex-1">
              <div>
                Amount Paid to Date
              </div>
              <div class="!mb-0 font-bold text-4xl">
                {{ Format.formatAmount(propertyData?.statistics?.paid_amount) }}
              </div>
            </div>
            <div class="flex flex-col justify-between gap-4 border border-(--colors-gray) rounded-xl p-6 flex-1">
              <div>
                Remaining Balance
              </div>
              <div class="!mb-0 font-bold text-4xl">
                {{ Format.formatAmount(propertyData?.statistics?.remaining_amount) }}
              </div>
            </div>
            <div class="flex flex-col justify-between gap-4 border border-(--colors-gray) rounded-xl p-6 flex-1">
              <div>
                Number of Remaining
                Scheduled Payments
              </div>
              <div class="!mb-0 font-bold text-4xl">
                {{ propertyData?.statistics?.remaining_schedule_number }}
              </div>
            </div>
          </div>
        </div>

        <div class="info-section mt-4">
          <h3 class="section-title">
            Payment History
          </h3>

          <div class="payment-history">
            <DataTable :value="propertyData.transaction">
              <!-- <Column field="" header="Due Date" /> -->
              <Column field="payment_amount" header="Amount">
                <template #body="{ data }">
                  {{ Format.formatAmount(data?.payment_amount) }}
                </template>
              </Column>
              <!-- <Column field="" header="Method" /> -->
              <Column field="status" header="Status">
                <template #body="{ data }">
                  <BaseTag
                    :type="data.status === TransactionStatus.SUCCEEDED ? 'paid'
                      : data.status === TransactionStatus.FAILED ? 'failed' : 'upcoming'"
                    :text="getTransStatusLabel(data.status)" class="w-30"
                  />
                </template>
              </Column>
              <!-- <Column field="" header="Failed Payment" /> -->
              <!-- <Column field="" header="Edit Count" /> -->
              <template #empty>
                <div class="flex justify-center items-center h-10">
                  <p>No payment history found</p>
                </div>
              </template>
            </DataTable>
          </div>
        </div>
      </div>
    </div>
  </CommonPage>
</template>

<style scoped lang="scss">
.property-detail {
  margin: 0 auto;

  .rates-info {
    color: var(--colors-gray);
  }
}

// Loading 状态样式
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;

  .loading-text {
    margin-top: 1rem;
    color: #6b7280;
    font-size: 1rem;
  }
}

// 错误状态样式
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

// 页面头部
.page-header {
  margin-top: 0;
  margin-bottom: 2rem;
  border-bottom: 2px solid var(--colors-gray);
  padding-bottom: 1cap;
  display: flex;
  justify-content: space-between;

  .page-title {
    font-size: 2rem;
    font-weight: 700;
    color: #031F73;
    margin-bottom: 0.5rem;
  }
}

// 信息区域
.info-section {
  margin-bottom: 2rem;

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #031F73;
    margin-bottom: 1rem;
  }
}

// 表单布局
.form-row {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.field {
  flex: 1;
  display: flex;
  margin-bottom: 1rem;

  label {
    min-width: 130px;
    line-height: 1;
    padding: 0.5rem 0;
  }
}

.detail-value {
  flex: 1;
  padding: 0.5rem 1rem;
  border: 1px solid var(--colors-gray);
  border-radius: 0.75rem;
  color: var(--colors-gray);
  min-height: 2.5rem;
  display: flex;
  align-items: center;

  &:empty::before {
    content: '-';
    color: var(--colors-gray);
  }
}

// 编辑模式样式
.edit-form {
  .field {
    // 保持与只读模式相同的布局
    display: flex;
    flex: 1;
    margin-bottom: 1rem;

    label {
      min-width: 130px;
      line-height: 1;
      padding: 0.5rem 0;
      font-weight: 500;
    }
  }

  .field-input-container {
    flex: 1;
  }

  .p-inputtext {
    border-radius: 0.75rem;
    border: 1px solid var(--colors-gray);
    padding: 0.5rem 1rem;
    min-height: 2.5rem;

    &:focus {
      border-color: #031F73;
      box-shadow: 0 0 0 2px rgba(3, 31, 115, 0.1);
    }

    &.p-invalid {
      border-color: #ef4444;
    }
  }

  .p-message {
    font-size: 0.875rem;
  }
}

.ratepayer-header {
  border-bottom: 2px solid var(--colors-gray);

  h1 {
    color: #031F73;
    margin: 1rem 0;
    margin-top: 0;
  }
}

.sub-title {
  color: #031F73;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.payment-history {
  background: #F5F5FF;
  border-radius: 1rem;
  padding: 1rem;
}

// 响应式样式
@media screen and (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 1rem;
  }

  .form-col {
    min-width: 100%;
  }

  .property-detail {
    padding: 1rem;
  }

  .page-header {
    .page-title {
      font-size: 1.75rem;
    }

    .page-subtitle {
      font-size: 1rem;
    }
  }
}

// 小型移动设备
@media screen and (max-width: 480px) {
  .property-detail {
    max-width: 100%;
  }

  .detail-value {
    font-size: 0.9rem;
    padding: 0.6rem 0.8rem;
  }

  .form-row {
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .info-section {
    margin-bottom: 1rem;

    .section-title {
      font-size: 1.125rem;
    }
  }
}
</style>
