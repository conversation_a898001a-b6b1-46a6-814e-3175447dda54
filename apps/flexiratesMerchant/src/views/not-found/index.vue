<script setup lang="ts">
import Button from 'primevue/button'
import { onBeforeUnmount, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push({ path: '/' })
}

const goBack = () => {
  router.back()
}

// 添加简单的动画效果计数
const count = ref(0)
let interval: ReturnType<typeof setInterval> | null = null

onMounted(() => {
  interval = setInterval(() => {
    count.value += 1
    if (count.value >= 100) {
      if (interval) {
        clearInterval(interval)
        interval = null
      }
      goHome()
    }
  }, 50)
})

onBeforeUnmount(() => {
  if (interval) {
    clearInterval(interval)
    interval = null
  }
})
</script>

<template>
  <div class="not-found">
    <div class="content">
      <div class="error-container">
        <div class="error-code">
          <span class="digit">4</span>
          <div class="planet-container">
            <div class="planet" />
            <div class="orbit">
              <div class="satellite" />
            </div>
          </div>
          <span class="digit">4</span>
        </div>
        <h2 class="error-message">
          {{ $t('message.pageNotFound') }}
        </h2>
        <p class="error-description">
          {{ $t('message.pageNotFoundDescription') }}
        </p>
        <div class="actions">
          <Button class="p-button-primary go-back-btn" @click="goBack">
            <i class="pi pi-arrow-left" />
            {{ $t('button.goBack') }}
          </Button>
          <Button class="p-button-outlined home-btn" @click="goHome">
            <i class="pi pi-home" />
            {{ $t('button.backToHome') }}
          </Button>
        </div>

        <div class="progress-container">
          <div class="progress-text">
            {{ $t('message.redirectingToHome') }} {{ count }}%
          </div>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: `${count}%` }" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.not-found {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, var(--colors-warn) 100%);
  font-family: var(--font-family);
}

.content {
  width: 100%;
  max-width: 800px;
  padding: 2rem;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 3rem 2rem;
  text-align: center;
}

.error-code {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
}

.digit {
  font-size: 8rem;
  font-weight: 700;
  color: var(--colors-warn);
  line-height: 1;
  text-shadow: 2px 4px 0px rgba(254,76,28, 0.2);
}

.planet-container {
  position: relative;
  width: 100px;
  height: 100px;
  margin: 0 1rem;
}

.planet {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--colors-warn), #fa9a7f);
  box-shadow: 0 0 20px rgba(254,76,28, 0.5), inset -10px -10px 20px rgba(254,76,28, 0.2);
}

.orbit {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 2px dashed rgba(254,76,28, 0.3);
  animation: orbit-rotation 10s linear infinite;
}

.satellite {
  position: absolute;
  top: -5px;
  left: 50%;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(254,76,28, 0.2);
  box-shadow: 0 0 10px rgba(254,76,28, 0.8);
}

.error-message {
  font-size: 1.75rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 1rem;
}

.error-description {
  font-size: 1rem;
  color: #5d6a7d;
  margin: 0 0 2rem;
  max-width: 500px;
}

.actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 2rem;
}

.go-back-btn, .home-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border-radius: 0.5rem;
}

.progress-container {
  width: 100%;
  max-width: 400px;
  margin-top: 2rem;
}

.progress-text {
  text-align: center;
  font-size: 0.875rem;
  color: #5d6a7d;
  margin-bottom: 0.5rem;
}

.progress-bar {
  height: 6px;
  width: 100%;
  background-color: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, rgba(254,76,28, 1), rgba(254,76,28, 0.2));
  border-radius: 3px;
  transition: width 0.2s ease;
}

@keyframes orbit-rotation {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@media (max-width: 576px) {
  .digit {
    font-size: 5rem;
  }

  .planet-container {
    width: 80px;
    height: 80px;
  }

  .planet {
    width: 40px;
    height: 40px;
  }

  .orbit {
    width: 70px;
    height: 70px;
  }

  .error-message {
    font-size: 1.5rem;
  }

  .actions {
    flex-direction: column;
  }
}
</style>
