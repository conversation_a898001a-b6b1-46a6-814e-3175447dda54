<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import { Format } from '@shared'
import { ref } from 'vue'
import BaseDataTable from '@/components/common/BaseDataTable.vue'
import { useListRefresh } from '@/composables/useListRefresh'
import { useRequestList } from '@/composables/useRequestList'
import { properties as propertiesApi } from '@/services/api'

defineOptions({
  name: 'arrearList',
})

const columns = ref<TableColumnItem[]>([
  { field: 'property_number', header: 'Property Number', style: { minWidth: '100px' } },
  { field: 'street_address', header: 'Address', style: { minWidth: '120px' } },
  { field: 'customer_profile.first_name', header: 'First Name', style: { minWidth: '120px' } },
  { field: 'customer_profile.last_name', header: 'Last Name', style: { minWidth: '120px' } },
  { field: 'customer_user.email', header: 'Email', style: { minWidth: '120px' } },
  { field: 'customer_user.mobile', header: 'Phone Number', style: { minWidth: '120px' } },
  { field: 'amount_owing', template: 'amount_owing', header: 'Arrears Amount', style: { minWidth: '120px' } },
])

const {
  list,
  loading,
  total,
  refresh,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
} = useRequestList<Properties.Info[], Api.PropertiesListReq>({
  requestFn: propertiesApi.getArrearList,
})

useListRefresh('arrearList', refresh)
</script>

<template>
  <div class="payout-list-page">
    <!-- 支付表格 -->
    <BaseDataTable
      :value="list" :columns="columns" :scrollable="true"
      :show-multiple-column="false" :loading="loading" :paginator="true" :rows="50" :total-records="total" :lazy="true"
      data-key="id" :show-search-bar="false" :failed="failed" :failure-message="failureMessage" :striped-rows="true" :row-hover="true"
      @page="(e: DataTablePageEvent) => handlePageChange(e)"
    >
      <template #amount_owing="{ data }">
        <span v-if="data?.amount_owing">
          {{ Format.formatAmount(data?.amount_owing) }}
        </span>
      </template>
    </BaseDataTable>
  </div>
</template>

<style scoped>
.p-datatable .p-datatable-tbody>tr>td {
  padding: 0.75rem 1rem;
}

.p-datatable .p-datatable-thead>tr>th {
  background-color: #f8f9fa;
  font-weight: 600;
}
</style>
