<script setup lang="ts">
import type { TreeNode } from 'primevue/treenode'
import { toTypedSchema } from '@vee-validate/yup'
import { useToast } from 'primevue/usetoast'
import { Field, Form as VeeForm } from 'vee-validate'
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import * as yup from 'yup'

import { useListRefresh } from '@/composables/useListRefresh'
import { userRole } from '@/services/api'

defineOptions({
  name: 'flexiratesMerchantUserRoleEdit',
})

const { backWithRefresh } = useListRefresh('flexiratesMerchantUserRoleList', () => {})

const toast = useToast()
const router = useRouter()
const route = useRoute()
const formRef = ref()
const loading = ref(false)
const roleId = ref<string>(route.params.id as string)

// 表单初始值
const initialValues = reactive<{
  name: string
  slug: string
  localPermissions: Record<string, { checked: boolean, partialChecked: boolean }>
  permissions: string[]
}>({
  name: '',
  slug: '',
  permissions: [],
  localPermissions: {},
})

// 权限树数据
const permissions = ref<TreeNode[]>([])
const permissionsMap = new Map<string, TreeNode>()

const transformPermissions = (data: UserRole.PermissionListRes[]): TreeNode[] => {
  return data.map((item: UserRole.PermissionListRes) => {
    permissionsMap.set(String(item.id), {
      key: String(item.id),
      label: item.name,
      data: item,
      children: item?.children?.length ? transformPermissions(item.children) : [],
    })
    return {
      key: String(item.id),
      label: item.name,
      data: item,
      children: item?.children?.length ? transformPermissions(item.children) : [],
    }
  })
}

// 获取权限列表
const getPermissionList = async () => {
  const res = await userRole.getPermissionList()
  permissions.value = transformPermissions(res.data)
}

const transactionStringToTree = (data: UserRole.PermissionListRes[]): { [key: string]: { checked: boolean, partialChecked: boolean } } => {
  const result: { [key: string]: { checked: boolean, partialChecked: boolean } } = {}

  for (const item of data) {
    let partialChecked = false
    if (item.children && item.children.length === permissionsMap.get(String(item.id))?.children?.length) {
      partialChecked = true
    }
    // Add current item to result
    result[item.id] = {
      checked: true,
      partialChecked,
    }
    // Process children recursively if they exist
    if (item.children?.length) {
      const childrenResult = transactionStringToTree(item.children)
      // Merge children results with current result
      Object.assign(result, childrenResult)
    }
  }

  return result
}

// 获取角色详情
const fetchRoleDetail = async () => {
  try {
    loading.value = true
    const response = await userRole.detail(roleId.value)
    const { name, slug, permissions: rolePermissions } = response.data
    initialValues.name = name
    initialValues.slug = slug

    // 设置已选中的权限
    if (rolePermissions && Array.isArray(rolePermissions)) {
      initialValues.localPermissions = transactionStringToTree(rolePermissions as UserRole.PermissionListRes[])
    }
  }
  catch (error) {
    console.error('Failed to fetch role detail:', error)
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to fetch role detail',
      life: 3000,
    })
  }
  finally {
    loading.value = false
  }
}

// 表单验证schema
const schema = toTypedSchema(yup.object({
  name: yup.string()
    .min(3, 'Minimum length is 3 characters')
    .max(50, 'Maximum length is 50 characters')
    .required(),
  slug: yup.string()
    .min(3, 'Minimum length is 3 characters')
    .max(50, 'Maximum length is 50 characters')
    .required(),
}))

// 提交表单
const submitForm = async () => {
  loading.value = true

  try {
    const result = await formRef.value?.validate()

    if (!result.valid) {
      loading.value = false
      return
    }

    // 映射字段名称以匹配API要求
    const formData = {
      role_name: result.values.name,
      role_mark: result.values.slug,
      permissions: Object.keys(initialValues.localPermissions).map(i => Number(i)),
    }

    const { code } = await userRole.update(roleId.value, formData)

    if (code === 0) {
      toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Role updated successfully',
        life: 3000,
      })

      backWithRefresh()
    }
  }
  catch (error) {
    console.error('Failed to update role:', error)
  }
  finally {
    loading.value = false
  }
}

// 批量操作权限的函数
const selectAllPermissions = () => {
  const allPermissions: Record<string, { checked: boolean, partialChecked: boolean }> = {}

  // 递归遍历所有权限节点
  const traversePermissions = (nodes: TreeNode[]) => {
    nodes.forEach((node) => {
      allPermissions[node.key as string] = {
        checked: true,
        partialChecked: false,
      }
      if (node.children && node.children.length > 0) {
        traversePermissions(node.children)
      }
    })
  }

  traversePermissions(permissions.value)
  initialValues.localPermissions = allPermissions
}

const deselectAllPermissions = () => {
  initialValues.localPermissions = {}
}

const toggleAllPermissions = () => {
  const allPermissions: Record<string, { checked: boolean, partialChecked: boolean }> = {}

  // 递归遍历所有权限节点
  const traversePermissions = (nodes: TreeNode[]) => {
    nodes.forEach((node) => {
      const currentKey = node.key as string
      const isCurrentlySelected = initialValues.localPermissions[currentKey]?.checked

      // 反转当前状态
      if (!isCurrentlySelected) {
        allPermissions[currentKey] = {
          checked: true,
          partialChecked: false,
        }
      }

      if (node.children && node.children.length > 0) {
        traversePermissions(node.children)
      }
    })
  }

  traversePermissions(permissions.value)
  initialValues.localPermissions = allPermissions
}

// 取消操作
const cancel = () => {
  router.back()
}

onMounted(() => {
  Promise.all([
    fetchRoleDetail(),
  ]).then(() => {
    getPermissionList()
  })
})
</script>

<template>
  <div class="merchant-role-edit-page">
    <div class="flex justify-between items-center mb-4">
      <h1 class="text-3xl">
        Edit Role
      </h1>
    </div>

    <div class="p-4 bg-white rounded-2xl">
      <VeeForm
        ref="formRef"
        :validation-schema="schema"
        class="merchant-role-form flex flex-col gap-4"
        @submit="submitForm"
      >
        <!-- 角色名称 -->
        <Field v-slot="{ field, errorMessage }" v-model="initialValues.name" name="name" class="form-col">
          <div class="field">
            <label for="name" class="mb-2 block">Role Name*</label>
            <InputText
              id="name"
              v-bind="field"
              placeholder="Role Name"
              class="w-full"
              autofocus
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>

        <!-- 角色标识 -->
        <Field v-slot="{ field, errorMessage }" v-model="initialValues.slug" name="slug" class="form-col">
          <div class="field">
            <label for="slug" class="mb-2 block">Slug*</label>
            <InputText
              id="slug"
              v-bind="field"
              placeholder="Slug"
              class="w-full"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>

        <!-- 角色权限树 -->
        <Field v-slot="{ errorMessage }" name="permissions" class="form-col">
          <div class="field">
            <label for="permissions" class="mb-2 block">Permissions</label>

            <!-- 批量操作按钮 -->
            <div class="permission-bulk-actions">
              <Button
                type="button"
                label="Select All"
                icon="pi pi-check-square"
                size="small"
                severity="secondary"
                outlined
                @click="selectAllPermissions"
              />
              <Button
                type="button"
                label="Toggle All"
                icon="pi pi-refresh"
                size="small"
                severity="secondary"
                outlined
                @click="toggleAllPermissions"
              />
              <Button
                type="button"
                label="Deselect All"
                icon="pi pi-square"
                size="small"
                severity="secondary"
                outlined
                @click="deselectAllPermissions"
              />
            </div>

            <div class="flex flex-col gap-2">
              <Tree
                v-model:selection-keys="initialValues.localPermissions"
                :value="permissions"
                selection-mode="checkbox"
                :meta-key-selection="false"
                class="w-full md:w-[30rem] !p-0"
                placeholder="Select permissions"
              >
                <template #default="{ node }">
                  <div class="flex align-items-center">
                    <span>{{ node.label }}</span>
                  </div>
                </template>
              </Tree>
            </div>
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>

        <!-- 表单按钮 -->
        <div class="flex justify-end mt-6 gap-2">
          <Button
            type="button"
            label="Cancel"
            icon="pi pi-times"
            class="p-button-text mr-2"
            @click="cancel"
          />
          <Button
            type="submit"
            label="Save"
            icon="pi pi-check"
            :loading="loading"
          />
        </div>
      </VeeForm>
    </div>
  </div>
</template>

<style scoped>
.merchant-role-edit-page {
  padding: 1rem;
}

.merchant-role-form {
  max-width: 600px;
}

.form-row {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.form-col {
  flex: 1;
  min-width: 250px;
}

.w-full {
  width: 100%;
}

/* 批量操作按钮样式 */
.permission-bulk-actions {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

/* 响应式样式 */
@media screen and (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 1rem;
  }

  .form-col {
    min-width: 100%;
  }

  .permission-bulk-actions {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}

/* 小型移动设备 */
@media screen and (max-width: 480px) {
  :deep(.p-float-label) {
    font-size: 0.9rem;
  }

  :deep(.p-inputtext) {
    font-size: 0.9rem;
    padding: 0.5rem;
  }
}
</style>
