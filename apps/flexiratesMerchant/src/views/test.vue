<script setup lang="ts">
import BaseFileUpload from '@/components/common/BaseFileUpload.vue'
import BaseSearch from '@/components/common/BaseSearch.vue'
import BaseTag from '@/components/common/BaseTag.vue'
import { SearchFieldType } from '@/constants/search'
import { computed, ref } from 'vue'

// 定义SearchField类型
interface SearchField {
  name: string
  label: string
  type: SearchFieldType
  placeholder?: string
  maxlength?: number
  defaultValue: any
  width?: string
  min?: number
  max?: number
  step?: number
  options?: Array<{ label: string, value: string | number | boolean }>
}

// 定义SearchModel类型，使搜索字段可以动态访问
interface SearchModel {
  keyword: string
  category: string
  status: string
  dateRange: null | string
  price: null | number
  priceRange: [null | number, null | number]
  inStock: boolean
  rating: string | number
  featured: boolean
  [key: string]: any // 添加索引签名以支持动态访问
}

// 文件上传示例
const fileList = ref<FileUpload.UploadFileItem[]>([])

// BaseSearch 示例数据
const searchModel = ref<SearchModel>({
  keyword: '',
  category: '',
  status: '',
  // 高级筛选选项
  dateRange: null,
  price: null,
  priceRange: [null, null],
  inStock: false,
  rating: '',
  featured: false,
})

// 示例下拉选项
const categoryOptions = ref([
  { label: 'All', value: '' },
  { label: '电子产品', value: 'electronics' },
  { label: '服装', value: 'clothing' },
  { label: '家居', value: 'home' },
  { label: '书籍', value: 'books' },
  { label: '食品1', value: 'food' },
])

const statusOptions = ref([
  { label: 'All', value: '' },
  { label: 'Active', value: 'active' },
  { label: 'Inactive', value: 'inactive' },
  { label: 'Pending', value: 'pending' },
])

const ratingOptions = ref([
  { label: '5星', value: 5 },
  { label: '4星及以上', value: 4 },
  { label: '3星及以上', value: 3 },
  { label: '2星及以上', value: 2 },
  { label: '1星及以上', value: 1 },
])

// 基本搜索字段（显示在主界面）
const basicSearchFields = computed(() => [
  {
    name: 'keyword',
    label: 'What are you looking for?',
    type: SearchFieldType.TEXT,
    placeholder: 'Search for Customer name, email, etc.',
    maxlength: 50,
    defaultValue: '',
    width: '420px',
  },
  {
    name: 'category',
    label: 'Category',
    type: SearchFieldType.SELECT,
    placeholder: 'All',
    options: categoryOptions.value,
    defaultValue: '',
    width: '200px',
  },
  {
    name: 'status',
    label: 'Status',
    type: SearchFieldType.SELECT,
    placeholder: 'All',
    options: statusOptions.value,
    defaultValue: '',
    width: '200px',
  },
])

// 高级搜索字段（显示在弹窗中）
const advancedSearchFields = computed<SearchField[]>(() => [
  {
    name: 'dateRange',
    label: '上架日期',
    type: SearchFieldType.DATE_RANGE,
    placeholder: '选择日期范围',
    defaultValue: null,
  },
  {
    name: 'price',
    label: '价格',
    type: SearchFieldType.NUMBER,
    placeholder: '输入价格',
    min: 0,
    max: 10000,
    step: 10,
    defaultValue: null,
  },
  {
    name: 'priceRange',
    label: '价格区间',
    type: SearchFieldType.NUMBER_RANGE,
    min: 0,
    max: 10000,
    step: 10,
    defaultValue: [null, null],
  },
  {
    name: 'inStock',
    label: '仅显示有库存商品',
    type: SearchFieldType.CHECKBOX,
    defaultValue: false,
  },
  {
    name: 'rating',
    label: '商品评分',
    type: SearchFieldType.RADIO,
    options: ratingOptions.value,
    defaultValue: '',
  },
  {
    name: 'featured',
    label: '精选商品',
    type: SearchFieldType.SWITCH,
    defaultValue: false,
  },
])

// 搜索处理函数
const handleSearch = () => {
  console.log('搜索参数:', searchModel.value)
  // 在实际应用中这里会调用API
}

// 当前搜索内容的展示
const currentSearchJson = computed(() => {
  return JSON.stringify(searchModel.value, null, 2)
})

// BaseTag示例
const handleTagClick = (tagType: string) => {
  console.log(`${tagType} tag clicked!`)
}
</script>

<template>
  <div class="test p-4">
    <h2 class="text-2xl font-bold mb-6">
      BaseSearch 与 More Filters 示例
    </h2>

    <!-- 使用重构后的BaseSearch组件 -->
    <div class="mb-8">
      <BaseSearch
        v-model="searchModel"
        :basic-search-fields="basicSearchFields"
        :advanced-search-fields="advancedSearchFields"
        :loading="false"
        @search="handleSearch"
      />

      <!-- 展示当前搜索内容 -->
      <div class="mt-4 p-4 bg-gray-100 rounded-lg">
        <h3 class="text-lg font-bold mb-2">
          当前搜索内容:
        </h3>
        <pre class="whitespace-pre-wrap">{{ currentSearchJson }}</pre>
      </div>
    </div>

    <h2 class="text-2xl font-bold mb-6 mt-8">
      文件上传组件示例
    </h2>

    <!-- 文件上传组件示例 -->
    <div class="mb-8">
      <BaseFileUpload v-model:model-value="fileList" :multiple="true" :max-files="2" accept="image/*" />
    </div>

    <!-- 添加BaseTag示例 -->
    <h2 class="text-2xl font-bold mb-6 mt-8">
      标签组件示例
    </h2>

    <div class="tag-container mb-8">
      <h3 class="text-lg font-bold mb-4">
        基本标签类型
      </h3>
      <div class="flex flex-wrap gap-2 mb-6">
        <BaseTag text="Default" />
        <BaseTag text="Paid" type="paid" />
        <BaseTag text="Upcoming" type="upcoming" />
        <BaseTag text="Failed" type="failed" />
      </div>

      <h3 class="text-lg font-bold mb-4">
        可点击标签示例
      </h3>
      <div class="flex flex-wrap gap-2">
        <BaseTag text="Default Clickable" clickable @click="() => handleTagClick('default')" />
        <BaseTag text="Paid Clickable" type="paid" clickable @click="() => handleTagClick('paid')" />
        <BaseTag text="Upcoming Clickable" type="upcoming" clickable @click="() => handleTagClick('upcoming')" />
        <BaseTag text="Failed Clickable" type="failed" clickable @click="() => handleTagClick('failed')" />
      </div>

      <h3 class="text-lg font-bold mb-4 mt-6">
        模拟状态列表
      </h3>
      <div class="status-list w-60 border border-gray-200 rounded bg-gray-50">
        <div class="border-b border-gray-200 px-4 py-2 font-bold">
          Status
        </div>
        <div class="status-item flex items-center p-2 border-b border-gray-200">
          <BaseTag text="Paid" type="paid" />
        </div>
        <div class="status-item flex items-center p-2 border-b border-gray-200">
          <BaseTag text="Upcoming" type="upcoming" />
        </div>
        <div class="status-item flex items-center p-2 border-b border-gray-200">
          <BaseTag text="Paid" type="paid" />
        </div>
        <div class="status-item flex items-center p-2 border-b border-gray-200">
          <BaseTag text="Failed" type="failed" />
        </div>
        <div class="status-item flex items-center p-2">
          <BaseTag text="Paid" type="paid" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.test {
  max-width: 1200px;
  margin: 0 auto;
}

.search-container {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-field-container {
  display: flex;
  flex-direction: column;
}

.search-input {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #ff5722;
  font-size: 14px;
}

.form-input {
  height: 44px;
  width: 420px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 0 12px 0 36px;
  font-size: 14px;
  transition: all 0.3s;
}

.form-input:focus {
  border-color: #ff5722;
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 87, 34, 0.2);
}

.select-container {
  position: relative;
}

.form-select {
  height: 44px;
  width: 200px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  appearance: none;
  background-color: white;
  transition: all 0.3s;
}

.form-select:focus {
  border-color: #ff5722;
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 87, 34, 0.2);
}

.select-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  color: #666;
}

.more-filters-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 44px;
  padding: 0 16px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.more-filters-btn:hover {
  border-color: #ff5722;
  color: #ff5722;
}

.more-filters-btn.has-filters {
  background-color: #fff8f6;
  border-color: #ff5722;
  color: #ff5722;
}

.search-btn {
  height: 44px;
  padding: 0 24px;
  background: #ff5722;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.search-btn:hover {
  background: #e64a19;
}

.advanced-filters-content {
  max-height: 60vh;
  overflow-y: auto;
}

.checkbox-input {
  width: 18px;
  height: 18px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.radio-input {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.clear-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  border-radius: 4px;
  background-color: white;
  border: 1px solid #d9d9d9;
  color: #666;
  cursor: pointer;
  transition: all 0.3s;
}

.clear-btn:hover {
  border-color: #ff5722;
  color: #ff5722;
}

.cancel-btn {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.cancel-btn:hover {
  color: #ff5722;
}

.apply-filters-btn {
  display: flex;
  align-items: center;
  height: 42px;
  padding: 0 20px;
  background: #ff5722;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.apply-filters-btn:hover {
  background: #e64a19;
}

.tag-container {
  max-width: 800px;
}

.status-item {
  background-color: white;
}

.status-list {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>
