<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/yup'
import { Field, Form as VeeForm } from 'vee-validate'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import * as yup from 'yup'
import logo from '@/assets/flexiratesMerchant/logo.png'
import loginBg from '@/assets/merchant/login-bg.png'
import GoogleRecaptcha from '@/components/googleRecaptchaV2/index.vue'
import { useUserStore } from '@/store/modules/user'

const { t } = useI18n()
const loading = ref(false)
const recaptchaRef = ref<InstanceType<typeof GoogleRecaptcha> | null>(null)
const formRef = ref()

const router = useRouter()

const route = useRoute()

const userStore = useUserStore()

const model = ref<Api.UserLoginReq>({
  email: '',
  password: '',
  rememberMe: false,
  google_token: '',
})

const schema = toTypedSchema(yup.object({
  email: yup.string().required(t('validation.emailRequired')).email(t('validation.emailInvalid')),
  password: yup.string().required(t('validation.passwordRequired')),
  google_token: yup.string().required(t('validation.googleTokenRequired')),
  rememberMe: yup.boolean(),
}))

const onRecaptchaVerify = (response: string) => {
  if (response) {
    model.value.google_token = response
    if (formRef.value) {
      formRef.value.setFieldValue('google_token', response)
    }
  }
}

const onRecaptchaExpired = () => {
  model.value.google_token = ''
  if (formRef.value) {
    formRef.value.setFieldValue('google_token', '')
  }
}

const onRecaptchaError = () => {
  model.value.google_token = ''
  if (formRef.value) {
    formRef.value.setFieldValue('google_token', '')
  }
}

const onFormSubmit = async () => {
  const { valid, errors } = await formRef.value?.validate()

  if (valid) {
    console.log(valid, errors)
    try {
      loading.value = true
      await userStore.login(model.value.email, model.value.password, model.value.google_token, model.value.rememberMe)
      router.replace(route?.query?.redirect as string || '/')
    }
    catch {
      model.value.google_token = ''
      recaptchaRef.value?.reset()
    }
    finally {
      loading.value = false
    }
  }
}
document.title = import.meta.env.VITE_APP_NAME
</script>

<template>
  <div class="auth-container">
    <div class="logo-wrap">
      <div class="logo">
        <Image :src="logo" width="250px" alt="Image" />
      </div>
    </div>

    <!-- Logo -->
    <div class="auth-bg-wrap" :style="{ backgroundImage: `url(${loginBg})`, backgroundSize: '100% 100%' }" />

    <div class="auth-content-wrap">
      <div class="auth-content">
        <h1 class="title">
          Hello!
        </h1>
        <p class="subtitle">
          Login <span class="subtitle-account">Your Account</span>
        </p>

        <VeeForm
          ref="formRef" :validation-schema="schema" class="login-form"
          @submit="onFormSubmit"
        >
          <Field
            v-slot="{ field, errorMessage }" v-model="model.email" as="div" class="flex flex-col gap-4"
            name="email"
          >
            <InputText v-bind="field" class="form-input" name="email" type="text" :placeholder="t('login.email')" />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>
          <Field
            v-slot="{ field, errorMessage }" v-model="model.password" as="div" class="flex flex-col gap-4"
            name="password"
          >
            <Password
              v-bind="field" type="text" :placeholder="t('login.password')" toggle-mask fluid
              :feedback="false"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>
          <div class="flex gap-4 justify-between tools">
            <div class="flex items-center gap-2">
              <Checkbox v-model="model.rememberMe" name="rememberMe" binary />
              <label for="rememberMe">{{ t("login.rememberMe") }}</label>
            </div>
            <Button
              text :label="t('login.forgotPassword')" class="!p-0"
              @click="$router.push('/forgot-password')"
            />
          </div>
          <Field
            v-slot="{ errorMessage }" v-model="model.google_token" as="div" class="flex flex-col"
            name="google_token"
          >
            <p class="text-lg !m-0" style="font-weight: 600;">
              *CAPTCHA
            </p>
            <GoogleRecaptcha
              ref="recaptchaRef" name="google_token" class="mb-2 mt-2" @verify="onRecaptchaVerify"
              @expired="onRecaptchaExpired" @error="onRecaptchaError"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>
          <Button
            class="login-submit" :loading="loading"
            severity="warn" label="LOGIN" @click="onFormSubmit"
          />
        </VeeForm>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/common/auth-layout.scss';
</style>
