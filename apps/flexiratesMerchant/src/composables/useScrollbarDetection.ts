import type { Ref } from 'vue'
import { onMounted, onUnmounted, ref } from 'vue'

export function useScrollbarDetection(elementRef: Ref<HTMLElement | null>) {
  const hasVerticalScrollbar = ref(false)
  const hasHorizontalScrollbar = ref(false)

  const checkForScrollbars = () => {
    if (!elementRef.value) { return }

    // 检查垂直滚动条
    hasVerticalScrollbar.value = elementRef.value.scrollHeight > elementRef.value.clientHeight

    // 检查水平滚动条
    hasHorizontalScrollbar.value = elementRef.value.scrollWidth > elementRef.value.clientWidth
  }

  // 使用 ResizeObserver 监听元素大小变化
  let resizeObserver: ResizeObserver | null = null

  onMounted(() => {
    if (!elementRef.value) { return }

    // 初始检查
    checkForScrollbars()

    // 监听元素大小变化
    resizeObserver = new ResizeObserver(() => {
      checkForScrollbars()
    })

    resizeObserver.observe(elementRef.value)

    // 监听内容变化（可能导致滚动条出现）
    elementRef.value.addEventListener('scroll', checkForScrollbars)
    window.addEventListener('resize', checkForScrollbars)
  })

  onUnmounted(() => {
    if (resizeObserver) {
      resizeObserver.disconnect()
    }

    if (elementRef.value) {
      elementRef.value.removeEventListener('scroll', checkForScrollbars)
    }

    window.removeEventListener('resize', checkForScrollbars)
  })

  return {
    hasVerticalScrollbar,
    hasHorizontalScrollbar,
    checkForScrollbars, // 导出方法，以便在内容变化时手动调用
  }
}
