import type { DictItem } from '@/services/api/dict'
import { useDictStore } from '@/store/modules/dict'
import { computed, ref } from 'vue'

/**
 * 字典 Composable
 * @param dictType 字典类型
 * @param callFn 字典加载后的回调
 * @returns 字典相关方法和状态
 */
export function useDict(dictType: string, callFn?: (dictList: DictItem[]) => void) {
  const dictStore = useDictStore()
  const dictList = ref<DictItem[]>([])

  // 加载状态
  const isTypeLoading = Reflect.get(dictStore, 'isTypeLoading')
  const loading = Reflect.apply(isTypeLoading, dictStore, [dictType])

  // 加载字典数据
  const loadDict = async () => {
    const getDictByType = Reflect.get(dictStore, 'getDictByType')
    dictList.value = await Reflect.apply(getDictByType, dictStore, [dictType])
    callFn && callFn(dictList.value)
    return dictList.value
  }

  // 获取字典标签
  const getLabel = (value: string | number): string => {
    const item = dictList.value.find(item => item.value === value)
    return item ? item.label : ''
  }

  // 获取字典值
  const getValue = (label: string): string | number | null | undefined => {
    const item = dictList.value.find(item => item.label === label)
    return item ? item.value : undefined
  }

  // 转换为下拉选项
  const options = computed(() => {
    return dictList.value.map(item => ({
      label: item.label,
      value: item.value,
    }))
  })

  // 清除字典缓存
  const clearCache = () => {
    const clearDictCache = Reflect.get(dictStore, 'clearDictCache')
    Reflect.apply(clearDictCache, dictStore, [dictType])
  }

  // 初始化时加载数据
  loadDict()

  return {
    dictList,
    loading,
    loadDict,
    getLabel,
    getValue,
    options,
    clearCache,
  }
}
