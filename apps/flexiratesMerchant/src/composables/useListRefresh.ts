import { onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { useListStore } from '@/store/modules/list'

export function useListRefresh(listName: string, refreshCallback: () => void) {
  const listStore = useListStore()
  const router = useRouter()

  // 监听列表激活
  onActivated(() => {
    if (listStore.getNeedRefresh(listName)) {
      refreshCallback()
      listStore.clearRefreshFlag(listName)
    }
  })

  // 返回上一页并设置刷新标记
  const backWithRefresh = (needBack: boolean = true) => {
    listStore.setNeedRefresh(listName, true)
    if (needBack) {
      router.back()
    }
  }

  const withRefresh = () => {
    listStore.setNeedRefresh(listName, true)
  }

  return {
    backWithRefresh,
    withRefresh,
  }
}
