import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'

export const formatDate = (dateString: string | number | Date | Dayjs | null | undefined, format: string = 'MMM DD h:mm A') => {
  const defaultReturn = '-'
  if (!dateString) {
    return defaultReturn
  }
  const targetDate = dayjs(dateString)
  if (!targetDate.isValid()) {
    return defaultReturn
  }
  return targetDate.format(format)
}

export const formatSearchDate = (searchData: Record<string, any>, formatKeys: string[] | string, format: string = 'YYYY-MM-DD') => {
  const formatKey = Array.isArray(formatKeys) ? formatKeys : [formatKeys]
  const returnData = { ...searchData }
  formatKey.forEach((key) => {
    if (searchData[key]) {
      if (Array.isArray(searchData[key])) {
        returnData[key] = searchData[key].map((date: string) => dayjs(date).format(format))
      }
      else {
        returnData[key] = dayjs(searchData[key]).format(format)
      }
    }
  })
  return returnData
}
