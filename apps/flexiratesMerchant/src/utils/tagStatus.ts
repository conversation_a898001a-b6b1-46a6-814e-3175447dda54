import { DownloadStatus } from '@/constants/download'
import { InvoiceStatus } from '@/constants/invoice'
import { PayoutStatus } from '@/constants/payout'
import { CustomerPlanStatus, PlanStatus } from '@/constants/plan'
import { TransactionStatus } from '@/constants/transaction'

export const getPlanTagStatus = (status: string) => {
  return {
    [PlanStatus.ACTIVE]: 'paid',
    [PlanStatus.INACTIVE]: 'upcoming',
    [PlanStatus.ARCHIVE]: 'failed',
  }[status] || 'info'
}

export const getCustomerPlanTagStatus = (status: string) => {
  return {
    [CustomerPlanStatus.ACTIVE]: 'paid',
    [CustomerPlanStatus.INACTIVE]: 'failed',
    [CustomerPlanStatus.PROCESSING]: 'upcoming',
    [CustomerPlanStatus.PROCESSED]: 'upcoming',
    [CustomerPlanStatus.ON_HOLD]: 'upcoming',
    [CustomerPlanStatus.COMPLETED]: 'upcoming',
    [CustomerPlanStatus.CANCELLED]: 'upcoming',
  }[status] || 'upcoming'
}

export const getTransactionTagStatus = (status: string | number) => {
  return {
    [TransactionStatus.PENDING]: 'upcoming',
    [TransactionStatus.SUCCEEDED]: 'paid',
    [TransactionStatus.FAILED]: 'failed',
    [TransactionStatus.REFUNDED]: 'upcoming',
    [TransactionStatus.DISPUTED]: 'upcoming',
    [TransactionStatus.UNCAPTURED]: 'upcoming',
  }[status] || 'upcoming'
}

export const getDownloadCenterTagStatus = (status: string) => {
  return {
    [DownloadStatus.InProgress]: 'upcoming',
    [DownloadStatus.Succeeded]: 'paid',
    [DownloadStatus.Failed]: 'failed',
    [DownloadStatus.NotStart]: 'upcoming',
  }[status] || 'upcoming'
}

export const getPayoutTagStatus = (status: string | number) => {
  return {
    [PayoutStatus.PENDING]: 'upcoming',
    [PayoutStatus.COMPLETED]: 'paid',
    [PayoutStatus.FAILED]: 'failed',
    [PayoutStatus.PROCESSING]: 'upcoming',
  }[status] || 'upcoming'
}

export const getInvoiceTagStatus = (status: string | number) => {
  return {
    [InvoiceStatus.UNPAID]: 'failed',
    [InvoiceStatus.PARTIAL_PAYMENT]: 'upcoming',
    [InvoiceStatus.FULL_PAYMENT]: 'paid',
  }[status] || 'upcoming'
}
