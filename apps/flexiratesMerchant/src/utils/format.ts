import type { BillingPeriodCustomType } from '@/constants/plan'
import { BillingPeriodType, customPeriodMap, periodMap, ScheduleType } from '@/constants/plan'

export const formatAmount = (amount: number | string, currency: string = 'AUD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(Number(amount))
}

// 显示 ···· 卡号后4位
export const formatBankAccount = (account: string) => {
  return `•••• •••• •••• ${String(account)?.slice(-4)}`
}

export const formatNumber = (num: number, precision: number = 1) => {
  const units: string[] = ['', 'k', 'M', 'B', 'T']
  // 处理 num 为 0 的情况，避免计算 log(0)
  const pow: number = Math.floor(num !== 0 ? Math.log(num) / Math.log(1000) : 0)
  // 确保 pow 不超过单位数组的索引范围
  const powClamped: number = Math.min(pow, units.length - 1)
  // 缩放数值到对应单位
  const scaledNum: number = num / 1000 ** powClamped
  // 四舍五入到指定精度
  const factor: number = 10 ** precision
  const rounded: number = Math.round(scaledNum * factor) / factor
  // 格式化为字符串并移除冗余的小数零
  let formatted: string = rounded.toFixed(precision)
  formatted = formatted.replace(/\.0+$/, '').replace(/\.$/, '')
  return formatted + units[powClamped]
}

export function formatYAxis(value: number) {
  const ranges = [
    { divider: 1e9, suffix: 'B' },
    { divider: 1e6, suffix: 'M' },
    { divider: 1e3, suffix: 'K' },
  ]

  for (const range of ranges) {
    if (value >= range.divider) {
      return `${(value / range.divider).toFixed(1)}${range.suffix}`
    }
  }
  return value.toString()
}

export const formatFrequencyTime = (plan: {
  process_type?: BillingPeriodType | null
  custom_cycle_type?: BillingPeriodCustomType | null
  custom_cycle?: number
  schedule_type?: ScheduleType | null
}) => {
  if (plan.schedule_type === ScheduleType.OneOff) {
    return 'One-off'
  }

  if (!plan.process_type) { return '' }

  if (plan.process_type === BillingPeriodType.Custom) {
    return `
    ${plan.custom_cycle} 
    ${customPeriodMap[plan.custom_cycle_type as BillingPeriodCustomType]}
    `
  }

  return periodMap[plan.process_type as BillingPeriodType] || plan.process_type
}
