import type { App, DirectiveBinding } from 'vue'
import { useUserStore } from '@/store/modules/user'

interface PermissionBinding {
  value: string | string[] | {
    permission: string | string[]
    mode?: 'any' | 'all' // 默认为 'any'
  }
}

/**
 * 权限指令 - 用于按钮和UI元素的权限控制
 * 用法：
 * v-permission="'cardiniaRatePayerCreate'" - 单个权限
 * v-permission="['cardiniaRatePayerCreate', 'cardiniaRatePayerUpdate']" - 多个权限（任意一个）
 * v-permission="{ permission: ['cardiniaRatePayerCreate', 'cardiniaRatePayerUpdate'], mode: 'all' }" - 多个权限（全部）
 */
const permission = {
  mounted(el: HTMLElement, binding: DirectiveBinding<PermissionBinding['value']>) {
    checkPermission(el, binding)
  },

  updated(el: HTMLElement, binding: DirectiveBinding<PermissionBinding['value']>) {
    checkPermission(el, binding)
  },
}

function checkPermission(el: HTMLElement, binding: DirectiveBinding<PermissionBinding['value']>) {
  const userStore = useUserStore()
  const { value } = binding

  let hasAccess = false

  if (typeof value === 'string') {
    // 单个权限
    hasAccess = userStore.hasPermission(value)
  }
  else if (Array.isArray(value)) {
    // 多个权限，默认任意一个
    hasAccess = userStore.hasAnyPermission(value)
  }
  else if (typeof value === 'object' && value !== null) {
    // 对象形式，可以指定模式
    const { permission: permissions, mode = 'any' } = value
    const permissionList = Array.isArray(permissions) ? permissions : [permissions]

    if (mode === 'all') {
      hasAccess = userStore.hasAllPermissions(permissionList)
    }
    else {
      hasAccess = userStore.hasAnyPermission(permissionList)
    }
  }

  // 控制元素显示/隐藏
  if (hasAccess) {
    el.style.display = ''
    el.removeAttribute('disabled')
  }
  else {
    el.style.display = 'none'
  }
}

/**
 * 权限指令（禁用模式） - 用于按钮和UI元素的权限控制
 * 没有权限时禁用元素而不是隐藏
 * 用法：v-permission-disable="'cardiniaRatePayerCreate'"
 */
const permissionDisable = {
  mounted(el: HTMLElement, binding: DirectiveBinding<PermissionBinding['value']>) {
    checkPermissionDisable(el, binding)
  },

  updated(el: HTMLElement, binding: DirectiveBinding<PermissionBinding['value']>) {
    checkPermissionDisable(el, binding)
  },
}

function checkPermissionDisable(el: HTMLElement, binding: DirectiveBinding<PermissionBinding['value']>) {
  const userStore = useUserStore()
  const { value } = binding

  let hasAccess = false

  if (typeof value === 'string') {
    hasAccess = userStore.hasPermission(value)
  }
  else if (Array.isArray(value)) {
    hasAccess = userStore.hasAnyPermission(value)
  }
  else if (typeof value === 'object' && value !== null) {
    const { permission: permissions, mode = 'any' } = value
    const permissionList = Array.isArray(permissions) ? permissions : [permissions]

    if (mode === 'all') {
      hasAccess = userStore.hasAllPermissions(permissionList)
    }
    else {
      hasAccess = userStore.hasAnyPermission(permissionList)
    }
  }

  // 控制元素启用/禁用
  if (hasAccess) {
    el.removeAttribute('disabled')
    el.classList.remove('permission-disabled')
  }
  else {
    el.setAttribute('disabled', 'true')
    el.classList.add('permission-disabled')
  }
}

/**
 * 注册权限指令 - 用于按钮和UI元素的权限控制
 */
export function setupPermissionDirectives(app: App) {
  app.directive('permission', permission)
  app.directive('permission-disable', permissionDisable)
}

export { permission, permissionDisable }
