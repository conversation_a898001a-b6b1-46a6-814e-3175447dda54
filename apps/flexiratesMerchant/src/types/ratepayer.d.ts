declare namespace Ratepayer {

  interface Info {
    customer_id: string
    customer_property: CustomerProperty
    customer_user: null | CustomerUser
    customer_banking?: CustomerBanking[]
    customer_banking_secondary?: CustomerBanking
    customer_plan: CustomerPlan[]
    id: number
    user_profile: null | UserProfile
  }

  interface DetailInfo {
    customer: DetailInfoCustomer
    customer_banking: CustomerBanking
    customer_banking_id: number
    customer_banking_secondary: CustomerBanking
    customer_id: string
    final_payment_date: string
    fiscal_year: string
    frequency: string
    id: number
    last_payment: LastPayment
    merchant_id: string
    next_payment_amount: string
    next_process_date: string
    plan_id: string
    price: DetailInfoPrice
    process_type: number
    property: DetailInfoProperty
    related_id: number
    secondary_customer_banking_id: number
    statistics: DetailInfoStatistics
    status: number
  }

  interface DetailInfoLastPayment {
    frequency: string
    payment_amount: string
    payment_at: string
    process_type: number
  }

  interface DetailInfoCustomer {
    customer_id: string
    customer_user: DetailInfoCustomerUser
    id: number
    name: string
    user_profile: DetailInfoUserProfile
  }

  interface DetailInfoCustomerUser {
    created_at: string
    customer_id: string
    email: string
    id: number
    last_login_time: string
    login_times: number
    mobile: string
  }

  interface DetailInfoUserProfile {
    address_line_1: string
    address_line_2: string
    avatar: string
    city: string
    country: string
    country_iso2: string
    created_at: string
    customer_id: string
    deleted_at: null
    first_name: string
    id: number
    last_name: string
    postcode: string
    state: string
    updated_at: string
  }

  interface DetailInfoPrice {
    amount_per_unit: string
    id: number
    plan_id: string
  }

  interface DetailInfoProperty {
    id: number
    initial_remaining_amount: string
    nickname: string
    postcode: string
    property_number: string
    street_address: string
    suburb: string
  }

  interface DetailInfoStatistics {
    full_rate_amount: string
    paid_amount: string
    remaining_amount: string
    remaining_schedule_number: number
  }

  interface CustomerProperty {
    created_at: string
    customer_id: string
    deleted_at: null
    id: number
    initial_full_amount: string
    initial_remaining_amount: string
    nickname: string
    postcode: string
    property_id: number
    property_number: string
    street_address: string
    suburb: string
    updated_at: string
  }

  interface CustomerUser {
    customer_id: string
    email: string
    mobile: string
  }
  interface UserProfile {
    address_line_1: string
    city: string
    customer_id: string
    first_name: string
    id: number
    last_name: string
    postcode: string
    state: string
  }

  interface Schedule {
    address: string
    default_payment_plan: number
    dict_payment_plan: DictPaymentPlan[]
    last_payment_date: string
    property_number: string
    suburb: string
    total_amount_due: string
  }

  interface DictPaymentPlan {
    label?: string
    value?: number
  }

  interface Note {
    content?: string
    created_at?: string
    customer_id?: string
    id?: number
    title?: string
    updated_at?: string
  }

  interface CustomerBanking {
    account_name: string
    account_no: string
    bsb: null | string
    created_at: string
    credit_brand: number
    credit_type: number | null
    customer_id: string
    expiration_month: null | string
    expiration_year: null | string
    first_name: null | string
    id: number
    last_name: null | string
    type: number
    /**
     * 信用卡权重，枚举：1：primary / 2：secondary / 3：backup
     */
    weight: number

    last_payment?: {
      transaction?: {
        payment_amount?: string
      }
    } | null
  }

  interface CustomerPlan {
    account_code: null
    branding_theme_id: null
    cancelled_at: null
    cancelled_uid: number
    created_at: string
    created_uid: number
    creation_from: number
    custom_cycle: number
    custom_cycle_type: number
    customer_banking_id: number
    customer_id: string
    deleted_at: null
    deleted_uid: number
    description: string
    edited_times: number
    enable: boolean
    end_date: string
    end_date_type: number
    end_terms: number | null
    fail_times: number
    final_payment_amount: string
    final_payment_date: string
    first_payment_date: string
    id: number
    is_inclusive_gst: boolean
    is_surcharge: boolean
    merchant_id: string
    next_process_date: string
    pause_end_date: null
    pause_start_date: null
    plan_id: string
    plan_name: string
    pricing_model: number
    process_terms: number
    process_time: string
    process_type: number
    related_id: number
    remaining_amount: string
    schedule_type: number
    secondary_customer_banking_id: number
    start_date: string
    status: number
    tiered_type: number
    type: number
    units: number
    updated_at: string
    updated_uid: number
  }
}
