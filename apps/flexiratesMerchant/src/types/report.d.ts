declare namespace Report {
  interface Overview {
    total_payments_received: OverviewItem
    total_amount_received: OverviewItem
    total_amount_arrears: OverviewItem
    total_number_failed: OverviewItem
    total_number_registered: OverviewItem

    total_active_registered: OverviewItem
    total_self_registered: OverviewItem
    total_merchant_registered: OverviewItem

    registered_count: {
      active: number
      inactive: number
      cancelled: number
    }
  }

  interface OverviewItem {
    current: string
    last: string
  }

  interface Monthly {
    [key: string]: string
  }

  interface Annual {
    current: {
      [key: string]: string
    }
    last: {
      [key: string]: string
    }
  }
}
