declare namespace Api {
  interface RatepayerListReq extends CommonSearchListParams {
    customer_id: string
  }

  interface RatepayerListRes extends CommonListRes {
    data: Ratepayer.Info[]
  }

  interface RatepayerDetailReq {
    id: string
  }

  interface RatepayerScheduleReq {
    property_number: string
    verification_code: string
  }

  interface RatepayerCreateReq {
    banking?: RatepayerCreateBanking
    city: string
    email: string
    first_name: string
    last_name: string
    mobile: string
    payment_frequency: RatepayerCreatePaymentProperty
    postcode: string
    /**
     * 页面上所需对应信息从接口【公共部分-获取Property信息】获取
     */
    property?: RatepayerScheduleReq
    state: string
    street_address: string
  }

  interface RatepayerCreateBanking {
    /**
     * 银行账号，type=1 时必填
     */
    bank?: RatepayerCreateBank
    /**
     * 信用卡，type=2 时必填
     */
    card?: RatepayerCreateCard
    /**
     * 支付类型，可选值：{
     * 1: 'Bank Account',
     * 2: 'Card Number'
     * }
     */
    type: number
    /**
     * 权重
     */
    weight: number
  }

  interface RatepayerCreateBank {
    /**
     * 账号名称
     */
    account_name: string
    /**
     * 银行账号
     */
    account_no: string
    bsb: string
    /**
     * 昵称
     */
    nickname?: string
  }

  interface RatepayerCreateCard {
    /**
     * 卡号
     */
    card_number: string
    /**
     * 城市
     */
    city: string
    /**
     * 国家代码
     */
    country_iso2: string
    /**
     * 邮箱
     */
    email: string
    /**
     * 月份，MM
     */
    expiration_month: string
    /**
     * 年份，YYYY
     */
    expiration_year: string
    /**
     * 名
     */
    first_name: string
    /**
     * 姓
     */
    last_name: string
    /**
     * 地址1
     */
    line_1: string
    /**
     * 地址2
     */
    line_2?: string
    /**
     * 持卡名称
     */
    name_on_card: string
    /**
     * 昵称
     */
    nickname?: string
    /**
     * 电话
     */
    phone: string
    /**
     * 邮编
     */
    postcode: string
    /**
     * CVV
     */
    security_code: string
    /**
     * 州
     */
    state: string
  }

  interface RatepayerCreatePaymentProperty {
    /**
     * 每个周期支付的金额
     */
    amount?: number
    /**
     * 日期格式
     */
    first_payment_date: string
    /**
     * 字典，可选值：{
     * 1: 'Weekly',
     * 2: 'Forthnightly',
     * 3: 'Quarterly', // 默认值
     * 4: 'Monthly',
     * 5: 'Full Amount'
     * }
     */
    payment_plan: number
  }

  interface RatepayerCreateRes {

  }

  interface RatepayerUpdateReq {
    first_name: string
    last_name: string
    email: string
    mobile: string
    street_address: string
    city: string
    state: string
    postcode: string
  }

  interface RatepayerNotesReq extends CommonSearchListParams {
    content?: string
    created_at?: string
    customer_id?: string
    id?: number
    title?: string
    updated_at?: string
  }

  interface RatepayerNoteCreateReq {
    content: string
    customer_id: string
    title: string
  }

  interface RatepayerNoteCreateRes {

  }

  interface CustomerActivityLogListReq extends CommonSearchListParams {
    /**
     * 页码
     */
    page?: number
    /**
     * 每页条数
     */
    page_size?: number

    customer_id?: string
  }

  interface UserActivityLogItem {
    activity_detail: string
    activity_status: number
    activity_type: number
    activity_type_desc: string
    created_at: string
    customer_id: string
    id: number
  }
  interface CustomerActivityLogListData {
    /**
     * 活动日志列表
     */
    data: UserActivityLogItem[]
    /**
     * 当前页码
     */
    current_page: number
    /**
     * 每页条数
     */
    per_page: number
    /**
     * 总条数
     */
    total: number
  }

  interface CustomerActivityLogListRes extends CommonRes<CustomerActivityLogListData> {
    code: number
    message: string
    data: CustomerActivityLogListData
  }
}
