declare namespace Api {

  interface PlanListReq extends Partial<Plan.Info> {
    /**
     * 页码
     */
    'page'?: number
    'page_size'?: number
    /**
     * 排序字段
     */
    'sort_by'?: string
    /**
     * 排序方向
     */
    'sort_order'?: 'asc' | 'desc'

    'created_at[]'?: string[]

    'name'?: string
  }

  interface PlanCreatePriceReq {
    /**
     * 额外费用
     */
    amount_flat_fee?: number | null
    /**
     * 金额类型，每一个单位的单价
     */
    amount_per_unit: number
    /**
     * 币种，三位ISO码，如：AUD
     */
    currency: string
    /**
     * 起始单位数
     */
    first_unit: number
    /**
     * 结束单位数，无限使用这个：∞
     */
    last_unit: number | string
    /**
     * 单位类型，1：Unit-based 2：Volume-based
     */
    unit_type?: string | null
    /**
     * 单位数量
     */
    units: number
  }

  interface PlanCreateReq {
    /**
     * 自定义周期数量，process_type为custom时必填，every x，每x days/weeks等
     */
    custom_cycle?: number
    /**
     * 自定义周期类型，process_type为custom时必填，说明如下：
     * 1：days 2：weeks 3：months 4：years
     */
    custom_cycle_type?: number
    /**
     * 描述
     */
    description?: string
    /**
     * 有效期截止日期，与 is_good_till_cancel 二选一必填，yyyyMMdd
     */
    end_date?: string
    /**
     * 是否长期有效，与 end_date 二选一必填，可选，0否1是
     */
    is_good_till_cancel?: boolean
    /**
     * 是否包含GST，可选，0否1是
     */
    is_inclusive_gst: boolean
    /**
     * 计划名称
     */
    plan_name: string
    /**
     * 价格表，一条或多条价格数据
     */
    prices: PlanCreatePriceReq[]
    /**
     * 费用类型，点开 more options 时必填，说明如下：
     * 1：Flat Rate 固定金额(One-off固定是这个)；2：Package Pricing 套餐金额；3：Tiered Pricing
     */
    pricing_model?: number
    /**
     *
     * 执行频率类型，schedule_type为Recurring时必填，1：one-off，2：daily，3：weekly，4：fortnightly，5：monthly，6：Every
     * 3 months， 7：Every 6 months，8：yearly，9：minutely，10：hourly，11：datetime，12：custom
     */
    process_type?: number
    /**
     * 任务类型，1：Recurring 持续的，2：One-off 一次的
     */
    schedule_type: number
    /**
     * 阶梯计费类型，pricing_model 为 Tiered Pricing 时必填，说明如下：
     * 1：Volume 批量定价，只收对应阶梯的费用 2：Graduated 阶梯式定价，每个阶梯分别都收费
     */
    tiered_type?: number

    invoice_theme_id: string
    invoice_account_code: string

  }

  interface PlanCreateByCustomerReq extends PlanCreateReq {
    customer_id: string
    units?: number
    theme?: string
    logo?: string
  }

  interface PlanEditReq extends PlanCreateReq {
    status: number
    plan_id: string
  }

  interface PlanUpdatePayMethodReq {
    plan_id: string
    customer_id: string
  }

  interface UpdateCustomerPlanInfoReq extends PlanCreateReq {
    /**
     * 客户编号
     */
    customer_id: string
    /**
     * 计划编号
     */
    plan_id: string
  }
}
