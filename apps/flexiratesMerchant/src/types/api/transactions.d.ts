declare namespace Api {
  interface Stat {
    count?: number
    status?: number
    status_text?: string
  }

  interface TransactionListRes extends CommonListRes<Transaction.Info[]> {
    current_page?: number
    per_page?: number
    stat?: Stat[]
    total?: number
  }

  interface TransactionListReq extends Transaction.Info, CommonSearchListParams {
    /**
     * 排序字段
     */
    'sort_by'?: string
    /**
     * 排序方向
     */
    'sort_order'?: 'asc' | 'desc'
    /**
     * 交易时间范围，[start, end]，格式：yyyy-MM-dd
     */
    'remit_date[]'?: string[]

    'keyword'?: string

    'payment_method'?: number | null
  }
}
