declare namespace Api {
  /**
   * Request payload for the login endpoint.
   *
   * @property email - The user's email address.
   * @property password - The user's password.
   * @property rememberMe - Whether to remember the user's login.
   */
  interface FlexiratesUserLoginReq {
    /**
     * 邮箱，email登录：与 mobile_phone 二选一必填
     */
    email: string

    country_code?: string
    /**
     * 移动号码，mobile登录：与 email 二选一必填
     */
    mobile_phone?: string
    /**
     * 密码，email登录：email不为空时必填
     */
    password: string
    /**
     * 验证码，mobile登录：mobile_phone不为空时必填
     */
    verification_code: string
    rememberMe: boolean
    google_token: string
  }

  type FlexiratesUserLoginReq = LoginWithEmail | LoginWithMobile

  interface FlexiratesUserLoginRes {
    refresh_token: string
    access_token: string
  }

  interface RegisterAccountInfoReq {
    property_number: string
    verification_code: string
  }

  interface RegisterAccountInfoRes {
    assessment_number: string
    end_date: string
    full_amount: string
    instalment_1_amount: string
    instalment_1_due: string
    instalment_2_mount: string
    instalment_2_due: string
    instalment_3_amount: string
    instalment_3_due: string
    instalment_4_amount: string
    instalment_4_due: string
    postcode: string
    property_address: string
    property_suburb: string
    start_date: string
    active_register_flag: string
    active_registration_id: string
    allow_quaterly: string
    allow_register: string
    message: string
    scheduled_payments_flag: string
    payment_plan?: {
      label: string
      value: string
    }[]
  }

  interface FlexiratesUserRegisterReq {
    /**
     * 邮箱
     */
    email: string
    first_name: string
    last_name: string
    /**
     * 手机号码
     */
    mobile_phone: string
    /**
     * 邮件通知，（0：关闭；1：开启）
     */
    notice_switch: number

    /**
     * 1：Bank Account / 2：Credit
     */
    type: 1 | 2

    /**
     * 物业编号
     */
    property_number?: number

    /**
     * 验证码
     */
    verification_code?: string

    /**
     * 银行账号信息，type为Bank Account时必需
     */
    bank?: FlexiratesUserRegisterBank
    /**
     * 卡信息，type为Credit时必需
     */
    card?: FlexiratesUserRegisterCard

    payment_frequency: {
      /**
       * 首次时间
       */
      first_payment_date: string
      /**
       * 类型，字典值（1：Weekly；2：Fortnightly；3：Monthly；4：Full amount*）
       */
      payment_plan: number
    }
  }

  interface FlexiratesUserRegisterCard {
    card_number: string
    security_code: string
    name_on_card: string
    expiration_year: string
    expiration_month: string
  }

  interface FlexiratesUserRegisterBank {
    bsb: string
    account_no: string
    account_name: string
  }

  interface FlexiratesUserRegisterRes {
    message: string
  }

  interface SendVerificationCodeReq {
    mobile_phone: string
    country_code: string
  }

  interface SendVerificationCodeRes {
    message: string
  }
}
