declare namespace Api {
  interface ScheduleListReq extends RequestListParams {
    property_id: string
    type: string
    status: string
    sort_by: string
    sort_order: string
    page: number
    page_size: number
    customer_id?: string
  }

  interface ScheduleListRes extends CommonListRes {
    data: Schedules.Info
  }

  interface ScheduleEditReq {
    /**
     * edit_type=3 必填，
     */
    amount?: number
    /**
     * 字典，可选值：{
     * 1: 'Skip Next Payment',
     * 2: 'Pause Schedule',
     * 3: 'Edit Schedule Details'
     * }
     */
    edit_type: number
    /**
     * edit_type=3 必填，
     */
    first_payment_date?: string
    /**
     * 房产信息ID编号
     */
    id: number
    /**
     * edit_type=2 必填
     */
    pause_end_date?: string
    /**
     * edit_type=2 必填
     */
    pause_start_date?: string
    /**
     * edit_type=3 必填，字典，可选值：{
     * 1: 'Weekly',
     * 2: 'Forthnightly',
     * 3: 'Quarterly', // 默认值
     * 4: 'Monthly',
     * 5: 'Full Amount'
     * }
     */
    payment_plan?: number
  }

  type ScheduleDetailRes = {
    id: number
    due_date: string
    amount: string
    frequency: string
    status_desc: string
    failed_times: number
    failable_times: number
    edited_times: number
    editable_count: number
    credit_brand: number
    credit_brand_desc: string
    account_no: string
  }[]

  interface CompletedScheduleDetailCustomerBanking {
    account_no: string
    credit_brand: number
    trans_no: string
    [property: string]: any
  }

  interface CompletedScheduleDetailCustomerPlan {
    edited_times: number
    fail_times: number
    trans_no: string
    [property: string]: any
  }

  interface PropertiesEditCountScheduleReq {
    id: number
    edit_count: number
  }
}
