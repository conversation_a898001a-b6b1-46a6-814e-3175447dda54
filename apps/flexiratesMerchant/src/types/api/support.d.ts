declare namespace Api {
  interface CardiniaCancelListReq extends CommonSearchListParams {
    status?: number
    created_at?: string[]
    keyword?: string
  }
  interface CardiniaCancelListRes extends CommonListRes {
    data: Support.PropertyInfo[]
  }

  interface CardiniaCancelAuditReq {
    id: number
    remark?: string
    /**
     * 可选1：通过 Approved 2：拒绝 Rejected
     */
    status: number
  }

}
