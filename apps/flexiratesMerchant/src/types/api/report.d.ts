declare namespace Api {
  interface ReportOverviewReq {
    month: string
  }

  interface ReportMonthlyReq {
    month: string
  }

  interface ReportAnnualReq {
    year: string
  }

  interface ReportRegistrationsRes {
    payments: {
      name?: string
      value?: number
    }[]
    properties: {
      name?: string
      value?: number
    }[]
    registrations: {
      name?: string
      value?: number
    }[]
  }
}
