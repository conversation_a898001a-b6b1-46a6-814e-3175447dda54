declare namespace Api {
  interface MerchantQuestionnaireRequest {
    abn: string
    average_of_days_prepays: string
    average_sales_amount_per_order: string
    business_category?: string
    contact_name: string
    email: string
    estimate_monthly_turnover: string
    /**
     * Google验证token
     */
    google_token: string
    percentage_of_orders_prepaid: string
    phone: string
    /**
     * [0,1]；枚举查询字典：official_proposed_services
     */
    proposed_services: string[]
    website: string
    [property: string]: any
  }
  interface PayerSupportRequest {
    contact_email: string
    contact_name: string
    customer_name: string
    ddr_id_or_crn: string
    /**
     * Google验证token
     */
    google_token: string
    phone: string
    support_request: string
    [property: string]: any
  }
  interface BillerSupportRequest {
    /**
     * Biller Id
     */
    biller_id?: string
    /**
     * 邮箱
     */
    email?: string
    /**
     * 谷歌验证
     */
    google_token?: string
    /**
     * 名称
     */
    name?: string
    /**
     * 组织名称
     */
    organisation_name?: string
    /**
     * 手机号
     */
    phone?: string
    /**
     * 支持请求
     */
    support_request?: string
    [property: string]: any
  }

  interface sendMessageRequest {
    /**
     * 公司名称
     */
    company_name: string
    /**
     * 邮箱
     */
    email: string
    enquiry: string
    first_name: string
    /**
     * 谷歌验证
     */
    google_token: string
    last_name: string
    /**
     * 消息
     */
    message: string
    /**
     * 手机号
     */
    phone: string
    /**
     * 产品
     */
    product?: string
    /**
     * 联系类型
     */
    request_category: string
    [property: string]: any
  }
  interface feedbackRequest {
    /**
     * 公司名称
     */
    company_name: string
    /**
     * 客户名称
     */
    customer_name: string
    /**
     * 邮箱
     */
    email: string
    /**
     * 反馈类型
     */
    feedback_type: string
    first_name: string
    /**
     * 谷歌验证
     */
    google_token?: string
    land_line: string
    last_name: string
    /**
     * 消息
     */
    message: string
    /**
     * 手机号
     */
    phone: string
    [property: string]: any
  }
}
