declare namespace Api {
  interface HomeSummaryData {
    /**
     * 净收入
     */
    net_revenue: string
    /**
     * 净收入百分比
     */
    net_revenue_rate: string
    /**
     * 总收入
     */
    total_revenue: string
    /**
     * 总收入百分比
     */
    total_revenue_rate: string
  }

  interface HomeChartData {
    monthly: {
      current: {
        [key: string]: string
      }
      last: {
        [key: string]: string
      }
    }
    weekly: {
      current: {
        [key: string]: string
      }
      last: {
        [key: string]: string
      }
    }
  }

}
