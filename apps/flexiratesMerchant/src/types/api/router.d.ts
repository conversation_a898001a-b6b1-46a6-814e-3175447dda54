declare namespace Api {

  // interface RouterItem {
  //   i18nKey: string
  //   path: string
  //   name: string
  //   component: string
  //   isHide?: boolean
  //   redirect?: string | { name: string }
  //   isHideBreadcrumb?: boolean
  //   isSeparator?: boolean
  //   breadcrumbTitle?: string
  //   children?: RouterItem[]
  //   icon?: string
  //   isKeepAlive?: boolean
  // }

  interface RouterItem {
    breadcrumbTitle: string
    children: RouterItem[]
    component: string
    i18nKey: string
    icon: string
    id: number
    isHide: 0 | 1
    isHideBreadcrumb: 0 | 1
    isKeepAlive: 0 | 1
    label: null
    name: string
    parent_id: number
    path: string
    redirect: string
    isSeparator: 0 | 1
  }

  interface RouterRes {
    routes: RouterItem[]
  }
}
