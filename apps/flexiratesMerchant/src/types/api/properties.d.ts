declare namespace Api {
  interface PropertiesListReq extends CommonSearchListParams {
    'created_at[]': string[]
    'property_id': string | null
    'keyword': string
    'category': string | number | null
    'status': string | number | null
  }

  interface PropertiesDetailReq {
    id: string
  }

  interface PropertiesListRes extends CommonListRes {
    data: Properties.Info[]
  }

  interface PropertiesEditScheduleDetailReq {
    customer_id: string
    property_id: string
  }

  interface PropertiesEditScheduleDetailRes {
    address: string
    first_payment_date: string
    id: number
    next_payment_amount: string
    /**
     * 对应Next Due日期
     */
    next_payment_date: string
    /**
     * 名称
     */
    nickname: string
    /**
     * 返回默认的主卡和副卡两张卡
     */
    payment_method: FlexiratesPropertyDetailPaymentMethod[]
    property_number: string
    schedule_details: FlexiratesPropertyDetailScheduleDetails
    status: number
    status_desc: string
    total_amount: string
    total_paid: string
    total_remaining: string
    transaction_history: FlexiratesPropertyDetailTransactionHistory[]

    adjust_final_payment_amount: string
    adjust_final_payment_date: string
    adjust_next_payment_amount: string
    adjust_next_payment_date: string
    first_payment_date: string
    final_payment_date: string
    date_range: {
      pause: {
        start: string
        end: string
      }
      first_payment_dates: Record<string, { start: string, end: string }>
    }
  }

  interface FlexiratesPropertyDetailPaymentMethod {
    account_name: string
    account_no: string
    expiration_month: string
    expiration_year: string
    id: number
    last_payment_amount: string
    /**
     * 最后/上一次支付日期
     */
    last_payment_date: string
    /**
     * 支付类型，可选值：{
     * 1: 'Bank Account',
     * 2: 'Card Number'
     * }
     */
    type: number
    weight: number
    weight_desc: string
  }

  interface PropertiesEditScheduleReq {
    /**
     * edit_type=3 必填，
     */
    amount: number
    /**
     * 字典，可选值：{
     * 1: 'Skip Next Payment',
     * 2: 'Pause Schedule',
     * 3: 'Edit Schedule Details'
     * }
     */
    edit_type: number
    /**
     * edit_type=3 必填，
     */
    first_payment_date: string
    /**
     * 房产信息ID编号
     */
    id: number
    /**
     * edit_type=2 必填
     */
    pause_end_date: string
    /**
     * edit_type=2 必填
     */
    pause_start_date: string
    /**
     * edit_type=3 必填，字典，可选值：{
     * 1: 'Weekly',
     * 2: 'Forthnightly',
     * 3: 'Quarterly', // 默认值
     * 4: 'Monthly',
     * 5: 'Full Amount'
     * }
     */
    payment_plan: number
  }
}
