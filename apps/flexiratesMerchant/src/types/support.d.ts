declare namespace Support {
    interface PropertyInfo {
        apply_by?: number
        apply_from?: number
        check_by?: number
        check_from?: number
        checked_at?: null
        created_at?: string
        current_paid_amount?: string
        current_remaining_amount?: string
        customer_id?: string
        customer_plan?: CustomerPlan
        customer_property?: CustomerProperty
        id?: number
        plan_id?: string
        property_id?: number
        remark?: string
        status?: number
        updated_at?: string
    }
    interface CustomerPlan {
        account_code: null
        branding_theme_id: null
        cancelled_at: null
        cancelled_uid: number
        created_at: string
        created_uid: number
        creation_from: number
        custom_cycle: number
        custom_cycle_type: number
        customer_banking_id: number
        customer_id: string
        deleted_at: null
        deleted_uid: number
        description: string
        edited_times: number
        enable: boolean
        end_date: string
        end_date_type: number
        end_terms: number
        fail_times: number
        final_payment_amount: string
        final_payment_date: string
        first_payment_date: string
        id: number
        is_inclusive_gst: boolean
        is_surcharge: boolean
        merchant_id: string
        next_process_date: string
        pause_end_date: null
        pause_start_date: null
        plan_id: string
        plan_name: string
        prices: Price[]
        pricing_model: number
        process_terms: number
        process_time: string
        process_type: number
        related_id: number
        remaining_amount: string
        schedule_type: number
        secondary_customer_banking_id: number
        start_date: string
        status: number
        tiered_type: number
        type: number
        units: number
        updated_at: string
        updated_uid: number
    }

    interface Price {
        amount_flat_fee?: string
        amount_per_unit?: string
        created_at?: string
        created_uid?: number
        currency?: string
        deleted_at?: null
        deleted_uid?: number
        first_unit?: number
        id?: number
        last_unit?: null
        plan_id?: string
        unit_type?: string
        units?: string
        updated_at?: string
        updated_uid?: number
    }

    interface CustomerProperty {
        created_at: string
        customer_id: string
        deleted_at: null
        id: number
        initial_full_amount: string
        initial_remaining_amount: string
        nickname: string
        postcode: string
        property_id: number
        property_number: string
        street_address: string
        suburb: string
        updated_at: string
    }
}