import type { CustomerAddressType, CustomerBankingType } from '@/constants/customer'

export { }

declare global {
  namespace Customer {

    interface Address {
      type?: CustomerAddressType
      line_1: string
      line_2: string
      line_3: string
      suburb: string
      postcode: string
      state: string
    }

    interface BankingBank {
      id: number
      customer_id: string
      type: number
      bsb: any
      account_no: string
      account_name: string
      company_name: string
      country_iso2: string
      first_name: string
      last_name: string
      line_1: string
      line_2: string
      city: string
      state: string
      phone: string
      email: string
      payment_instrument_id: string
      expiration_date: string
      security_code: string
      credit_type: any
      credit_brand: any
      credit_weight: number
      postcode: string
      shipping_id: any
      enable: number
      created_uid: number
      updated_uid: number
      deleted_uid: number
      created_at: string
      updated_at: string
      deleted_at: any
    }

    interface CommunicationConfig {
      type?: number
      type_name?: string
      email?: number
      __is_loading?: boolean
    }

    interface Info {
      id?: string
      customer_id?: string
      type: CustomerBankingType
      abn: string
      title: string
      name: string
      birthday: string | Date
      phone_area_daytime: string
      phone_no_daytime: string
      phone_area_afterhour: string
      phone_no_afterhour: string
      phone_area_fax: string
      phone_no_fax: string
      phone_mobile: string
      email_primary: string
      email_secondary: string
      addresses?: Address[]
      bankings?: Banking[]
      customer_plan?: Plan.Info[]
      billing_details?: string
      language?: string
      theme?: string
      last_payment_date?: string
      customer_banking?: BankingBank[]
      communication_config?: CommunicationConfig[]
      created_at?: string
      address?: string
      description?: string
    }

    interface History {
      communication_id?: string
      created_at?: string
      customer_id?: string
      deleted_at?: null
      detail?: string
      id?: number
      merchant_id?: string
      /**
       * 类型，1：Email 2：SMS
       */
      type?: number
    }
  }
}
