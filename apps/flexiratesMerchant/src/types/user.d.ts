declare namespace User {
  interface Info {
    id: number
    name: string
    email?: string
    avatar?: string
    merchant_id?: string
    mfa_check: number
    status: number
    type: number
    xero_link: boolean
  }

  interface Profile {
    id?: number
    fullName: string
    nickName: string
    gender: string
    avatar: string
    email: string
    emails: Email[]
  }

  interface UserInfo {
    mfa_check: number
    created_at: string
    email: string
    email_verified_at: string
    google2fa_code: string
    google2fa_secret: string
    id: number
    merchant_id: string
    name: string
    status: number
    type: number
    xero_link: boolean
    updated_at: string
  }

  interface UserCreateReq {
    /**
     * 是否开启MFA验证 ，0：关闭，1：开启
     */
    mfa_check?: number
    /**
     * 邮箱
     */
    email: string
    /**
     * 密码
     */
    password: string
    /**
     * 角色ID，例：1,2,3
     */
    roles?: string
    /**
     * 用户名
     */
    user_name: string
  }

  interface UserUpdateReq extends Partial<UserCreateReq> {}

  interface UserInfoUpdateReq {
    /**
     * MFA开关，(0:关闭,1:开启)
     */
    mfa_check: number
    /**
     * 头像
     */
    avatar: string
    /**
     * 邮箱
     */
    email: string
    /**
     * 账户名称
     */
    user_name: string
  }

  interface UserPasswordUpdateReq {
    password: string
    new_password: string
  }

}
