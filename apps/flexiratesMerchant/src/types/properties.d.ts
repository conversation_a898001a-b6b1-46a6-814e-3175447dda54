declare namespace Properties {

  interface Info {
    /**
     * 未支付金额
     */
    amount_owing: string
    created_at: string
    customer_id: string
    customer_profile: null | Profile
    deleted_at: null
    id: number
    initial_full_amount: string
    /**
     * 总金额
     */
    initial_remaining_amount: string
    nickname: string
    postcode: string
    property_id: number
    property_number: string
    street_address: string
    suburb: string
    updated_at: string
    transaction: Transaction.Info[]

    customer_user: {
      customer_id: string
      email: string
      mobile: string
    }
  }

  interface DetailInfo {
    customer: Ratepayer.DetailInfoCustomer
    customer_property: Ratepayer.DetailInfoProperty
    id: number
    statistics: Ratepayer.DetailInfoStatistics
    /**
     * 交易历史
     */
    fiscal_year: string
    transaction: Transaction.Info[]
  }

  interface Profile {
    customer_id: string
    first_name: string
    last_name: string
  }
}
