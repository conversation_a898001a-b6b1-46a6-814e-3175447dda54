declare namespace FlexiratesTransaction {
  interface Info {
    created_at?: string
    id?: number
    payment_details?: PaymentDetail[]
    payment_method_id?: number
    property_id?: number
    status?: number
    trans_no?: string
  }
  interface PaymentDetail {
    created_at?: string
    id?: number
    payment_amount?: string
    payment_currency?: string
    payment_date?: string
    status?: number
    trans_no?: string
  }
  interface PropertyInfo {
    id?: number
    street_address?: string
  }
  interface BankingInfo {
    payment_method: string
    id: number
  }
}
