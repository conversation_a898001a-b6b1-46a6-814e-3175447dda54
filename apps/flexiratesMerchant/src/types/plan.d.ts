declare namespace Plan {

  interface Info {
    /**
     *
     * 执行类型，执行类型，1：one-off，2：daily，3：weekly，4：fortnightly，5：monthly，6：quarterly，7：yearly，8：minutely，9：hourly
     * 10：Datetime
     */
    process_type?: string | number | null
    /**
     * 最大金额，随便填个金额吧
     */
    amount?: string | number | null
    /**
     * plan_id编号，plan_id编号，可不填，会自动生成，如果前端需要提前显示，请从接口获取后放这里
     */
    plan_id?: string
    plan_name: string | null
    description: string | null
    /**
     * 启用结束时间，模板启用结束时间，时间格式 yyyy-MM-dd HH:mm:ss，如2025-01-01 12:00:00
     */
    end_date: Date | null

    // 收费类型
    schedule_type: 1 | 2 | 3

    currency: string | null

    billing_period: string | null

    pricing_model?: number | null

    billing_period_custom?: string | null
    billing_period_custom_unit?: string | null

    plan_status?: string | Date | null

    end_date_type?: 1 | 2 | 3

    start_date?: string

    end_terms?: number

    is_inclusive_gst?: boolean

    is_surcharge?: boolean

    custom_cycle?: string | null

    custom_cycle_type?: string | null

    status?: string | number | null

    prices: Api.PlanCreatePriceReq[]

    tiered_type: number | null

    updated_at?: string | null

    customer_id?: string | null

    last_payment_date?: string | null

    units?: number | null

    // invoice 模版 id
    invoice_theme_id?: string | null

    // invoice 账户代码
    invoice_account_code?: string | null

    surcharge_rate: {
      /**
       * 类型，1-百分比 2-固定值
       */
      fee_rate: string
      /**
       * 费率
       */
      fee_value: string
    }
  }
}
