import { GET } from '@/services/http'

export const getSumData = () => GET('/cardinia/home/<USER>')

export const getMonthPayment = (time: string) => GET('/cardinia/home/<USER>', { params: { time } })
export const getCollectedPayment = (params: { days?: string, date?: string }) => GET('/home/<USER>', { params })

// export const getPropertyDetail = (params: { id: number }) => GET<Api.FlexiratesGetPropertyDetailRes>('/property/detail', { params })

export const getBusinessSummary = (start_date?: string | null, end_date?: string | null) => GET<CommonRes<Api.HomeSummaryData>>('/home/<USER>', { params: { start_date, end_date } })

export const getChartData = (params: { date: string }) => GET<Api.HomeChartData>('/cardinia/home/<USER>', { params })
