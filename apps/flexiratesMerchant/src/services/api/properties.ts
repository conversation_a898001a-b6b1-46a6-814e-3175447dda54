import { GET, POST } from '@/services/http'

export const getList = (params: Api.PropertiesListReq) => GET<Api.PropertiesListRes>('/cardinia/properties/list', { params })

export const getArrearList = (params: Api.PropertiesListReq) => GET<Api.PropertiesListRes>('/cardinia/properties/arrearList', { params })

export const exportProperties = (params: Api.PropertiesListReq) => GET<Api.PropertiesListRes>('/cardinia/properties/export', { params })

export const getDetail = (params: Api.PropertiesDetailReq) => GET<Properties.DetailInfo>('/cardinia/properties/detail', { params })

export const getScheduleDetail = (params: Api.PropertiesEditScheduleDetailReq) => GET<Api.PropertiesEditScheduleDetailRes>('/cardinia/schedule/getPropertyDetail', { params })

export const editScheduleDetail = (data: Api.PropertiesEditScheduleReq) => POST<CommonRes<null>>('/cardinia/properties/updateSchedule', data)
