import { GET, POST } from '@/services/http'

export const getList = (params: Api.PlanListReq) => GET<CommonRes<Plan.Info[]>>('/plan/list', { params })
export const create = (data: Api.PlanCreateReq) => POST<CommonRes<Plan.Info>>('/plan/create', data)
export const remove = (plan_ids: string[]) => POST<CommonRes>('/plan/delete', { plan_ids })
export const getDetail = (plan_id: string) => GET<Plan.Info>('/plan/detail', { params: { plan_id } })
export const edit = (data: Api.PlanEditReq) => POST<CommonRes>('/plan/update', data)
export const exportPlans = (params: Api.PlanListReq) => GET<CommonRes>('/plan/export', { params })
export const createByCustomer = (data: Api.PlanCreateByCustomerReq) => POST<{ customer_id: string }>('/subscription/create', data)
export const updatePayMethod = (data: Api.PlanUpdatePayMethodReq) => POST<CommonRes>('/subscription/sendModPaymentMethodMail', data)
export const updateCustomerPlanInfo = (data: Api.UpdateCustomerPlanInfoReq) => POST<CommonRes>('/subscription/update', data)
