import { GET, POST } from '../http'

// Get merchant role list with pagination and filtering
export const getList = (params: Api.UserRoleListReq) => GET<CommonListRes<UserRole.Info[]>>('/cardinia/role/list', { params })

// Create new merchant role
export const create = (data: UserRole.CreateReq) => POST<UserRole.Info>('/cardinia/role/create', data)

// Update merchant role
export const update = (id: string, data: UserRole.UpdateReq) => POST<UserRole.Info>(`/cardinia/role/update`, { ...data, role_id: id })

// Delete merchant role
export const remove = (id: string) => POST<void>(`/cardinia/role/delete`, { role_id: id })

// Get merchant role detail
export const detail = (id: string) => GET<UserRole.Info>(`/cardinia/role/detail`, { params: { role_id: id } })

// Get merchant role permission list
export const getPermissionList = () => GET<UserRole.PermissionListRes[]>('/cardinia/permission/list')
