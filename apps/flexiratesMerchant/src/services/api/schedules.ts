import { GET, POST } from '@/services/http'

export const getList = (params: Api.ScheduleListReq) => GET<Api.ScheduleListRes>('/cardinia/schedule/list', { params })

export const exportSchedules = (params: Api.ScheduleListReq) => GET<Api.ScheduleListRes>('/cardinia/schedule/export', { params })

export const getDetail = (id: number) => GET<Api.ScheduleDetailRes>('/cardinia/schedule/detail', { params: { id } })

export const editSchedule = (data: Api.ScheduleEditReq) => POST<CommonRes<null>>('/cardinia/properties/updateSchedule', data)

export const editScheduleCount = (data: Api.PropertiesEditCountScheduleReq) => POST<CommonRes<null>>('/cardinia/properties/updateScheduleEditCount', data)

export const exportDetailSchedule = (params: Api.ScheduleListReq) => GET<Api.ScheduleListRes>('/cardinia/schedule/detailExporter', { params })
