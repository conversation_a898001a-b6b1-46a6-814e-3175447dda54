import { GET } from '@/services/http'

export const getList = (params: Api.TransactionListReq) => GET<Api.TransactionListRes>('/cardinia/payments/list', { params })
export const getTransactionDetail = (id: number) => GET<Transaction.Info>('/cardinia/payments/detail', { params: { id } })
export const exportTransactions = (params: Api.TransactionListReq) => GET<Api.TransactionListRes>('/cardinia/payments/export', { params })
