import { GET } from '@/services/http'

export const getOverview = (params: Api.ReportOverviewReq) => GET<Report.Overview>('/cardinia/report/overview', { params })
export const getMonthly = (params: Api.ReportMonthlyReq) => GET<Report.Monthly>('/cardinia/report/monthly', { params })
export const getAnnual = (params: Api.ReportAnnualReq) => GET<Report.Annual>('/cardinia/report/annual', { params })
export const getRegistrations = () => GET<Api.ReportRegistrationsRes>('/cardinia/report/pie')
export const downloadReport = () => GET<Api.ReportRegistrationsRes>('/cardinia/report/overviewExport')
