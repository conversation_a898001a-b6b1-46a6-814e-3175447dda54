import { GET, POST } from '@/services/http'

export const getNotifyList = (params: CommonSearchListParams) => GET('/cardinia/notify/list', { params })

export const deleteNotify = (data: Api.FlexiratesNotifyDeleteReq) => POST('/cardinia/notify/delete', data)

export const updateNotify = (data: Api.FlexiratesNotifyDeleteReq) => POST('/cardinia/notify/updateRead', data)

export const getUnreadCount = () => GET('/cardinia/notify/unreadCount')
