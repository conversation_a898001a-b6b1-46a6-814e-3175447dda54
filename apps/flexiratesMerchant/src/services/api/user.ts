import { GET, POST } from '@/services/http'

export const login = (data: Api.UserLoginReq) => POST<Api.UserLoginRes>('/userLogin', data)
export const register = (data: Api.UserRegisterReq) => POST<Api.UserRegisterRes>('/register', data)
export const forgotPassword = (data: Api.UserForgotPasswordReq) => POST<Api.UserForgotPasswordRes>('/forgetPassword', data)

export const setNewPassword = (data: Api.UserSetNewPasswordRequest) => POST<CommonRes>('/forgetPasswordSubmit', data)
export const getUserInfo = () => GET<User.Info>('/getUserInfo')
export const logout = () => GET('/userLoginOut')
export const getMerchantInfo = () => GET('/userLoginOut')

// 2FA Authentication APIs
export const get2FASetupInfo = () => GET<Api.UserTwoFactorSetupRes>('/getValidatePage')
export const verify2FACode = (data: Api.UserTwoFactorVerifyReq) => POST<Api.UserTwoFactorVerifyRes>('/validateMfa', data)

// Get user list with pagination and filtering
export const getUserList = (params: Api.UserListReq) => GET<Api.UserListRes>('/cardinia/user/list', { params })

// Get user detail by ID
export const getUserDetail = (id: string) => GET<User.UserInfo>(`/cardinia/user/detail`, { params: { user_id: id } })

export const editUpdateUser = (id: string, data: User.UserUpdateReq) => POST<User.UserInfo>(`/cardinia/user/update`, { ...data, user_id: id })

// Create new user
export const createUser = (data: User.UserCreateReq) => POST<User.UserInfo>('/cardinia/user/create', data)

// Delete user
export const removeUser = (id: string) => POST<void>(`/cardinia/user/delete`, { user_id: id })

// Update user
export const updateUserInfo = (data: User.UserInfoUpdateReq) => POST<User.UserInfo>(`/userUpdate`, data)

// Update User Info Password
export const updateUserPassword = (data: User.UserPasswordUpdateReq) => POST<User.UserInfo>(`/userUpdatePassword`, data)

export const getUserMenu = () => GET<Api.RouterItem[]>('/cardinia/getMenu')
