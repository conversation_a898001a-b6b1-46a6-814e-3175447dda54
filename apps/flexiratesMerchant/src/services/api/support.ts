import { GET, POST } from '@/services/http'

export const deleteNotify = (data: { property_id: number }) => POST('/cardinia/notify/closePlan', data)

export const getNotify = () => GET<any>('/cardinia/support/detail')

export const getAllProperty = () => GET<FlexiratesTransaction.PropertyInfo[]>('/cardinia/support/getAllProperty')

export const getAllCustomer = () => GET<{ customer_id: string, name: string }[]>('/cardinia/support/getAllCustomer')

export const getCancelList = (params: Api.CardiniaCancelListReq) => GET<Api.CardiniaCancelListRes>('/cardinia/property/list', { params })

export const cancelRequest = (data: { property_id: number }) => POST('/cardinia/property/cancelRequest', data)

export const audit = (data: Api.CardiniaCancelAuditReq) => POST('/cardinia/property/check', data)
