import { GET, POST } from '@/services/http'

export const getList = (params: Api.RatepayerListReq) => GET<Api.RatepayerListRes>('/cardinia/ratePayer/list', { params })

export const getDetail = (params: Api.RatepayerDetailReq) => GET<Ratepayer.DetailInfo>('/cardinia/ratePayer/detail', { params })

export const getSchedule = (params: Api.RatepayerScheduleReq) => GET<Ratepayer.Schedule>('/cardinia/property/baseInfo', { params })

export const exportRatepayers = (params: Api.RatepayerListReq) => GET<Api.RatepayerListRes>('/cardinia/ratePayer/export', { params })

export const createRatepayer = (data: Api.RatepayerCreateReq) => POST<Api.RatepayerCreateRes>('/cardinia/ratePayer/create', data)

export const updateRatepayer = (customer_id: string, data: Api.RatepayerUpdateReq) => POST<Ratepayer.Info>('/cardinia/ratePayer/update', { ...data, customer_id })

export const getRatepayerNotes = (params: Api.RatepayerNotesReq) => GET<CommonListRes<Ratepayer.Note>>('/cardinia/ratePayer/noteList', { params })

export const createRatepayerNote = (data: Api.RatepayerNoteCreateReq) => POST<Api.RatepayerNoteCreateRes>('/cardinia/ratePayer/addNote', data)

export const getActivityLog = (params?: Api.CustomerActivityLogListReq) => GET<Api.CustomerActivityLogListRes>('/cardinia/activityLog/list', { params })

export const exportActivityLog = (params: Api.CustomerActivityLogListReq) => GET('/cardinia/activityLog/export', { params })
