import { definePreset, palette } from '@primeuix/themes'
import Aura from '@primeuix/themes/aura'

const primaryColors: Record<number, string> = palette('#031F73')

const infoColors: Record<number, string> = palette('#0073CF')

const warnColors: Record<number, string> = palette('#EBB700')

const successColors: Record<number, string> = palette('#00C49F')

const Noir = definePreset(Aura, {
  semantic: {
    primary: {
      ...primaryColors,
    },
    sky: {
      ...infoColors,
    },
    orange: {
      ...warnColors,
    },
    green: {
      ...successColors,
    },
  },
})

export default Noir
