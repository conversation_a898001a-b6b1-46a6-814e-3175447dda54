<script setup lang="ts">
import collapsePanel from '../collapsePanel.vue'
import { ref } from 'vue'

const emits = defineEmits(['accept'])
const visible = ref(false)
// const functionalCookies = ref(true)
// const mediaCookies = ref(true)
// const performanceCookies = ref(true)
// const targetingCookies = ref(false)
const closePop = () => {
  emits('accept')
}
</script>

<template>
  <div>
    <div class="cookies-pop w-xl">
      <p>
        By clicking “Accept All Cookies”, you agree to the storing of
        cookies on your device to enhance site navigation, analyze site
        usage, and assist in our marketing efforts.
      </p>
      <div class="flex justify-between items-center mt-4">
        <div>
          <button class="btn cursor-pointer btn-setting" @click="visible = true">
            COOKIE SETTINGS
          </button>
        </div>
        <div>
          <button class="btn btn-operation cursor-pointer" @click="closePop">
            REJECT ALL
          </button>
          <button class="btn btn-operation cursor-pointer" @click="closePop">
            ACCEPT ALL COOKIES
          </button>
        </div>
      </div>
    </div>
    <div class="settings">
      <Dialog v-model:visible="visible" :modal="true" style="border:none;" pt:mask:class="backdrop-blur-sm">
        <template #container="{ closeCallback }">
          <div class="w-[700px]  p-10 bg-[#ffe3e8] text-[#181349] rounded-2xl">
            <div class="header flex justify-between items-center border-b-2 border-[#acb8c0] pb-4 ">
              <div class="font-semibold text-[2rem]">
                Privacy Preference Center
              </div>
              <i
                class="pi pi-times" style="color: #fe4c1c;font-weight: 700;font-size: 1.5rem;"
                @click="closeCallback"
              />
            </div>
            <ScrollPanel style="width: 100%; height: 600px">
              <div class="description mt-4 mb-8">
                <p class="leading-8">
                  Websites store information on your browser, mainly through
                  cookies, to enhance your experience. While this data doesn’t
                  usually identify you, it personalizes your web experience. You
                  can manage your cookie preferences, but disabling some may
                  affect site functionality.
                </p>
                <div class="mt-8 italic underline cursor-pointer">
                  More information
                </div>
              </div>
              <div class="choice">
                <div class="choice-title mb-6 font-bold text-[28px]">
                  Customize Your Choice
                </div>
                <div class="bg-[#f5f5ff] border border-[#fe4c1c]">
                  <!-- <collapsePanel title="Functional Cookies">
                    <template #header-right>
                      <ToggleSwitch v-model="functionalCookies" class="custom-switch" />
                    </template>
                    <p class=" mb-4">
                      These cookies enable the website to provide enhanced
                      functionality and personalisation. They may be set by us or by
                      third party providers whose services we have added to our
                      pages. If you do not allow these cookies then some or all of
                      these services may not function properly.
                    </p>
                  </collapsePanel>
                  <collapsePanel title="Social Media Cookies">
                    <template #header-right>
                      <ToggleSwitch v-model="mediaCookies" class="custom-switch" />
                    </template>
                  </collapsePanel>
                  <collapsePanel title="Performance Cookies">
                    <template #header-right>
                      <ToggleSwitch v-model="performanceCookies" class="custom-switch" />
                    </template>
                  </collapsePanel>
                  <collapsePanel title="Targeting Cookies">
                    <template #header-right>
                      <ToggleSwitch v-model="targetingCookies" class="custom-switch" />
                    </template>
                  </collapsePanel> -->
                  <collapsePanel title="Strictly Necessary Cookies">
                    <template #header-right>
                      <span class="text-[#fe4c1c] font-semibold">Always Active</span>
                    </template>
                  </collapsePanel>
                </div>
              </div>
              <div class="flex justify-between items-center mt-8">
                <div class="flex flex-col leading-6">
                  <div class="italic underline">
                    Cookies Notice<i class="pi pi-arrow-up-right" />
                  </div>
                  <div class="italic underline">
                    Cookies List<i class="pi pi-arrow-up-right" />
                  </div>
                </div>
                <div class="">
                  <button class="btn btn-secondary mt-2 cursor-pointer" @click="() => { visible = false; closePop() }">
                    REJECT ALL
                  </button>
                  <button class="btn btn-secondary mt-2 cursor-pointer" @click="() => { visible = false; closePop() }">
                    ACCEPT ALL COOKIES
                  </button>
                </div>
              </div>
            </ScrollPanel>
          </div>
        </template>
      </Dialog>
    </div>
  </div>
</template>

<style scoped>
.cookies-pop {
  position: fixed;
  bottom: 60px;
  left: 50%;
  transform: translate(-50%);
  z-index: 3;
  background-color: #181349;
  border-radius: 16px;
  padding: 1.5rem 1.8rem;
  color: #fff;
}

.btn {
  color: #fff;
  font-weight: 600;
  font-size: 14px;
  padding: 8px;
  background-color: #b2b2b2;
}

.btn-setting:hover {
  background-color: #747474;
}

.btn-operation {
  background-color: #fe4c1c;

}

.btn-operation:not(:last-child) {
  margin-right: 10px;
}

.btn-operation:hover {
  background-color: #c13a14;
}

.settings {
  color: #181349;
}

.custom-switch {
  --p-toggleswitch-checked-background: #7ed957;
  --p-toggleswitch-checked-hover-background: #7ed957;
}

.btn-secondary {
  background-color: #fe4c1c;

}

.btn-secondary:hover {
  background-color: #c13a14;
}

.btn-secondary:not(:last-child) {
  margin-right: 10px;
}
</style>
