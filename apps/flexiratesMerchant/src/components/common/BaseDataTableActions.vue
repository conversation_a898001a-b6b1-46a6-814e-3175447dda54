<script setup lang="ts">
const props = withDefaults(defineProps<{
  disabled?: boolean
  contentWidth?: string
  popperClass?: string
  offset?: number
  showArrow?: boolean
  loading?: boolean
  isShowEdit?: boolean
  isShowDelete?: boolean
  isShowDetail?: boolean
}>(), {
  disabled: false,
  contentWidth: 'auto',
  popperClass: '',
  offset: 12,
  showArrow: true,
  loading: false,
  isShowEdit: true,
  isShowDelete: true,
  isShowDetail: true,
})

const emits = defineEmits<{
  (e: 'edit'): void
  (e: 'delete'): void
  (e: 'detail'): void
}>()
</script>

<template>
  <BasePopover
    :disabled="props.disabled"
    trigger="hover"
    :offset="props.offset"
    :show-arrow="props.showArrow"
    :width="props.contentWidth"
    :popper-class="props.popperClass"
    placement="left"
  >
    <template #reference>
      <Button class="base-data-table-actions" severity="secondary" icon="pi pi-ellipsis-h" :loading="props.loading" />
    </template>

    <slot>
      <div class="flex gap-2">
        <Button v-if="props.isShowDetail" severity="secondary" @click="emits('detail')">
          Details
        </Button>
        <Button v-if="props.isShowEdit" severity="secondary" @click="emits('edit')">
          Edit
        </Button>
        <Button v-if="props.isShowDelete" severity="secondary" @click="emits('delete')">
          Delete
        </Button>
      </div>
    </slot>
  </BasePopover>
</template>

<style lang="scss">
.base-data-table-actions {
  --p-button-secondary-border-color: transparent;
  --p-button-secondary-background: transparent;
}
</style>
