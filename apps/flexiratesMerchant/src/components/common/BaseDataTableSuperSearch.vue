<script setup lang="ts">
import type { PropType, Ref } from 'vue'

const emits = defineEmits(['update:value'])

const searchField: Ref<TableSearchFieldItem> = defineModel('searchField', {
  type: Object as PropType<TableSearchFieldItem>,
  default() {
    return {}
  },
})

const handleStatusChange = (value: string | number) => {
  emits('update:value', value)
}
</script>

<template>
  <div
    v-if="
      searchField?.componentProps && Object.keys(searchField?.componentProps).length
        && searchField?.componentProps?.options?.length
    "
    class="base-data-table-super-search flex gap-4 items-center"
  >
    <div
      class="base-data-table-super-search-item top-search flex-1 border-1 border-gray-100 p-5 rounded-2xl cursor-pointer"
      :class="{
        active: searchField?.value === '',
      }"
      @click="handleStatusChange('')"
    >
      All
    </div>
    <div
      v-for="dict in searchField?.componentProps?.options" :key="dict.value"
      class="base-data-table-super-search-item top-search flex-1 border-1 border-gray-100 p-5 rounded-2xl cursor-pointer"
      :class="{
        active: searchField?.value === dict.value,
      }"
      @click="handleStatusChange(dict.value)"
    >
      {{ dict.label }}
    </div>
  </div>
</template>

<style lang="scss">
.base-data-table-super-search {
  display: flex;
  margin-bottom: 0.75rem;
  .base-data-table-super-search-item {
    &.active {
      border-color: var(--p-primary-color);
    }
  }
}
</style>
