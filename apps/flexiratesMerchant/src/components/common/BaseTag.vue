<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'BaseTag',
  props: {
    text: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: 'default',
      validator: (value: string) => ['default', 'paid', 'upcoming', 'failed', 'info'].includes(value),
    },
    clickable: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['click'],
  setup(props, { emit }) {
    const handleClick = () => {
      if (props.clickable) {
        emit('click')
      }
    }

    return {
      handleClick,
    }
  },
})
</script>

<template>
  <div
    class="base-tag"
    :class="[
      `base-tag--${type}`,
      { 'base-tag--clickable': clickable },
    ]"
    @click="handleClick"
  >
    <slot>{{ text }}</slot>
  </div>
</template>

<style scoped>
.base-tag {
  display: inline-flex;
  align-items: center;
  border-radius: 6px;
  padding: 4px 0;
  font-size: 14px;
  font-weight: 500;
  min-width: 80px;
  white-space: nowrap;
  transition: all 0.2s ease;
  font-weight: 600;
  height: 100%;
}

.base-tag--clickable {
  cursor: pointer;
}

.base-tag--clickable:hover {
  opacity: 0.8;
}

/* 标签类型样式 */
.base-tag--default {
  color: #666666;

}

.base-tag--paid {
  color: #39B54A;
}

.base-tag--upcoming {
  color: #FF5F00;
}

.base-tag--failed {
  color: #EB001B;
}

.base-tag--info {
  color: #0073CF;
}
</style>
