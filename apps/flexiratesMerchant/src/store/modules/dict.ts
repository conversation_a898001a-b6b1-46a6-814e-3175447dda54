import type { DictData, DictItem } from '@/services/api/dict'
import { dict } from '@/services/api'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const useDictStore = defineStore('dict', () => {
  // state
  const dictCache = ref<DictData>({})
  const loadingTypes = ref<Record<string, boolean>>({})

  // actions
  /**
   * Get dictionary data by type
   * @param type Dictionary type
   * @returns Promise with dictionary items
   */
  const getDictByType = async (type: string): Promise<DictItem[]> => {
    // Return from cache if available
    if (dictCache.value[type]) {
      return dictCache.value[type]
    }

    loadingTypes.value[type] = true
    try {
      const response = await dict.getDictByType(type)

      if (response.data) {
        // Update cache
        dictCache.value[type] = response.data
        return response.data
      }
      return []
    }
    catch (error) {
      console.error(`Failed to fetch dictionary data for type: ${type}`, error)
      return []
    }
    finally {
      loadingTypes.value[type] = false
    }
  }

  /**
   * Clear dictionary cache
   * @param type Optional dictionary type to clear. If not provided, clears all cache.
   */
  const clearDictCache = (type?: string) => {
    if (type) {
      const newCache = { ...dictCache.value }
      delete newCache[type]
      dictCache.value = newCache
    }
    else {
      dictCache.value = {}
    }
  }

  // getters
  const isTypeLoading = (type: string) => computed(() => !!loadingTypes.value[type])
  const getDictLabel = (type: string, value: string | number): string => {
    if (!dictCache.value[type]) { return '' }
    const item = dictCache.value[type].find(item => item.value === value)
    return item ? item.label : ''
  }

  return {
    // state
    dictCache,
    loadingTypes,

    // actions
    getDictByType,
    clearDictCache,

    // getters
    isTypeLoading,
    getDictLabel,
  }
}, {
  persist: false,
})
