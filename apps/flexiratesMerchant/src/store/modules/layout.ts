import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useLayoutStore = defineStore('layout', () => {
  // state
  const isSidebarVisible = ref(true)
  const isSidebarSlim = ref(false)
  const isDarkTheme = ref(false)

  // actions
  const setSidebarMode = (mode: 'slim' | 'expanded') => {
    isSidebarSlim.value = mode === 'slim'
  }

  const toggleSidebarVisible = () => {
    isSidebarVisible.value = !isSidebarVisible.value
  }

  const toggleDarkTheme = () => {
    isDarkTheme.value = !isDarkTheme.value
  }

  return {
    // state
    isSidebarVisible,
    isSidebarSlim,
    isDarkTheme,

    // actions
    setSidebarMode,
    toggleSidebarVisible,
    toggleDarkTheme,
  }
}, {
  persist: true,
})
