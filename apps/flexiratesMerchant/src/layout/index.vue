<script setup lang="ts">
import { useScrollbarDetection } from '@/composables/useScrollbarDetection'
import { useAppStore } from '@/store/modules/app'
import { useLayoutStore } from '@/store/modules/layout'
import { storeToRefs } from 'pinia'
import ProgressSpinner from 'primevue/progressspinner'
import { onMounted, onUnmounted, ref } from 'vue'
import appHeader from './components/appHeader.vue'
import appSidebar from './components/appSidebar.vue'

const contentRef = ref<HTMLElement | null>(null)
const appStore = useAppStore()
const layoutStore = useLayoutStore()
const { isSidebarVisible, isSidebarSlim } = storeToRefs(layoutStore)

const isMobile = ref(false)

// 检测窗口大小变化，判断是否是移动设备
const checkMobileDevice = () => {
  isMobile.value = window.innerWidth < 992
}

// 挂载时添加窗口大小变化事件监听
onMounted(() => {
  checkMobileDevice()
  window.addEventListener('resize', checkMobileDevice)
})

// 卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', checkMobileDevice)
})

const { hasVerticalScrollbar } = useScrollbarDetection(contentRef)

document.title = import.meta.env.VITE_APP_NAME
</script>

<template>
  <div
    class="layout-flexirates-merchant-wrapper"
    :class="{
      'sidebar-hidden': !isSidebarVisible,
      'sidebar-slim': isSidebarSlim,
      'mobile-layout': isMobile,
    }"
  >
    <div class="layout-flexirates-merchant-sidebar">
      <app-sidebar />
    </div>
    <div class="layout-flexirates-merchant-header" :class="{ 'sidebar-hidden': !isSidebarVisible, 'sidebar-slim': isSidebarSlim }">
      <app-header />
    </div>
    <div class="layout-flexirates-merchant-content-wrapper" :class="{ 'has-vertical-scrollbar': hasVerticalScrollbar }">
      <div ref="contentRef" class="layout-flexirates-merchant-content">
        <router-view v-slot="{ Component, route }">
          <template v-if="Component">
            <transition name="fade" mode="out-in">
              <keep-alive :include="appStore.keepAliveComponents" :max="10">
                <Suspense>
                  <component :is="Component" :key="route.fullPath" />
                  <template #fallback>
                    <div class="loading-container">
                      <ProgressSpinner style="width: 50px; height: 50px;" stroke-width="4" fill="var(--surface-ground)" animation-duration=".5s" />
                      <p>Loading...</p>
                    </div>
                  </template>
                </Suspense>
              </keep-alive>
            </transition>
          </template>
        </router-view>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  padding: 2rem;

  p {
    margin-top: 1rem;
    color: var(--text-color-secondary);
    font-size: 1rem;
  }
}
</style>
