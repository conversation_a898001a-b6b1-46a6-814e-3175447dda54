<script setup lang="ts">
import type { PropType } from 'vue'
import billbuddyLogo from '@/assets/flexiratesMerchant/logo.png'
import { useAppStore } from '@/store/modules/app'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import AppMenuItem from './appMenuItem.vue'

defineProps({
  items: {
    type: Array as PropType<Menu.Item[]>,
    required: true,
    default: () => [],
  },
})

const router = useRouter()
const appStore = useAppStore()

const { isMobileMenuVisible } = storeToRefs(appStore)

const closeMobileMenu = (menu: Menu.Item) => {
  if (menu.children && menu.children.length === 0 || menu.children === undefined || menu.children === null) {
    appStore.closeMobileMenu()
  }
}
</script>

<template>
  <Drawer v-model:visible="isMobileMenuVisible" position="left" class="flexirates-merchant-mobile-menu-drawer" :modal="true" :dismissable="true" :show-close-icon="false">
    <div class="mobile-menu-header">
      <Image :src="billbuddyLogo" alt="Logo" width="160" @click="router.push('/flexiratesMerchant'); appStore.closeMobileMenu()" />
    </div>

    <div class="mobile-menu-content">
      <template v-for="(item, index) in items" :key="index">
        <AppMenuItem v-if="!item?.meta?.isSeparator" :item="item" :slim="false" @click="closeMobileMenu(item)" />
        <div v-else class="menu-separator" />
      </template>
    </div>
  </Drawer>
</template>

<style lang="scss" scoped>
.mobile-menu-content {
  padding: 0.25rem;
}

.menu-separator {
  height: 1px;
  background-color: var(--surface-border);
  margin: 0.5rem 0;
}
</style>
