<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import userDefaultImage from '@/assets/merchant/account-icon.png'
import noticeImage from '@/assets/merchant/notification-bell.png'
import { publicRouterName } from '@/router/publicRouterName'
// import { notifications } from '@/services/api'
import { useAppStore } from '@/store/modules/app'
import { useLayoutStore } from '@/store/modules/layout'
import { useNotificationStore } from '@/store/modules/notifications'
import { useUserStore } from '@/store/modules/user'
import Breadcrumbs from './appBreadcrumbs.vue'

const router = useRouter()
const { t } = useI18n()
const userSettingMenu = ref()
const userStore = useUserStore()
const appStore = useAppStore()

const { locale } = storeToRefs(appStore)

watch(locale, (newValue) => {
  appStore.setLocale(newValue as 'en' | 'zh')
})

const avatarUrl = computed(() => {
  return userStore.user?.avatar || userDefaultImage
})

const notificationStore = useNotificationStore()
const { unreadCount } = storeToRefs(notificationStore)
const hasUnreadNotice = computed(() => unreadCount.value > 0)

onMounted(() => {
  notificationStore.startPolling()
})
onUnmounted(() => {
  notificationStore.stopPolling()
})

const layoutStore = useLayoutStore()
const { isSidebarSlim } = storeToRefs(layoutStore)

const userSettingItems = computed(() => {
  return [
    {
      label: t('user.profile'),
      icon: 'pi pi-user',
      command: () => router.push('/user/profile'),
    },
    {
      label: t('user.settings.title'),
      icon: 'pi pi-cog',
      command: () => router.push('/user/settings'),
    },
    { separator: true },
    {
      label: t('user.logout'),
      icon: 'pi pi-power-off',
      command: async () => {
        await userStore.logout()
        router.replace({ name: publicRouterName.LOGIN })
      },
    },
  ]
})

const toggleSidebar = () => {
  layoutStore.setSidebarMode(isSidebarSlim.value ? 'expanded' : 'slim')
}

const toggleMobileMenu = () => {
  appStore.toggleMobileMenu()
}
</script>

<template>
  <div class="app-header">
    <div class="header-start">
      <Button class="mobile-menu-toggle" severity="secondary" @click="toggleMobileMenu">
        <i class="pi pi-bars" />
      </Button>
      <Button class="sidebar-toggle" severity="secondary" @click="toggleSidebar">
        <i class="pi" :class="isSidebarSlim ? 'pi-angle-right' : 'pi-angle-left'" />
      </Button>
      <Divider layout="vertical" />
      <Breadcrumbs />
    </div>

    <div class="header-end">
      <!-- <div class="user-notice">
        <img class="notice-image" :src="noticeImage" alt="notice">
      </div> -->
      <div class="user-notice">
        <OverlayBadge v-if="hasUnreadNotice" :value="unreadCount > 99 ? '99+' : unreadCount">
          <img class="notice-image" :src="noticeImage" alt="notice" @click="$router.push({ name: 'notifications' })">
        </OverlayBadge>
        <img v-else class="notice-image" :src="noticeImage" alt="notice" @click="$router.push({ name: 'notifications' })">
      </div>
      <Menu ref="userSettingMenu" :model="userSettingItems" :popup="true" />
      <div class="user-profile" @click="userSettingMenu.toggle($event)">
        <Avatar
          :image="avatarUrl"
          shape="circle"
          class="user-avatar"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.mobile-menu-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
