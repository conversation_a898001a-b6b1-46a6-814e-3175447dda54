import type { App } from 'vue'
import nProgress from 'nprogress'
import PrimeVue from 'primevue/config'
import ConfirmationService from 'primevue/confirmationservice'
import DialogService from 'primevue/dialogservice'
import KeyFilter from 'primevue/keyfilter'
import ToastService from 'primevue/toastservice'
import { configure } from 'vee-validate'
import { setupPermissionDirectives } from './directives/permission'
import i18n from './i18n'
import Noir from './presets/Noir'
import router from './router'
import { routerGuardsSetup } from './router/guards'
import store from './store'
import { createCopyDirective } from './utils/clipboard'
import 'nprogress/nprogress.css'
import 'normalize.css'
import './styles/tailwindcss.css'
import './styles/style.scss'
import 'primeicons/primeicons.css'

export function bootstrap(app: App<Element>) {
  nProgress.configure({ easing: 'ease', speed: 300, showSpinner: true })

  // Configure vee-validate
  configure({
    validateOnBlur: true, // controls if `blur` events should trigger validation with `handleChange` handler
    validateOnChange: true, // controls if `change` events should trigger validation with `handleChange` handler
    validateOnInput: false, // controls if `input` events should trigger validation with `handleChange` handler
    validateOnModelUpdate: true, // controls if `update:modelValue` events should trigger validation with `handleChange` handler
  })

  // Configure store
  app.use(store)

  // Configure i18n
  app.use(i18n)

  // Configure PrimeVue
  app.use(PrimeVue, {
    theme: {
      preset: Noir,
    },
  })

  // Add Toast Service
  app.use(ToastService)

  // Add Dialog Service
  app.use(DialogService)

  // Add Confirmation Service
  app.use(ConfirmationService)

  // Register global directives
  createCopyDirective(app)
  setupPermissionDirectives(app)

  // Configure router
  app.use(router)

  // app directive
  app.directive('keyfilter', KeyFilter)

  routerGuardsSetup(router)

  return app
}
