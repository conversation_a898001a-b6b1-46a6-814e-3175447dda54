import type { App } from 'vue'
import nProgress from 'nprogress'
import PrimeVue from 'primevue/config'
import ConfirmationService from 'primevue/confirmationservice'
import DialogService from 'primevue/dialogservice'
import KeyFilter from 'primevue/keyfilter'
import ToastService from 'primevue/toastservice'
import { configure } from 'vee-validate'
import { watch } from 'vue'
import i18n from './i18n'
import { darkTheme, whiteTheme } from './presets/Noir'
import router from './router'
import { routerGuardsSetup } from './router/guards'
import store from './store'
import { useLayoutStore } from './store/modules/layout'
import { createCopyDirective } from './utils/clipboard'
import 'nprogress/nprogress.css'
import 'normalize.css'
import './styles/tailwindcss.css'
import './styles/style.scss'
import 'primeicons/primeicons.css'

export function bootstrap(app: App<Element>) {
  nProgress.configure({ easing: 'ease', speed: 300, showSpinner: true })

  // Configure vee-validate
  configure({
    validateOnBlur: true, // controls if `blur` events should trigger validation with `handleChange` handler
    validateOnChange: true, // controls if `change` events should trigger validation with `handleChange` handler
    validateOnInput: false, // controls if `input` events should trigger validation with `handleChange` handler
    validateOnModelUpdate: true, // controls if `update:modelValue` events should trigger validation with `handleChange` handler
  })

  // Configure store
  app.use(store)

  // Configure i18n
  app.use(i18n)

  // Configure PrimeVue with dynamic theme
  const primeVueConfig = {
    theme: {
      preset: whiteTheme,
      options: {
        darkModeSelector: '.dark',
      },
    },
  }

  app.use(PrimeVue, primeVueConfig)

  // Setup theme switching logic
  const layoutStore = useLayoutStore()

  // Initialize theme based on stored preference
  const updateTheme = (isDark: boolean) => {
    const htmlElement = document.documentElement
    if (isDark) {
      htmlElement.classList.add('dark')
      // Update PrimeVue theme to dark
      app.config.globalProperties.$primevue.config.theme.preset = darkTheme
    }
    else {
      htmlElement.classList.remove('dark')
      // Update PrimeVue theme to light
      app.config.globalProperties.$primevue.config.theme.preset = whiteTheme
    }
  }

  // Apply initial theme
  updateTheme(layoutStore.isDarkTheme)

  // Watch for theme changes
  watch(() => layoutStore.isDarkTheme, (newValue) => {
    updateTheme(newValue)
  })

  // Add Toast Service
  app.use(ToastService)

  // Add Dialog Service
  app.use(DialogService)

  // Add Confirmation Service
  app.use(ConfirmationService)

  // Register global directives
  createCopyDirective(app)

  // Configure router
  app.use(router)

  // app directive
  app.directive('keyfilter', KeyFilter)

  routerGuardsSetup(router)

  return app
}
