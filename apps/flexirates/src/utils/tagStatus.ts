import { DownloadStatus } from '@/constants/download'
import { PayoutStatus } from '@/constants/payout'
import { TransactionStatus } from '@/constants/transaction'

export const getTransactionTagStatus = (status: string | number) => {
  return {
    [TransactionStatus.PENDING]: 'upcoming',
    [TransactionStatus.SUCCEEDED]: 'paid',
    [TransactionStatus.FAILED]: 'failed',
    [TransactionStatus.REFUNDED]: 'upcoming',
    [TransactionStatus.DISPUTED]: 'upcoming',
    [TransactionStatus.UNCAPTURED]: 'upcoming',
  }[status] || 'upcoming'
}

export const getDownloadCenterTagStatus = (status: string) => {
  return {
    [DownloadStatus.InProgress]: 'upcoming',
    [DownloadStatus.Succeeded]: 'paid',
    [DownloadStatus.Failed]: 'failed',
    [DownloadStatus.NotStart]: 'upcoming',
  }[status] || 'upcoming'
}

export const getPayoutTagStatus = (status: string | number) => {
  return {
    [PayoutStatus.PENDING]: 'upcoming',
    [PayoutStatus.COMPLETED]: 'paid',
    [PayoutStatus.FAILED]: 'failed',
    [PayoutStatus.PROCESSING]: 'upcoming',
  }[status] || 'upcoming'
}
