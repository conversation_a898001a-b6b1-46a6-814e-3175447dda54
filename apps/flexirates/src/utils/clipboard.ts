import i18n from '@/i18n'

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @param successCallback 复制成功的回调函数
 * @param errorCallback 复制失败的回调函数
 * @returns Promise<boolean> 复制是否成功
 */
export const copyToClipboard = async (
  text: string,
  successCallback?: () => void,
  errorCallback?: (error: unknown) => void,
): Promise<boolean> => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      // 使用现代的 Clipboard API (仅在安全上下文中可用)
      await navigator.clipboard.writeText(text)
    }
    else {
      // 回退方法，使用 document.execCommand
      const textArea = document.createElement('textarea')
      textArea.value = text

      // 防止滚动到底部
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'

      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      const success = document.execCommand('copy')
      document.body.removeChild(textArea)

      if (!success) {
        throw new Error('无法复制文本到剪贴板')
      }
    }

    if (successCallback) {
      successCallback()
    }

    return true
  }
  catch (error) {
    if (errorCallback) {
      errorCallback(error)
    }
    console.error('复制到剪贴板失败:', error)
    return false
  }
}

/**
 * 创建一个可点击复制的指令
 * 使用方法: v-copy="textToCopy"
 */
export const createCopyDirective = (app: any) => {
  app.directive('copy', {
    mounted(el: HTMLElement, binding: any) {
      el.style.cursor = 'pointer'
      el.addEventListener('click', () => {
        const value = binding.value
        if (!value) { return }

        copyToClipboard(
          value,
          () => {
            // 如果全局有toast组件，可以显示复制成功的提示
            if (window.$toast) {
              window.$toast.add({
                severity: 'success',
                summary: i18n.global?.t('common.success') || '成功',
                detail: i18n.global?.t('common.copied') || '已复制到剪贴板',
                life: 3000,
              })
            }
          },
          (_error) => {
            // 处理错误
            if (window.$toast) {
              window.$toast.add({
                severity: 'error',
                summary: i18n.global?.t('common.error') || '错误',
                detail: i18n.global?.t('common.copyFailed') || '复制失败',
                life: 3000,
              })
            }
          },
        )
      })
    },
  })
}

/**
 * 创建一个可复制的组件
 * @param showToast 是否显示提示，默认为true
 * @returns 返回一个函数，调用该函数可以复制文本
 */
export const useCopy = (showToast = true) => {
  return async (text: string): Promise<boolean> => {
    return copyToClipboard(
      text,
      () => {
        if (showToast && window.$toast) {
          window.$toast.add({
            severity: 'success',
            summary: i18n.global?.t('common.success') || '成功',
            detail: i18n.global?.t('common.copied') || '已复制到剪贴板',
            life: 3000,
          })
        }
      },
      (_error) => {
        // 处理错误
        if (showToast && window.$toast) {
          window.$toast.add({
            severity: 'error',
            summary: i18n.global?.t('common.error') || '错误',
            detail: i18n.global?.t('common.copyFailed') || '复制失败',
            life: 3000,
          })
        }
      },
    )
  }
}
