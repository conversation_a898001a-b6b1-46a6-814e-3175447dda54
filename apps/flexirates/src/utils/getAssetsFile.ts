export const getAssetsFiles = (fileName: string): string => {
  const path = `../assets/${fileName}`
  const modules = import.meta.glob('../assets/**/*.{png,jpg,jpeg,webp}', { eager: true, import: 'default' })

  for (const key in modules) {
    if (Object.prototype.hasOwnProperty.call(modules, key)) {
      const element: string = String(modules[key])
      if (path === key) {
        return element
      }
    }
  }

  return '/src/assets/logo.png'
}
