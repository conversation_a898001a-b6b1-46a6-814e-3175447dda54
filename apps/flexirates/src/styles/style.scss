@use './mixins/breakpoints' as *;
@use './common/index.scss' as *;

@use './flexirates/layout.scss' as *;
@use './flexirates/table.scss' as *;
@use './flexirates/style.scss' as *;

.merchant-common-page {
    padding: 24px;
    background-color: var(--color-white);
    border-radius: 16px;

    @include media-breakpoint-down(md) {
        padding: 16px;
    }
}



// 统计卡片样式
.common-stat-card {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;

    &__item {
        flex: 1;
        padding: 1rem;
        background-color: var(--color-white);
        border-radius: 16px;
        padding: 24px;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
            box-shadow: inset 0 0 0 2px var(--color-orange-500);
        }
    }

    &__title {
        color: var(--color-gray-500);
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;
    }

    &__count {
        font-size: 24px;
        font-weight: 800;
    }

}

// 卡掩码
.add-card-mask-wrapper {
    &::before {
        content: '••••';
        padding: 0 8px;
    }
}

.export-dialog {
    --p-dialog-background: rgb(255, 227, 232);

    &.p-dialog {

        .p-dialog-header {
            padding-bottom: 12px;
            border-bottom: 1px solid #e9e9e9;

            .p-dialog-close-button {
                --p-button-text-secondary-color: var(--color-orange-500);
            }
        }

        border: none;

        .p-dialog-content {
            padding-bottom: 34px;
        }
    }

    .export-options {
        border-top: 2px solid rgb(172, 184, 192);
        padding-top: 22px;
    }

    .dialog-cancel-button {
        background-color: rgb(245, 245, 255);
    }

    .export-options h3 {
        font-size: 1.1rem;
        margin-bottom: 1rem;
        color: #333;
    }

    .file-type-options {
        display: flex;
        gap: 1rem;
    }

    .format-option {
        display: flex;
        align-items: center;
    }

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 0.5rem;
    }
}

.flexirates-title {
    margin: .5rem 0 1rem 0;
    padding-bottom: 1.5rem;
    border-bottom: 3px solid var(--color-gray-500);
    display: flex;
    justify-content: space-between;
    align-items: center;

    @include media-breakpoint-down(md) {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    &-text {
        font-size: 2rem;
        font-weight: 800;
    }
}

.flexirates-wrap {
    padding: 1.5rem;
    border-radius: 8px;
    background-color: var(--color-white);
}