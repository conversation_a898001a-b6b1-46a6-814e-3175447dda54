export interface Notification {
  id: string
  title: string
  content: string
  type: number
  created_at: Date
  is_read: number
  type_desc: string
  transaction_status: number
}

export interface NotificationGroup {
  today: Notification[]
  yesterday: Notification[]
  lastWeek: Notification[]
}

export type NotificationType = 'info' | 'payment' | 'error' | 'system'

export interface NotificationColor {
  [key: string]: string
}
