import type { ComputedRef } from 'vue'
import type { SearchFieldType } from '@/constants/search'
/**
 * 搜索字段接口定义
 */
export interface SearchField {
  /** 字段名称（用于表单提交） */
  name: string

  /** 字段标签（显示在界面上） */
  label: string

  /** 字段类型 */
  type: SearchFieldType

  /** 占位符文本 */
  placeholder?: string

  /** 选择器选项列表 */
  options?: Array<{ label: string, value: any }>

  /** 加载状态 */
  loading?: boolean | ComputedRef<boolean>

  /** 最大输入长度 */
  maxlength?: number

  /** 默认值 */
  defaultValue?: any

  /** 自定义宽度 */
  width?: string

  /** 验证规则 */
  rules?: Record<string, any>

  /** 数值最小值 */
  min?: number

  /** 数值最大值 */
  max?: number

  /** 数值步长 */
  step?: number

  /** 日期格式 */
  dateFormat?: string

  /** 多选模式展示label数量 */
  maxLabels?: number
}

/**
 * 搜索参数对象
 */
export interface SearchParams {
  [key: string]: any
}
