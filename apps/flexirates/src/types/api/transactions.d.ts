declare namespace Api {
  interface Stat {
    count?: number
    status?: number
    status_text?: string
  }

  interface TransactionListRes extends CommonListRes<Transaction.Info[]> {
    current_page?: number
    per_page?: number
    stat?: Stat[]
    total?: number
  }

  interface TransactionListReq extends Transaction.Info, CommonSearchListParams {
    /**
     * 排序字段
     */
    'sort_by'?: string
    /**
     * 排序方向
     */
    'sort_order'?: 'asc' | 'desc'
    /**
     * 交易时间范围，[start, end]，格式：yyyy-MM-dd
     */
    'created_at[]'?: string[]
    /**
     * 交易金额范围，[start, end]，
     */
    'payment_amount[]'?: string[]
    /**
     * 币种，如：AUD
     */
    'payment_currency'?: string | null
    /**
     * 支付方式，对应客户的银行卡，卡号 或 卡名称，支持模糊搜索
     */
    'payment_method'?: string | null
    /**
     * 状态，1：Succeeded 2：Failed 3：Pending 4：Refunded 5：Disputed 6：UnCaptured
     */
    'status'?: number | null
    /**
     * 交易类型，1：Payment 2：Refund 3：Dispute
     */
    'trans_type'?: number | null

    'description'?: string | null

    'credit_brand'?: string | null

    'trans_invoice_number'?: string | null

    'refunded_date[]'?: string[]

    'amount'?: string
  }
}
