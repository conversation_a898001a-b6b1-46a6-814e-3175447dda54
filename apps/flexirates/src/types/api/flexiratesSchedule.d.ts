declare namespace Api {
  interface FlexiratesListItem {
    banking?: FlexiratesProperty.Banking[]
    created_at?: null
    created_uid?: number
    deleted_at?: null
    deleted_uid?: number
    enable?: number
    end_date?: string
    fail_times?: number
    flexi_banking_id?: number
    flexi_property_id?: number
    flexi_user_id?: number
    id?: number
    next_process_date?: string
    plan_id?: string
    plan_name?: string
    process_terms?: number
    process_type?: number
    property?: FlexiratesProperty.Property
    start_date?: string
    status?: number
    updated_at?: null
    updated_uid?: number
    street_address?: string
    related_id?: number
  }
  interface PaymentDetail {
    amount?: string
    id?: number
    payment_method?: string
    plan_id?: string
    property_address?: string
    property_name?: string
    trans_no?: string
    /**
     * 字典：trans_status
     */
    trans_status?: number
  }
  interface FlexiratesGetScheduleReq extends CommonSearchListParams {
    property_id?: string
    status?: number | null
  }
  interface FlexiratesScheduleRes extends CommonListRes {
    data: FlexiratesListItem[]
  }
  interface FlexiratesSchedulesDetailRes extends CommonRes {
    data: FlexiratesListItem
  }

  type FlexiratesScheduleDetailRes = {
    id: number
    due_date: string
    amount: string
    frequency: string
    status_desc: string
    failed_times: number
    failable_times: number
    edited_times: number
    editable_count: number
    credit_brand: number
    credit_brand_desc: string
    account_no: string
  }[]
}
