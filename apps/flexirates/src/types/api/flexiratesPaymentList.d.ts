declare namespace Api {

  interface FlexiratesGetCardListReq extends CommonSearchListParams {
    id: string
  }

  interface PaymentMethodInfo {
    customer_id: string
    id: number
    nickname: string
    payment_methods?: PaymentMethod[]
    postcode: string
    property_number: string
    street_address: string
    suburb: string
  }

  interface PaymentMethod {
    account_no: string
    credit_brand: number
    credit_brand_desc: string
    expiration_month: null | string
    expiration_year: null | string
    id: number
    last_payment_amount: string
    last_payment_date: string
    status_desc: string
    weight: number
    weight_desc: string
  }

  interface FlexiratesGetBankAccountListReq extends CommonSearchListParams {
    customer_id: string
  }

  interface FlexiratesXeroSubmitPaymentReq {
    redirect_uri: string
  }

  interface FlexiratesXeroSubmitPaymentRes {
    url: string
  }

}
