declare namespace Api {
  interface FlexiratesTransactionsReq {
    /**
     * 金额范围，字典 trans_amount_range
     */
    amount_range?: number
    // /**
    //  * 填写了date_range_start，则必填
    //  */
    // date_range_end?: Date
    // /**
    //  * 填写了date_range_end，则必填
    //  */
    // date_range_start?: Date
    created_at?: Date[]
    page: number
    page_size: number
    /**
     * 支付方式id，多个使用英文逗号隔开
     */
    payment_method_ids?: string
    /**
     * 房产id，多个使用英文逗号隔开
     */
    property_ids?: string
    /**
     * 排序字段
     */
    sort_by: string
    /**
     * 排序方式，asc或desc
     */
    sort_order: string
    /**
     * 可选值：{
     * 1: 'Success',
     * 2: 'Failed',
     * 3: 'Pending'
     * }
     */
    status?: number

  }
  interface FlexiratesTransactionsExportReq {
    /**
     * 金额范围，字典 trans_amount_range
     */
    amount_range?: number
    // /**
    //  * 填写了date_range_start，则必填
    //  */
    // date_range_end?: Date
    // /**
    //  * 填写了date_range_end，则必填
    //  */
    // date_range_start?: Date
    created_at?: Date[]
    /**
     * 导出字段
     */
    export_cols: string[]
    /**
     * 导出类型，csv，xls，pdf
     */
    export_type: string
    /**
     * 支付方式id，多个使用英文逗号隔开
     */
    payment_method_ids?: number[]
    /**
     * 房产id，多个使用英文逗号隔开
     */
    property_ids?: number[]
    /**
     * 排序字段
     */
    sort_by?: string
    /**
     * 排序方式，asc或desc
     */
    sort_order?: string
    /**
     * 字典：trans_status
     */
    status?: number
  }
  interface FlexiratesTransactionsRes extends CommonListRes {
    data: FlexiratesTransaction.Info[]
  }
  interface FlexiratesTransactionsExportRes extends CommonRes {
    data: { file_url: string }
  }

  interface FlexiratesTransactionDetailRes extends CommonRes {
    data: FlexiratesTransaction.TransactionDetail
  }
  interface FlexiratesTransactionsSendReceipt {
    /**
     * 二选一必填一个
     */
    is_send_via_email?: boolean
    /**
     * 二选一必填一个
     */
    is_send_via_sms?: boolean
  }
}
