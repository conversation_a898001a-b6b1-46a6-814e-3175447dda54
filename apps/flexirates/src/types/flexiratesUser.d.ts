declare namespace FlexiratesUser {
  interface Info {
    merchant_id: string
    email: string
    first_name: string
    last_name: string
    last_name
    location: {
      address_line_one: string
      address_line_two: string
      city: string
      state: string
      postcode: string
      country: string
      timezone: string
    }
    mobile: string
    name: string

    // 扩展字段 默认值
    avatar: string
    mfa_check: number
  }

  interface Location {
    adderss_line_1?: string
    adderss_line_2?: string
    city?: string
    country?: string
    postcode?: string
    state?: string
    timezone?: string

  }

  interface UserInfoUpdateReq {
    /**
     * MFA开关，(0:关闭,1:开启)
     */
    mfa_check: number
    /**
     * 头像
     */
    avatar: string
    /**
     * 邮箱
     */
    email: string
    /**
     * 账户名称
     */
    user_name?: string
  }

  interface updatePersonalInfoReq {
    email?: string
    /**
     * 选填，last_name有值则必填
     */
    first_name?: string
    /**
     * 选填，first_name有值则必填
     */
    last_name?: string
    location?: Location
    mobile?: string
    /**
     * 选填，password有值则必填
     */
    new_password?: string
    /**
     * 选填，new_password有值则必填
     */
    password?: string
  }
}
