<script setup lang="ts">
import type { PropType } from 'vue'
import AppMenuItem from './appMenuItem.vue'

defineProps({
  slim: {
    type: Boolean,
    default: false,
  },
  items: {
    type: Array as PropType<Menu.Item[]>,
    required: true,
    default: () => [],
  },
})
</script>

<template>
  <div class="app-menu-wrap">
    <nav class="app-flexirates-menu" :class="{ 'menu-slim': slim }">
      <template v-for="(item, index) in items" :key="index">
        <AppMenuItem v-if="!item?.meta?.isSeparator" :item="item" :slim="slim" />
        <div v-else class="menu-separator" />
      </template>
    </nav>
  </div>
</template>
