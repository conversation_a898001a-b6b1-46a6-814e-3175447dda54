<script setup lang="ts">
import type { DataTablePageEvent, DataTableSortEvent } from 'primevue/datatable'
import type { PropType, Ref } from 'vue'
import Checkbox from 'primevue/checkbox'
import Popover from 'primevue/popover'
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import BaseDataTableSearch from '@/components/common/BaseDataTableSearch.vue'
import BaseDataTableSearchItem from '@/components/common/BaseDataTableSearchItem.vue'
import BaseDataTableSuperSearch from '@/components/common/BaseDataTableSuperSearch.vue'

interface Props {
  /**
   * Data to be displayed in the table
   */
  value: any[]
  /**
   * Column definitions
   */
  columns: TableColumnItem[]
  /**
   * Table minimum width style
   */
  minWidth?: string
  /**
   * Number of rows per page
   */
  rows?: number
  /**
   * Data key
   */
  dataKey?: string
  /**
   * Available options for rows per page
   */
  rowsPerPageOptions?: number[]
  /**
   * Whether to show striped rows
   */
  stripedRows?: boolean
  /**
   * Whether to show multiple column
   */
  showMultipleColumn?: boolean
  /**
   * Whether to show edit column
   */
  showEditColumn?: boolean
  /**
   * Edit column header
   */
  editColumnLabel?: string
  /**
   * Export label
   */
  exportLabel?: string
  /**
   * Whether to show paginator
   */
  paginator?: boolean
  /**
   * Edit column header
   */
  editColumnHeader?: string
  /**
   * Table size
   */
  size?: 'small' | 'large' | undefined
  /**
   * Whether to show scrollable
   */
  scrollable?: boolean
  /**
   * Whether the request failed (API returned error)
   */
  failed?: boolean
  /**
   * Error message to display when request failed
   */
  failureMessage?: string
  /**
   * Whether to show search bar
   *  showSearchBar?: boolean
   */
  showSearchBar?: boolean
  /**
   * Whether to show row hover
   */
  rowHover?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  minWidth: '49rem',
  rows: 20,
  rowsPerPageOptions() {
    return [20, 50, 100]
  },
  stripedRows: false,
  dataKey: 'id',
  showMultipleColumn: true,
  showEditColumn: true,
  editColumnLabel() {
    return useI18n().t('actions')
  },
  exportLabel() {
    return useI18n().t('export')
  },
  paginator: true,
  size: undefined,
  scrollable: false,
  showSearchBar: true,
  rowHover: false,
})

// Emit events that might be needed by parent components
const emits = defineEmits<{
  (e: 'sort', value: DataTableSortEvent): void
  (e: 'page', value: DataTablePageEvent): void
  (e: 'changeSearch', value: any): void
  (e: 'export'): void
  (e: 'edit', value: any): void
  (e: 'editRows', value: any[]): void
  (e: 'rowClick', value: any): void
}>()

const searchFields: Ref<TableSearchField> = defineModel('searchFields', {
  type: Object as PropType<TableSearchField>,
  default() {
    return {}
  },
})

const dataTableRef = ref()

const vModelSelection = defineModel('v-model:selection', {
  type: Array as PropType<any[]>,
  default() {
    return []
  },
})

const getSearchValues = () => {
  const values: Record<string, any> = {}
  for (const key in searchFields.value) {
    if (Object.prototype.hasOwnProperty.call(searchFields.value, key)) {
      const value = searchFields.value[key].value
      values[key] = value
    }
  }
  return values
}

const updateSearchItem = (e: any, item: TableSearchFieldItem) => {
  item.value = e
  emits('changeSearch', getSearchValues())
}

const columnVisibility = ref<Record<string, boolean>>({})
const editEditColumnPopover = ref()

// Initialize column visibility
props.columns.forEach((col) => {
  if (columnVisibility.value[col.field] === undefined) {
    columnVisibility.value[col.field] = true
  }
})

// Filtered columns based on visibility
const visibleColumns = computed(() => {
  return props.columns.filter(col => columnVisibility.value[col.field])
})

const toggleColumnVisibility = (field: string) => {
  columnVisibility.value[field] = !columnVisibility.value[field]
}

const isShowSuperSearch = computed(() => {
  let isFind = false
  for (const key of Object.keys(searchFields.value)) {
    if (Object.prototype.hasOwnProperty.call(searchFields.value, key)) {
      const element = searchFields.value[key] as TableSearchFieldItem
      if (element.isSuperSearch) {
        isFind = true
      }
    }
  }
  return isFind
})

const findSuperSearch = (): string | undefined => {
  for (const key of Object.keys(searchFields.value)) {
    if (Object.prototype.hasOwnProperty.call(searchFields.value, key)) {
      const element = searchFields.value[key] as TableSearchFieldItem
      if (element.isSuperSearch) {
        return key
      }
    }
  }
}

defineExpose({
  exportCSV: () => {
    dataTableRef.value.exportCSV()
  },
  getSelectedRowsData: () => {
    return dataTableRef.value.getSelectedRowsData()
  },
})
</script>

<template>
  <DataTable
    ref="dataTableRef" v-model:selection="vModelSelection" :value="value" :striped-rows="props.stripedRows"
    :table-style="{ 'min-width': props.minWidth }" :paginator="props.paginator && value.length > 0" :rows="props.rows"
    :data-key="props.dataKey" :size="props.size" :rows-per-page-options="props.rowsPerPageOptions"
    :scrollable="props.scrollable" class="base-data-table transactions-table" :row-hover="props.rowHover" @sort="emits('sort', $event)"
    @page="emits('page', $event)" @row-click="emits('rowClick', $event)"
  >
    <template v-if="props.showSearchBar" #header>
      <BaseDataTableSuperSearch
        v-if="isShowSuperSearch" :search-field="searchFields[findSuperSearch() as string]"
        @update:value="updateSearchItem($event, searchFields[findSuperSearch() as string])"
      />
      <div class="table-search">
        <BaseDataTableSearch>
          <BaseDataTableSearchItem
            v-for="(item, key) in (searchFields as TableSearchField)" :key="key" v-bind="item"
            :is-hide="!!item?.isHide" @update:value="updateSearchItem($event, item)"
          />
        </BaseDataTableSearch>
        <div class="flex gap-4 justify-end mb-2">
          <Button
            v-if="props.showEditColumn" icon="pi pi-cog" :label="props.editColumnLabel" severity="secondary"
            @click="editEditColumnPopover.toggle($event)"
          />
          <Button icon="pi pi-external-link" :label="props.exportLabel" severity="secondary" @click="$emit('export')" />
          <Popover v-if="props.showEditColumn" ref="editEditColumnPopover" class="p-4">
            <div class="flex flex-col gap-2">
              <div v-for="col in props.columns" :key="col.field" class="flex items-center gap-2">
                <Checkbox
                  :model-value="columnVisibility[col.field]" :binary="true" severity="secondary"
                  @change="toggleColumnVisibility(col.field)"
                />
                <span>{{ col.header }}</span>
              </div>
            </div>
          </Popover>
        </div>
      </div>
    </template>
    <Column v-if="showMultipleColumn" selection-mode="multiple" :exportable="false" />
    <Column v-for="col of visibleColumns" v-bind="col" :key="col.field">
      <template v-if="col.field === 'action' && !col.isCustom" #header>
        <i class="pi pi-cog m-auto !text-xl" />
      </template>
      <template v-else-if="col.field === 'action' && col.isCustom" #header>
        <slot name="actionHeader" />
      </template>
      <template v-if="col.field === 'action'" #body="slotProps">
        <slot :name="col.template" v-bind="slotProps" />
      </template>
      <template v-else-if="col.template" #body="slotProps">
        <slot :name="col.template" v-bind="slotProps" />
      </template>
      <template v-else-if="col?.format" #body="slotProps">
        {{ col?.format && col.format(slotProps.data) }}
      </template>
    </Column>
    <template #empty>
      <div class="empty-state-container">
        <div class="empty-state-card" :class="{ 'error-state': props.failed }">
          <div class="empty-state-icon-container" :class="{ 'error-icon': props.failed }">
            <i :class="props.failed ? 'pi pi-exclamation-triangle' : 'pi pi-inbox'" />
          </div>
          <h3 class="empty-state-title">
            <template v-if="props.failed">
              {{ $t('common.requestFailed') || 'Request Failed' }}
            </template>
            <template v-else>
              {{ $t('common.noDataFound') || 'No data found' }}
            </template>
          </h3>
          <p class="empty-state-message">
            <template v-if="props.failed && props.failureMessage">
              {{ props.failureMessage }}
            </template>
            <template v-else-if="props.failed">
              {{ $t('common.requestFailedDescription') || 'An error occurred while fetching data. Please try again later.' }}
            </template>
            <template v-else>
              {{ $t('common.noDataFoundDescription') || 'Try adjusting your search or filter to find what you\'re looking for.' }}
            </template>
          </p>
          <slot name="empty-action" />
        </div>
      </div>
    </template>
    <template
      #paginatorcontainer="{
        rows: selectedRows,
        page,
        pageCount = 0,
        pageLinks = [],
        prevPageCallback,
        nextPageCallback,
        rowChangeCallback,
        changePageCallback,
      }"
    >
      <div class="paginator-container">
        <div class="flex items-center gap-2">
          <span class="hidden sm:block">Show</span>
          <Select
            :model-value="selectedRows"
            :options="props.rowsPerPageOptions?.map(item => ({ label: item, value: item }))" option-label="label"
            option-value="value" @change="e => rowChangeCallback(e.value)"
          />
        </div>
        <div class="paginator-button-container">
          <Divider align="center" layout="vertical" />
          <Button
            v-if="page !== 0" icon="pi pi-chevron-left" class="paginator-button" label="PREV" rounded text
            @click="prevPageCallback"
          />
          <Button
            v-for="i in pageLinks" :key="i" :label="String(i)" :disabled="i === page + 1"
            class="paginator-button-page" @click="changePageCallback(i - 1)"
          />
          <Button
            v-if="page !== pageCount - 1" label="NEXT" class="paginator-button" icon="pi pi-chevron-right"
            icon-pos="right" rounded text @click="nextPageCallback"
          />
        </div>
      </div>
    </template>
  </DataTable>
</template>

<style scoped lang="scss">
.base-data-table.transactions-table {
  --p-datatable-header-padding: 0.4rem 0;
  --p-datatable-header-cell-padding:0.4rem 0;
  --p-datatable-header-cell-color: #545454;
  --p-datatable-row-color: #545454;
  --p-button-text-secondary-color: #545454;
  --p-datatable-body-cell-padding: 1.2rem 0;
  --p-datatable-body-cell-border-color: #545454;
}

.p-datatable-thead {
  font-size: 16px;
}

.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  width: 100%;
}

.empty-state-card {
  background: var(--surface-card, #ffffff);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  max-width: 400px;
}

.error-state {
  border: 1px solid var(--red-200, #ef9a9a);
}

.empty-state-icon-container {
  background: var(--p-primary-500);
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.error-icon {
  background: var(--red-500, #f44336);
}

.empty-state-icon-container i {
  font-size: 2rem;
  color: white;
}

.empty-state-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--text-color, #495057);
}

.empty-state-message {
  color: var(--text-color-secondary, #6c757d);
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.table-search {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.paginator-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;

  :deep(.p-select) {
    border-radius: 12px;
    --p-select-background: transparent;
  }

  .paginator-button-container {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .paginator-button-page {
    border-radius: 12px;
    padding-inline: 12px;
    border: 1px solid var(--p-primary-500);
    background: transparent;
    color: var(--p-primary-500);
  }

  .paginator-button {
    border-radius: 12px;
    padding-inline: 18px;
    border: 1px solid var(--p-primary-500);
  }
}

:deep(.p-datatable-paginator-bottom) {
  --p-paginator-padding: 12px 0;
  border-bottom: none;

  .p-paginator {
    background-color: transparent;
  }
}

.p-datatable-table-container {
  .p-datatable-tbody {
    tr {
      &:last-child {
        td {
          border-bottom: none;
        }
      }
    }
  }
}
</style>
