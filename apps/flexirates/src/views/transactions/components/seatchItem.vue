<script setup lang="ts">
import type { SearchField } from '@/types/search'
import Button from 'primevue/button'
import Checkbox from 'primevue/checkbox'
import InputNumber from 'primevue/inputnumber'
import InputSwitch from 'primevue/inputswitch'
import InputText from 'primevue/inputtext'
import MultiSelect from 'primevue/multiselect'
import RadioButton from 'primevue/radiobutton'
import Select from 'primevue/select'
import { watch } from 'vue'
import BaseRangeDatePicker from '@/components/common/BaseRangeDatePicker.vue'
import { SearchFieldType } from '@/constants/search'

const props = defineProps<{
  field: SearchField
  modelValue: Record<string, any>
  isAdvanced?: boolean
  onClear?: () => void
}>()

const emit = defineEmits(['update:modelValue'])

// Ensure NUMBER_RANGE fields always have array values
const ensureNumberRangeArray = () => {
  if (props.field.type === SearchFieldType.NUMBER_RANGE) {
    if (!props.modelValue[props.field.name] || !Array.isArray(props.modelValue[props.field.name])) {
      props.modelValue[props.field.name] = [null, null]
    }
  }
}

// Initialize on component mount and when field or modelValue changes
watch(() => props.field, ensureNumberRangeArray, { immediate: true })
watch(() => props.modelValue, ensureNumberRangeArray, { immediate: true, deep: true })

// 确保复选框和开关组件的模型值为布尔类型
const ensureBooleanType = (_: string, value: any): boolean => {
  if (typeof value === 'boolean') {
    return value
  }
  return value === 'true' || value === true || false
}

// Handle clear button click for NUMBER_RANGE
const handleClear = () => {
  if (props.onClear) {
    props.onClear()
  }
  else if (props.field.type === SearchFieldType.NUMBER_RANGE) {
    props.modelValue[props.field.name] = [null, null]
    emit('update:modelValue', props.modelValue)
  }
}

// Handle min value change with validation
const handleMinChange = (value: number | null) => {
  if (props.field.type === SearchFieldType.NUMBER_RANGE && Array.isArray(props.modelValue[props.field.name])) {
    const maxValue = props.modelValue[props.field.name][1]

    // Update min value
    props.modelValue[props.field.name][0] = value

    // If max is not null and min > max, update max to min
    if (value !== null && maxValue !== null && value > maxValue) {
      props.modelValue[props.field.name][1] = value
    }

    emit('update:modelValue', props.modelValue)
  }
}

// Handle max value change with validation
const handleMaxChange = (value: number | null) => {
  if (props.field.type === SearchFieldType.NUMBER_RANGE && Array.isArray(props.modelValue[props.field.name])) {
    const minValue = props.modelValue[props.field.name][0]

    // Update max value
    props.modelValue[props.field.name][1] = value

    // If min is not null and min > max, update min to max
    if (value !== null && minValue !== null && minValue > value) {
      props.modelValue[props.field.name][0] = value
    }

    emit('update:modelValue', props.modelValue)
  }
}
</script>

<template>
  <!-- 文本字段 -->
  <div v-if="field.type === SearchFieldType.TEXT" class="search-form-input-group">
    <i v-if="!isAdvanced" class="pi pi-search search-form-input-icon" />
    <InputText
      v-model="modelValue[field.name]" :placeholder="field.placeholder"
      :class="isAdvanced ? 'w-full' : 'search-form-input'"
      :style="field.width && !isAdvanced ? { width: field.width } : {}" :maxlength="field.maxlength"
    />
  </div>

  <!-- 下拉选择 -->
  <div v-else-if="field.type === SearchFieldType.SELECT" :class="isAdvanced ? '' : 'search-form-select-group'">
    <Select
      v-model="modelValue[field.name]" :options="field.options" option-label="label" option-value="value"
      :placeholder="field.placeholder || 'All'" :class="isAdvanced ? 'w-full' : 'search-form-select'"
      :style="field.width && !isAdvanced ? { width: field.width } : {}" show-clear
    >
      <template #dropdownicon>
        <span class="search-form-select-arrow" />
      </template>
    </Select>
  </div>

  <!-- 下拉多选 -->
  <div v-else-if="field.type === SearchFieldType.MULTISELECT" :class="isAdvanced ? '' : 'search-form-select-group'">
    <MultiSelect
      v-model="modelValue[field.name]" :options="field.options" option-label="label" option-value="value"
      :placeholder="field.placeholder || ''" :class="isAdvanced ? 'w-full' : 'search-form-select'"
      :style="field.width && !isAdvanced ? { width: field.width } : {}" show-clear
      :max-selected-labels="field.maxLabels || 1" filter
    >
      <template #dropdownicon>
        <span class="search-form-select-arrow" />
      </template>
    </MultiSelect>
  </div>

  <!-- 日期范围选择 -->
  <BaseRangeDatePicker
    v-else-if="field.type === SearchFieldType.DATE_RANGE" v-model="modelValue[field.name]"
    class="search-form-date-range w-full" selection-mode="range" :placeholder="field.placeholder || 'Select Date Range'"
    date-format="dd/mm/yy"
  />

  <!-- 数值输入 -->
  <InputNumber
    v-else-if="field.type === SearchFieldType.NUMBER" v-model="modelValue[field.name]"
    :placeholder="field.placeholder || 'Enter Number'" :min="field.min" :max="field.max" :step="field.step || 1"
    class="w-full"
  />

  <!-- 价格区间 -->
  <div v-else-if="field.type === SearchFieldType.NUMBER_RANGE" class="flex items-center gap-3">
    <InputNumber
      v-if="Array.isArray(modelValue[field.name])" :model-value="modelValue[field.name][0]" placeholder="Min"
      :min="field.min || 0" :max="field.max || 999999999" :step="field.step || 1" class="flex-1"
      @update:model-value="handleMinChange"
    />
    <InputNumber
      v-if="Array.isArray(modelValue[field.name])" :model-value="modelValue[field.name][1]" placeholder="Max"
      :min="field.min || 0" :max="field.max || 999999999" :step="field.step || 1" class="flex-1"
      @update:model-value="handleMaxChange"
    />
    <Button
      v-if="Array.isArray(modelValue[field.name]) && (modelValue[field.name][0] !== null || modelValue[field.name][1] !== null)"
      severity="warn" icon="pi pi-times" text class="clear-btn" @click="handleClear"
    />
  </div>

  <!-- 复选框 -->
  <div v-else-if="field.type === SearchFieldType.CHECKBOX" class="flex items-center gap-2">
    <Checkbox
      :model-value="ensureBooleanType(field.name, modelValue[field.name])" :binary="true" :input-id="field.name"
      @update:model-value="val => modelValue[field.name] = val"
    />
    <label :for="field.name" class="cursor-pointer">{{ field.label }}</label>
  </div>

  <!-- 单选按钮组 -->
  <div v-else-if="field.type === SearchFieldType.RADIO" class="flex flex-wrap gap-6">
    <div v-for="option in field.options" :key="option.value as number" class="flex items-center gap-2">
      <RadioButton v-model="modelValue[field.name]" :value="option.value" :input-id="`${field.name}_${option.value}`" />
      <label :for="`${field.name}_${option.value}`" class="cursor-pointer">{{ option.label }}</label>
    </div>
  </div>

  <!-- 开关 -->
  <div v-else-if="field.type === SearchFieldType.SWITCH" class="flex items-center gap-2">
    <InputSwitch
      :model-value="ensureBooleanType(field.name, modelValue[field.name])" :input-id="field.name"
      @update:model-value="val => modelValue[field.name] = val"
    />
    <label :for="field.name" class="cursor-pointer">{{ field.label }}</label>
  </div>
</template>
