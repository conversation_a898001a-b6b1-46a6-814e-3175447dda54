<script setup lang="ts">
import type { FormContext } from 'vee-validate'
import type { Schema } from 'yup'
import { toTypedSchema } from '@vee-validate/yup'
import { Field, Form as VeeForm } from 'vee-validate'
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import * as yup from 'yup'
import GoogleRecaptcha from '@/components/googleRecaptchaV2/index.vue'
import { useCountdown } from '@/composables/useCountdown'
import { user as userApi } from '@/services/flexirates'
import { useLayoutStore } from '@/store/modules/layout'
import { useUserStore } from '@/store/modules/user'

const emit = defineEmits<{
  (e: 'changeShowType', type: 'login' | 'register' | 'forgetPassword'): void
  (e: 'sendVerificationCode', phoneData: { countryCode: string, phoneNumber: string }): void
}>()

enum LoginType {
  Email = 'Email',
  Phone = 'Phone',
}

enum PhoneStep {
  SendCode = 'sendCode',
  VerifyCode = 'verifyCode',
}

// Update the interface to include phone login fields
interface ExtendedLoginReq extends Api.FlexiratesUserLoginReq {
  type: LoginType
  phoneStep: PhoneStep
  verification_code: string
}

const { t } = useI18n()
const loading = ref(false)
const sendingCode = ref(false)
const showCountryDropdown = ref(false)

const router = useRouter()

const userStore = useUserStore()
const { isDarkTheme } = useLayoutStore()

// 使用倒计时钩子函数
const { counting, formattedTime, start: startCountdown } = useCountdown({
  storageKey: 'flexirates_verification_code_countdown',
})

const model = ref<ExtendedLoginReq>({
  email: '',
  password: '',
  rememberMe: false,
  google_token: '',
  type: LoginType.Email,
  country_code: '61',
  mobile_phone: '',
  verification_code: '',
  phoneStep: PhoneStep.SendCode,
})

const recaptchaRef = ref<InstanceType<typeof GoogleRecaptcha> | null>(null)
const formRef = ref<FormContext | null>(null)
const isShowRegister = ref(false)

const fetchCheckTimeRegister = async () => {
  const { data: checkTimeRegister } = await userApi.getCheckTimeRegister()
  isShowRegister.value = checkTimeRegister.allowRegister
}

fetchCheckTimeRegister()

const schema = toTypedSchema(yup.object({
  type: yup.string().optional(),
  // email login
  email: yup.string().when('type', {
    is: (type: LoginType) => type === LoginType.Email,
    then: (schema: Schema) => schema.required('Email is required').email('Email is invalid'),
    otherwise: (schema: Schema) => schema.optional(),
  }),
  password: yup.string().when('type', {
    is: (type: LoginType) => type === LoginType.Email,
    then: (schema: Schema) => schema.required('Password is required'),
    otherwise: (schema: Schema) => schema.optional(),
  }),
  google_token: yup.string().when('type', {
    is: () => model.value.type === LoginType.Email,
    then: (schema: Schema) => schema.required('Google token is required'),
    otherwise: (schema: Schema) => schema.optional(),
  }),
  rememberMe: yup.boolean().optional(),

  // phone login
  mobile_phone: yup.string().when('type', {
    is: () => model.value.type === LoginType.Phone,
    then: (schema: Schema) => {
      // 校验规则 改成 04xx xxx xxx
      return schema.required('Phone number is required').matches(/^04\d{2} \d{3} \d{3}$/, 'Phone number is invalid')
    },
    otherwise: (schema: Schema) => schema.optional(),
  }),

  verification_code: yup.string().when('type', {
    is: () => model.value.type === LoginType.Phone,
    then: (schema: Schema) => schema.required('Verification code is required'),
    otherwise: (schema: Schema) => schema.optional(),
  }),
}))

const onRecaptchaVerify = (response: string) => {
  if (response) {
    model.value.google_token = response
    if (formRef.value) {
      formRef.value.setFieldValue('google_token', response)
    }
  }
}

const onRecaptchaExpired = () => {
  model.value.google_token = ''
  if (formRef.value) {
    formRef.value.setFieldValue('google_token', '')
  }
}

const onRecaptchaError = () => {
  model.value.google_token = ''
  if (formRef.value) {
    formRef.value.setFieldValue('google_token', '')
  }
}

const countryCodes = [
  { name: 'AU', code: '+61' },
]

const selectedCountry = computed(() => {
  return countryCodes.find(c => c.code === model.value.country_code) || countryCodes[0]
})

const sendVerificationCode = async () => {
  formRef.value?.validateField('mobile_phone').then(async (result) => {
    if (result.valid) {
      try {
        sendingCode.value = true

        const { code } = await userApi.sendVerificationCode({
          mobile: (model.value.mobile_phone as string).replace(/\s/g, ''),
          country_code: model.value.country_code as string,
        })

        if (code === 0) {
          // 开始倒计时
          startCountdown()

          window.$toast.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Verification code sent successfully',
          })

          model.value.phoneStep = PhoneStep.VerifyCode
        }
      }
      finally {
        sendingCode.value = false
      }
    }
  })
}

const onFormSubmit = async () => {
  const result = await formRef.value?.validate()
  if (result?.valid) {
    try {
      loading.value = true
      const sendData = { ...model.value }
      if (model.value.type === LoginType.Email) {
        sendData.mobile_phone = ''
        sendData.verification_code = ''
        sendData.country_code = ''
      }
      else {
        sendData.mobile_phone = sendData.mobile_phone?.replace(/\s/g, '')
        sendData.email = ''
        sendData.password = ''
      }
      await userStore.login(sendData)
      router.push({
        path: '/home',
      })
    }
    catch {
      model.value.google_token = ''
      recaptchaRef.value?.reset()
    }
    finally {
      loading.value = false
    }
  }
}

// Click outside handler to close country dropdown
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

const countryCodeSelectorRef = ref<HTMLElement | null>(null)

const handleClickOutside = (event: MouseEvent) => {
  if (countryCodeSelectorRef.value && !countryCodeSelectorRef.value.contains(event.target as Node)) {
    showCountryDropdown.value = false
  }
}
</script>

<template>
  <div class="login-form-wrap">
    <div class="login-form-content">
      <h1 class="title !mt-0">
        Welcome back
      </h1>
      <p class="subtitle">
        Login <span class="subtitle-account">To Your Account</span>
      </p>
      <p v-if="isShowRegister" class="flexirates-register-link">
        Don’t have an account? <span class="flexirates-register-link-text" @click="emit('changeShowType', 'register')">
          Register here
        </span>
      </p>
      <p v-else class="flexirates-register-link" />

      <div class="login-form-type">
        <button
          class="login-form-type-button" :class="{ active: model.type === LoginType.Email }"
          @click="model.type = LoginType.Email"
        >
          Email
        </button>
        <button
          class="login-form-type-button" :class="{ active: model.type === LoginType.Phone }"
          @click="model.type = LoginType.Phone"
        >
          Mobile
        </button>
      </div>

      <VeeForm ref="formRef" :validation-schema="schema" class="login-form" @submit="onFormSubmit">
        <!-- Email login form -->
        <div v-show="model.type === 'Email'" class="login-form-email">
          <Field
            v-slot="{ field, errorMessage }" v-model="model.email" as="div" class="flex flex-col gap-2"
            name="email"
          >
            <InputText v-bind="field" class="form-input" name="email" type="text" :placeholder="t('login.email')" />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>
          <Field
            v-slot="{ field, errorMessage }" v-model="model.password" as="div" class="flex flex-col gap-2"
            name="password"
          >
            <Password
              v-bind="field" type="text" :placeholder="t('login.password')" toggle-mask fluid
              :feedback="false"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>
          <div class="flex gap-4 justify-between tools">
            <div class="flex items-center gap-2">
              <Checkbox v-model="model.rememberMe" name="rememberMe" binary />
              <label for="rememberMe" class="font-light">{{ t("login.rememberMe") }}</label>
            </div>
            <Button
              text :label="t('login.forgotPassword')" style="--p-button-label-font-weight: 300" class="!p-0"
              @click="emit('changeShowType', 'forgetPassword')"
            />
          </div>

          <Field
            v-slot="{ errorMessage }" v-model="model.google_token" as="div" class="flex flex-col"
            name="google_token"
          >
            <p class="text-lg !m-0 recaptcha-title">
              *CAPTCHA
            </p>
            <GoogleRecaptcha
              ref="recaptchaRef" name="google_token" class="mb-2 mt-2" :theme="isDarkTheme ? 'dark' : 'light'"
              @verify="onRecaptchaVerify" @expired="onRecaptchaExpired"
              @error="onRecaptchaError"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>
        </div>

        <!-- Phone login form -->
        <div v-show="model.type === 'Phone'" class="login-form-phone">
          <Field
            v-slot="{ errorMessage }" v-model="model.mobile_phone" name="mobile_phone" as="div"
            class="flex flex-col gap-2"
          >
            <label class="text-left font-medium mb-1">Mobile</label>
            <div class="mobile-input-container">
              <div
                ref="countryCodeSelectorRef" class="country-code-selector"
                @click.stop="showCountryDropdown = !showCountryDropdown"
              >
                <span>{{ selectedCountry.name }}</span>
                <i class="pi pi-chevron-down" />
                <div v-if="showCountryDropdown" class="country-dropdown-menu">
                  <div
                    v-for="country in countryCodes" :key="country.code" class="country-dropdown-item"
                    :class="{ active: model.country_code === country.code }"
                    @click.stop="model.country_code = country.code; showCountryDropdown = false"
                  >
                    <span>{{ country.name }}</span>
                    <span class="country-code">{{ country.code }}</span>
                  </div>
                </div>
              </div>
              <InputMask id="phone" v-model="model.mobile_phone" class="mobile-phone-input" mask="9999 999 999" placeholder="04xx xxx xxx" />
            </div>
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>

          <Field
            v-if="model.phoneStep === PhoneStep.VerifyCode" v-slot="{ errorMessage }" v-model="model.verification_code" name="verification_code"
            as="div"
            class="flex flex-col gap-2"
          >
            <label class="text-left font-medium mb-1">Verification Code</label>
            <InputText v-model="model.verification_code" maxlength="6" />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>
        </div>
      </VeeForm>
    </div>
    <div class="form-tools">
      <Button
        v-if="model.type === LoginType.Phone && model.phoneStep === PhoneStep.VerifyCode" class="w-full"
        severity="warn" text :label="counting ? `RESEND IN ${formattedTime}` : 'RESEND'"
        :disabled="counting"
        :loading="sendingCode"
        @click="sendVerificationCode"
      />
      <div class="form-tools-item">
        <Button
          v-if="model.type === LoginType.Email" class="login-submit w-full" :loading="loading" severity="info"
          label="LOG IN" @click="onFormSubmit"
        />
        <Button
          v-if="model.type === LoginType.Phone && model.phoneStep === PhoneStep.SendCode" :loading="sendingCode" class="send-access-code-btn w-full"
          severity="info" label="SEND ACCESS CODE" @click="sendVerificationCode"
        />
        <Button
          v-if="model.type === LoginType.Phone && model.phoneStep === PhoneStep.VerifyCode" class="login-submit w-full" :loading="loading" severity="info"
          label="LOG IN" @click="onFormSubmit"
        />
        <div class="h-3" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints' as *;
@use 'sass:color';

.login-form-wrap {
  background: var(--color-white);
  padding: 30px 50px;
  padding-bottom: 128px;
  border-radius: 24px;
  max-width: 800px;
  min-width: 600px;
  min-height: 660px;
  z-index: 2;
  position: relative;

  .p-button-info {
    background: #0073CF;
    --p-button-info-border-color: #0073CF;
  }

  @include media-breakpoint-down(lg) {
    min-width: 400px;
  }

  @include media-breakpoint-down(md) {
    width: 100%;
    min-width: 100%;
    padding: 20px;
  }

  .login-form-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;

    .login-form {
      width: 100%;

      .login-form-email,
      .login-form-phone {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 1.3rem;
      }
    }
  }

  :deep(.p-inputtext) {
    height: 50px;
    border-radius: 8px;
    font-size: 16px;
    padding: 0 1.5rem;
    font-weight: 600;
  }

  .title {
    margin-top: 0;
    margin-bottom: 0.25rem;
    font-size: 54px;
    font-weight: 800;
  }

  .subtitle {
    font-size: 18px;
    margin-bottom: 1rem;
    color: #0073CF;

    .subtitle-account {
      color: var(--color-gray-500);
    }
  }

  .login-form-type {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;

    .login-form-type-button {
      border: 1px solid #0073CF;
      width: 170px;
      height: 45px;
      border-radius: 8px;
      background: var(--color-white);
      color: #0073CF;
      cursor: pointer;
      font-size: 16px;
      font-weight: 600;
      transition: all .3s ease;

      @include media-breakpoint-down(md) {
        width: 100px;
      }

      &:hover {
        background: #0073CF;
        color: var(--color-white);
      }

      &.active {
        background: #0073CF;
        color: var(--color-white);
      }

    }
  }

  .flexirates-register-link {
    font-size: 18px;
    margin-bottom: 1rem;
    text-align: center;
    color: var(--color-gray-500);
    &-text {
      cursor: pointer;
      color: #0073CF;
      text-decoration: underline;
    }
  }

  .login-submit {
    border-radius: 14px;
    font-size: 14px;
    height: 50px;
    --p-button-padding-y: 16px;
    --p-button-padding-x: 24px;
    --p-button-border-color: var(--p-gray-800);
  }

  .recaptcha-title {
    font-weight: 600;
    color: var(--color-gray-500);
  }

  :deep(.p-password-toggle-mask-icon) {
    --p-icon-size: 22px;
  }

  .country-dropdown {
    width: 80px;
    min-width: 80px;

    :deep(.p-dropdown-label) {
      padding-right: 0.5rem;
    }
  }

  .phone-input {
    flex: 1;
  }

  .send-code-btn {
    min-width: 150px;
    border-radius: 8px;
    background-color: var(--color-orange-500);
    color: white;
    font-weight: 600;
    font-size: 14px;

    &:disabled {
      background-color: var(--color-gray-100);
    }
  }

  .mobile-input-container {
    display: flex;
    align-items: center;
    width: 100%;
    height: 50px;
    border: 1px solid var(--color-black);
    border-radius: 8px;
    overflow: hidden;

    .mobile-phone-input {
      flex: 1;
      border: none !important;
      border-radius: 0 !important;

      &:focus {
        box-shadow: none !important;
      }
    }

  }

  .country-code-selector {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 14px;
    width: 70px;
    height: 100%;
    border-right: 1px solid var(--color-black);
    cursor: pointer;
    position: relative;

    span {
      font-weight: 600;
      color: #495057;
    }

    i {
      font-size: 12px;
      color: #6c757d;
    }
  }

  .country-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 200px;
    max-height: 240px;
    overflow-y: auto;
    background-color: white;
    border: 1px solid #ced4da;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 100;
    margin-top: 5px;
  }

  .country-dropdown-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 12px;
    cursor: pointer;

    &:hover {
      background-color: #f8f9fa;
    }

    &.active {
      background-color: #f1f1f1;
    }

    .country-code {
      font-weight: 600;
    }
  }

  .send-access-code-btn {
    height: 50px;
    width: 100%;
    border-radius: 14px;
    color: white;
    font-weight: 600;
    font-size: 16px;
  }
}
</style>
