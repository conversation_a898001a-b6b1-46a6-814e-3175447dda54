<script setup lang="ts">
import logo from '@/assets/flexirates/flex-login-logo-mini.png'
</script>

<template>
  <div class="bottom-info-wrap">
    <div class="bottom-info-content">
      <div class="bottom-info-left">
        <div class="bottom-info__item">
          <span class="bottom-info__item-title">
            Help Center
          </span>
          <span class="bottom-info__item-title">
            Privacy
          </span>
          <span class="bottom-info__item-title">
            Terms
          </span>
        </div>
      </div>
      <div class="bottom-info-center">
        <span class="bottom-info-center__title">
          Powered by
        </span>
        <div class="logo-wrap">
          <Image :src="logo" width="120px" alt="Image" />
        </div>
      </div>
      <div class="bottom-info-right">
        © Bill Buddy PTY LTD. All Rights Reserved
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints' as *;

.bottom-info-wrap {
    position: absolute;
    bottom: 24px;
    left: 0;
    right: 0;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;

    @include media-breakpoint-down(md) {
        height: 70px;
        bottom: 20px;
    }

    .bottom-info-content {
        max-width: 1700px;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;

        @include media-breakpoint-down(md) {
            flex-direction: column;
        }

        .bottom-info__item {
            display: flex;
            gap: 4rem;

            &-title {
                cursor: pointer;
                text-decoration: underline;
            }
        }

        .bottom-info-left,
        .bottom-info-right {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .bottom-info-center {
            width: 210px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            &__title {
                display: inline-block;
                width: 100px;
                font-size: 14px;
                font-weight: 300;
                text-align: right;
            }
        }

        .bottom-info-right {
            @include media-breakpoint-down(md) {
                display: none;
            }
        }

        .logo-wrap {
            width: 120px;
            border-radius: 4px;
            margin-left: 8px;

            img {
                width: 100%;
            }
        }
    }
}
</style>
