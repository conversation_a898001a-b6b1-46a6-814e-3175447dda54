<script setup lang="ts">
import type { FormContext } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/yup'
import { Field, Form as VeeForm } from 'vee-validate'
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import * as yup from 'yup'
import { user as userApi } from '@/services/flexirates'

const emit = defineEmits<{
  (e: 'changeShowType', type: 'login' | 'register' | 'forgetPassword'): void
}>()

enum PasswordStrength {
  Low = 'Low',
  Medium = 'Medium',
  Excellent = 'Excellent',
}

const currentPath = ref<string>(window.location.href)

const route = useRoute()

const forgetPasswordToken = ref<string>(route.query.forgetPasswordToken as string || '')

const isSend = ref(false)

const sendLoading = ref(false)
const resetLoading = ref(false)

const passwordStrength = ref(PasswordStrength.Low)

const formRef = ref<FormContext>()
const newPasswordFormRef = ref<FormContext>()

const schema = toTypedSchema(yup.object({
  email: yup.string().email().required(),
}))

const newPasswordSchema = toTypedSchema(yup.object({
  newPassword: yup.string()
    .required('Password required')
    .min(8, 'At least 8 characters')
    .test('lowercase', 'Must include a lowercase letter', v => /[a-z]/.test(v || ''))
    .test('uppercase', 'Must include an uppercase letter', v => /[A-Z]/.test(v || ''))
    .test('number', 'Must include a number', v => /\d/.test(v || ''))
    .test('symbol', 'Must include a symbol', v => /[!@#$%^&*(),.?":{}|<>]/.test(v || '')),
  confirmPassword: yup.string()
    .oneOf([yup.ref('newPassword')], 'Passwords must match')
    .required('Confirm password'),
}))

const onPasswordChange = (value: string | undefined) => {
  if (!value) {
    passwordStrength.value = PasswordStrength.Low
    return
  }

  // 检查各种字符类型
  const hasLowerCase = /[a-z]/.test(value)
  const hasUpperCase = /[A-Z]/.test(value)
  const hasNumber = /\d/.test(value)
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value)
  const length = value.length

  // 计算满足的条件数量
  let score = 0
  if (length >= 8) { score++ }
  if (hasLowerCase) { score++ }
  if (hasUpperCase) { score++ }
  if (hasNumber) { score++ }
  if (hasSpecialChar) { score++ }
  if (length >= 12) { score++ }

  // 根据得分判断强度
  if (score <= 2) {
    passwordStrength.value = PasswordStrength.Low
  }
  else if (score <= 4) {
    passwordStrength.value = PasswordStrength.Medium
  }
  else {
    passwordStrength.value = PasswordStrength.Excellent
  }
}

const onFormSubmit = async () => {
  const res = await formRef.value?.validate()
  if (res?.valid) {
    sendLoading.value = true
    try {
      const { code } = await userApi.sendResetPasswordEmail({ email: res.values?.email as string, path: currentPath.value })
      if (code === 0) {
        window.$toast.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Email sent successfully',
          life: 3000,
        })
        isSend.value = true
      }
    }
    finally {
      sendLoading.value = false
    }
  }
}

const onNewPasswordSubmit = async () => {
  const res = await newPasswordFormRef.value?.validate()
  if (res?.valid) {
    resetLoading.value = true
    try {
      const { code } = await userApi.resetPassword({ token: forgetPasswordToken.value, new_password: res.values?.newPassword })
      if (code === 0) {
        window.$toast.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Password reset successfully',
        })
        // 重置成功后跳转到登录页面
        emit('changeShowType', 'login')
      }
    }
    finally {
      resetLoading.value = false
    }
  }
}
</script>

<template>
  <div class="forget-password" :class="{ 'has-token': forgetPasswordToken }">
    <template v-if="forgetPasswordToken">
      <h1 class="forget-password__title">
        New Password
      </h1>
      <p class="forget-password__desc">
        Enter a new password.
      </p>

      <VeeForm
        ref="newPasswordFormRef" :validation-schema="newPasswordSchema"
        :initial-values="{ newPassword: '', confirmPassword: '' }" class="new-password-form"
        @submit="onNewPasswordSubmit"
      >
        <Field v-slot="{ field, errorMessage }" name="newPassword" as="div" class="form-item">
          <div class="form-item__content flex flex-col gap-y-2">
            <Password
              v-model="field.value"
              :class="{ 'p-invalid': errorMessage, 'p-valid': field.value && !errorMessage }" name="newPassword"
              placeholder="New Password*" class="form-input" :toggle-mask="true" :input-props="{
                autocomplete: 'new-password',
              }" :feedback="false" @value-change="onPasswordChange"
            />

            <template v-if="field.value">
              <div class="password-strength">
                <div
                  class="password-strength__bar"
                  :class="passwordStrength === PasswordStrength.Low ? 'password-strength__bar--low' : passwordStrength === PasswordStrength.Medium ? 'password-strength__bar--medium' : 'password-strength__bar--strong'"
                >
                  <div class="password-strength__progress" />
                  <div
                    v-if="passwordStrength === PasswordStrength.Medium || passwordStrength === PasswordStrength.Excellent"
                    class="password-strength__progress"
                  />
                  <div v-if="passwordStrength === PasswordStrength.Excellent" class="password-strength__progress" />
                </div>
              </div>
              <div class="password-strength-tips">
                <span class="password-strength__text">Password strength: {{ passwordStrength }}</span>
                <Message v-if="errorMessage" severity="error" variant="simple">
                  {{ errorMessage }}
                </Message>
              </div>
            </template>
          </div>
        </Field>

        <Field v-slot="{ field, errorMessage }" name="confirmPassword" as="div" class="form-item mt-12">
          <div class="form-item__content flex flex-col gap-y-2">
            <Password
              v-model="field.value" :class="{ 'p-invalid': errorMessage }" name="confirmPassword"
              placeholder="Re-enter Password*" class="form-input" :toggle-mask="true" :input-props="{
                autocomplete: 'new-password',
              }" :feedback="false"
            />
            <Message v-if="errorMessage" class="!mt-2 !ml-6" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>

        <div class="form-tools">
          <div class="form-tools-item">
            <Button
              :loading="resetLoading" type="submit" severity="info" label="SUBMIT"
              class="new-password-submit w-full"
            />
          </div>
        </div>
      </VeeForm>
    </template>
    <template v-else>
      <template v-if="!isSend">
        <h1 class="forget-password__title">
          Forgot Password
        </h1>
        <p class="forget-password__desc">
          Enter your email and we’ll send you a link to reset your password.
        </p>

        <VeeForm ref="formRef" :validation-schema="schema" class="forget-password-form" @submit="onFormSubmit">
          <Field v-slot="{ field, errorMessage }" name="email" as="div" class="form-item">
            <div class="form-item__content flex flex-col gap-y-2">
              <InputText
                v-model="field.value" :class="{ 'p-invalid': errorMessage }" name="email" type="text"
                placeholder="Email" class="form-input"
              />
              <Message v-if="errorMessage" class="!mt-2 !ml-6" severity="error" variant="simple">
                {{ errorMessage }}
              </Message>
            </div>
          </Field>

          <div class="form-tools">
            <div class="form-tools-item">
              <div class="flex justify-center flex-wrap forget-password-submit-wrap">
                <Button
                  :loading="sendLoading" type="submit" severity="info" label="SUBMIT"
                  class="forget-password-submit"
                />
              </div>
              <div class="flex justify-center mt-4">
                <Button
                  class="!p-0 back-to-login" text label="Back to Login"
                  @click="emit('changeShowType', 'login')"
                />
              </div>
            </div>
          </div>
        </VeeForm>
      </template>
      <!-- check email -->
      <div v-else class="is-send-success">
        <h1 class="title">
          Check Your Inbox
        </h1>
        <p class="forgot-subtitle">
          We have sent password recovery instructions to your email.
        </p>
        <div class="flex justify-center mt-4">
          <Button
            class="back-to-login" severity="primary" label="BACK TO LOGIN"
            @click="emit('changeShowType', 'login')"
          />
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
.forget-password {
  background: var(--color-white);
  padding: 30px 50px;
  padding-bottom: 128px;
  border-radius: 24px;
  max-width: 800px;
  min-width: 600px;
  min-width: 400px;
  position: relative;

  &.has-token {
    min-width: 500px;
    max-height: 500px;
    .form-tools {
      bottom: 24px;
    }
  }

  &__title {
    margin-top: 0;
    margin-bottom: 0.25rem;
    font-size: 40px;
    font-weight: 800;
    color: #031F73;
  }

  &__desc {
    font-size: 16px;
    color: var(--p-gray-500);
    margin-top: 1.5rem;
    margin-bottom: 2rem;
  }

  .form-input,
  :deep(.p-inputtext) {
    border-radius: 22px;
    font-size: 18px;
    height: 54px;
    font-weight: 600;
    width: 100%;
    --p-inputtext-padding-y: 16px;
    --p-inputtext-padding-x: 24px;
    --p-inputtext-border-color: var(--p-gray-800);
  }

  .forget-password-submit-wrap {
    margin-top: 3rem;
  }

  .forget-password-submit,
  .new-password-submit {
    background: #0073CF;
    --p-button-info-border-color: #0073CF;
    flex: 1;
    border-radius: 16px;
    font-size: 14px;
    height: 54px;
    --p-button-padding-y: 16px;
    --p-button-padding-x: 24px;
    --p-button-border-color: var(--p-gray-800);
  }

  .back-to-login {
    color: var(--color-gray-500);

    :deep(.p-button-label) {
      font-weight: 400;
    }
  }

  .is-send-success {
    height: 100%;
    padding: 20px 0;

    .title {
      font-size: 32px;
      font-weight: 800;
      margin-top: 0;
      margin-bottom: 0.25rem;
      color: #031F73;
    }

    .forgot-subtitle {
      margin-top: 1rem;
      font-size: 16px;
      color: var(--color-gray-500);
    }

    .back-to-login {
      position: absolute;
      bottom: 34px;
      left: 0;
      right: 0;
      margin: 0 auto;
      width: 100%;
      max-width: 80%;
      height: 54px;
      color: white;
      font-size: 24px;
      font-weight: 600;
      border-radius: 16px;
    }
  }

  .password-strength-tips {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    color: var(--color-gray-500);
  }

  .password-strength {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    .password-strength__bar {
      width: 100%;
      height: 8px;
      display: flex;
      justify-content: flex-start;
      flex-wrap: nowrap;
      gap: 3.33%;

      &--low {
        --color: var(--color-red-500);
      }

      &--medium {
        --color: var(--color-yellow-500);
      }

      &--strong {
        --color: var(--color-green-500);
      }

      .password-strength__progress {
        width: 30%;
        height: 100%;
        background-color: var(--color);
        border-radius: 10px;
      }
    }
  }
}
</style>
