<script setup lang="ts">
import type { Notification } from '@/types/notification'
import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import { useToast } from 'primevue/usetoast'
import { onMounted, ref } from 'vue'
import { notifications as notificationsApi } from '@/services/flexirates'
import { useNotificationStore } from '@/store/modules/notifications'
import { formatDate } from '@/utils/date'

const toast = useToast()

// 选中的通知ID列表
const selectedNotifications = ref<string[]>([])

// 详情对话框状态
const detailDialogVisible = ref(false)
const selectedNotificationDetail = ref<Notification | null>(null)

// 通知数据类型
const notifications = ref<{
  today: Notification[]
  yesterday: Notification[]
  week_ago: Notification[]
}>({
  today: [
  ],
  yesterday: [

  ],
  week_ago: [
  ],
})

// const totalCount = ref(0)

const notificationStore = useNotificationStore()
const { unreadCount } = storeToRefs(notificationStore)

// 切换通知选中状态
const toggleNotification = (id: string) => {
  const index = selectedNotifications.value.indexOf(id)
  if (index > -1) {
    selectedNotifications.value.splice(index, 1)
  }
  else {
    selectedNotifications.value.push(id)
  }
}

// 标记所有为已读
const markAllAsRead = async () => {
  Object.values(notifications.value).forEach((group) => {
    group.forEach((notification: Notification) => {
      notification.is_read = 1
    })
  })
  const res = await notificationsApi.updateNotify({ is_all: 1 })
  if (res.code === 0) {
    toast.add({ severity: 'success', summary: 'Tips', detail: 'All have been successfully read.' })
  }
}

// 删除所有通知
const deleteAllNotifications = () => {
  window.$confirm.require({
    message: 'Are you sure you want to delete all notifications? This action cannot be undone.',
    header: 'Delete All Notifications',
    icon: 'pi pi-exclamation-triangle',
    acceptClass: 'p-button-danger',
    accept: async () => {
      notifications.value.today = []
      notifications.value.yesterday = []
      notifications.value.week_ago = []
      selectedNotifications.value.splice(0)
      const res = await notificationsApi.deleteNotify({ is_all: 1 })
      if (res.code === 0) {
        toast.add({ severity: 'success', summary: 'Tips', detail: 'All have been successfully deleted.' })
        getAllNotifyList()
      }
    },
  })
}

// 删除通知
const deleteNotification = (id: string) => {
  window.$confirm.require({
    message: 'Are you sure you want to delete this notification?',
    header: 'Delete Confirmation',
    icon: 'pi pi-exclamation-triangle',
    accept: async () => {
      const res = await notificationsApi.deleteNotify({ ids: id })
      if (res.code === 0) {
        toast.add({ severity: 'success', summary: 'Tips', detail: 'All have been successfully deleted.' })
        getAllNotifyList()
      }
    },
  })
}

// 查看通知详情
const viewNotificationDetail = (notification: Notification) => {
  selectedNotificationDetail.value = notification
  detailDialogVisible.value = true
  // 如果通知未读，标记为已读
  if (!notification.is_read) {
    notificationsApi.updateNotify({ is_all: 0, ids: `${notification.id}` })
    notification.is_read = 1
  }
}

// 关闭详情对话框
const closeDetailDialog = () => {
  detailDialogVisible.value = false
  selectedNotificationDetail.value = null
}

// 格式化详细时间
const formatDetailTime = (time: Date) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 获取通知类型对应的颜色
const getTitleColor = (type: number) => {
  switch (type) {
    case 2:
      return '#EB001B'
    default:
      return '#333333'
  }
}

const getTitleWeight = (type: number) => {
  switch (type) {
    case 2:
      return '600'
    default:
      return '500'
  }
}

const getAllNotifyList = async () => {
  const res = await notificationsApi.getNotifyList({ page: 1, page_size: 1000 })
  notifications.value = res.data.data
  // totalCount.value = res.data.total
}
onMounted(() => {
  getAllNotifyList()
})
</script>

<template>
  <div class="notifications-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="flex items-baseline gap-4">
        <h1 class="page-title">
          Notifications
        </h1>
        <div class="total-count">
          Unread Notifications: <span>{{ unreadCount }}</span>
        </div>
      </div>

      <div class="header-actions">
        <Button class="!px-8 btn" @click="deleteAllNotifications">
          DELETE ALL
        </Button>
        <Button severity="warn" class="!ml-4 !px-8 btn" @click="markAllAsRead">
          MARK ALL AS READ
        </Button>
      </div>
    </div>

    <!-- 分隔线 -->
    <div class="divider" />

    <!-- 通知列表 -->
    <div class="notifications-list">
      <!-- 今天 -->
      <div v-if="notifications.today.length > 0" class="notification-section">
        <h2 class="section-title">
          Today
        </h2>
        <div
          v-for="notification in notifications.today" :key="notification.id" class="notification-item"
          :class="{ 'is-read': notification.is_read }"
        >
          <Checkbox
            v-model="notification.is_read" :binary="true" size="large" class="notification-checkbox"
            @change="toggleNotification(notification.id)"
          />
          <div class="notification-content" @click="viewNotificationDetail(notification)">
            <h3
              class="notification-title"
              :style="{ color: getTitleColor(notification.transaction_status), fontWeight: getTitleWeight(notification.transaction_status) }"
            >
              {{ notification.title }}
            </h3>
            <p class="notification-text">
              {{ notification.content }}
            </p>
          </div>
          <div class="notification-actions">
            <i v-if="!notification.is_read" class="pi pi-envelope action-icon" />
            <img v-else src="@/assets/open-notif.png" alt="open-notif" class="action-icon w-8 h-7">
            <i class="pi pi-trash action-icon" @click="deleteNotification(notification.id)" />
            <span class="notification-time">{{ formatDate(notification.created_at) }}</span>
          </div>
        </div>
      </div>

      <!-- 昨天 -->
      <div v-if="notifications.yesterday.length > 0" class="notification-section">
        <h2 class="section-title">
          Yesterday
        </h2>
        <div
          v-for="notification in notifications.yesterday" :key="notification.id" class="notification-item"
          :class="{ 'is-read': notification.is_read }"
        >
          <input
            type="checkbox" :checked="selectedNotifications.includes(notification.id)"
            class="notification-checkbox" @change="toggleNotification(notification.id)"
          >
          <div class="notification-content" @click="viewNotificationDetail(notification)">
            <h3
              class="notification-title"
              :style="{ color: getTitleColor(notification.transaction_status), fontWeight: getTitleWeight(notification.transaction_status) }"
            >
              {{ notification.title }}
            </h3>
            <p class="notification-text">
              {{ notification.content }}
            </p>
          </div>
          <div class="notification-actions">
            <i v-if="!notification.is_read" class="pi pi-envelope action-icon" />
            <img v-else src="@/assets/open-notif.png" alt="open-notif" class="action-icon w-8 h-7">
            <i
              class="pi pi-trash action-icon" :class="{ 'is-read': notification.is_read }"
              @click="deleteNotification(notification.id)"
            />
            <span class="notification-time">{{ formatDate(notification.created_at) }}</span>
          </div>
        </div>
      </div>

      <!-- 最近7天 -->
      <div v-if="notifications.week_ago.length > 0" class="notification-section">
        <h2 class="section-title">
          Last 7 days
        </h2>
        <div
          v-for="notification in notifications.week_ago" :key="notification.id" class="notification-item"
          :class="{ 'is-read': notification.is_read }"
        >
          <input
            type="checkbox" :checked="selectedNotifications.includes(notification.id)"
            class="notification-checkbox" @change="toggleNotification(notification.id)"
          >
          <div class="notification-content" @click="viewNotificationDetail(notification)">
            <h3
              class="notification-title"
              :style="{ color: getTitleColor(notification.transaction_status), fontWeight: getTitleWeight(notification.transaction_status) }"
            >
              {{ notification.title }}
            </h3>
            <p class="notification-text">
              {{ notification.content }}
            </p>
          </div>
          <div class="notification-actions">
            <i v-if="!notification.is_read" class="pi pi-envelope action-icon" />
            <img v-else src="@/assets/open-notif.png" alt="open-notif" class="action-icon w-8 h-7">
            <i class="pi pi-trash action-icon" @click="deleteNotification(notification.id)" />
            <span class="notification-time">{{ formatDate(notification.created_at) }}</span>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div
        v-if="notifications.today.length === 0 && notifications.yesterday.length === 0 && notifications.week_ago.length === 0"
        class="empty-state"
      >
        <p> No notifications found </p>
      </div>
    </div>

    <!-- 通知详情对话框 -->
    <Dialog
      v-model:visible="detailDialogVisible" :style="{ width: '600px' }" header="Notification Details"
      :modal="true" class="notification-detail-dialog"
    >
      <div v-if="selectedNotificationDetail" class="notification-detail">
        <div class="detail-header">
          <h3 class="detail-title" :style="{ color: getTitleColor(selectedNotificationDetail.type) }">
            {{ selectedNotificationDetail.title }}
          </h3>
          <div class="detail-meta">
            <!-- <span class="detail-type">{{ getTypeDisplayName(selectedNotificationDetail.type) }}</span> -->
            <span class="detail-status" :class="{ 'is-read': selectedNotificationDetail.is_read }">
              {{ selectedNotificationDetail.is_read ? 'Read' : 'Unread' }}
            </span>
          </div>
        </div>

        <div class="detail-content">
          <p class="detail-text">
            {{ selectedNotificationDetail.content }}
          </p>
        </div>

        <div class="detail-footer">
          <span class="detail-time">{{ formatDetailTime(selectedNotificationDetail.created_at) }}</span>
        </div>
      </div>

      <template #footer>
        <Button label="Close" icon="pi pi-times" @click="closeDetailDialog" />
      </template>
    </Dialog>
  </div>
</template>

<style lang="scss" scoped>
$text-secondary: #545454;
$text-muted: #545454;
$border-light: #e5e5e5;
$border-lighter: #f0f0f0;
$bg-hover: #fafafa;

@mixin flex-center {
  display: flex;
  align-items: center;
}

@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@mixin text-ellipsis($lines: 1) {
  @if $lines ==1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@mixin button-base {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.2s;
}

.notifications-page {
  margin: 0 auto;
  padding: 24px 30px;
  border-radius: 16px;
  background-color: var(--color-white);

  .page-header {
    @include flex-between;
    margin-bottom: 1rem;

    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: var(--colors-flexirates-primary);
      margin: 0;
    }
  }

  .divider {
    height: 3px;
    background: var(--color-gray-500);
    margin-bottom: 1.5rem;
  }

  .notifications-list {
    display: flex;
    flex-direction: column;
    gap: 2rem;

    .notification-section {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .section-title {
        font-size: 24px;
        font-weight: 600;
        margin: 0;
        padding-bottom: 0.5rem;
        color: #0073cf;
      }

      .notification-item {
        @include flex-center;
        align-items: flex-start;
        gap: 1rem;
        padding: 1rem 0;
        border-bottom: 1px solid var(--color-gray-500);
        transition: background-color 0.2s;
        font-weight: 600;

        &.is-read {
          font-weight: 400;

          .notification-actions {
            .pi-envelope {
              color: var(--color-blue-500);
            }
          }
        }

        &:hover {
          background-color: $bg-hover;
        }

        .notification-checkbox {
          margin-top: 0.2rem;
          cursor: pointer;
        }

        .notification-content {
          flex: 1;
          min-width: 0;
          cursor: pointer;
          padding: 0.25rem;
          border-radius: 4px;
          transition: background-color 0.2s;

          &:hover {
            background-color: rgba(239, 79, 39, 0.05);
          }

          .notification-title {
            font-size: 1rem;
            margin: 0 0 0.5rem 0;
            line-height: 1.4;
            font-weight: 500;
          }

          .notification-text {
            font-size: 0.875rem;
            color: $text-secondary;
            margin: 0;
            line-height: 1.5;
            @include text-ellipsis(2);
          }
        }

        .notification-actions {
          @include flex-center;
          gap: 0.5rem;
          flex-shrink: 0;

          .action-icon {
            font-size: 1rem;
            color: $text-muted;
            cursor: pointer;
            padding: 0.25rem;
            transition: color 0.2s;

            &:hover {
              color: $text-secondary;
            }
          }

          .notification-time {
            font-size: 0.75rem;
            color: $text-muted;
            min-width: 60px;
            text-align: right;
          }
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 3rem 1rem;
      color: $text-muted;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 1rem 0.5rem;

    .page-header {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;

      .header-actions {
        width: 100%;
      }
    }

    .notifications-list {
      .notification-section {
        .notification-item {
          gap: 0.75rem;

          .notification-actions {
            flex-direction: column;
            align-items: flex-end;
          }
        }
      }
    }
  }

  // 通知详情对话框样式
  :deep(.notification-detail-dialog) {
    .p-dialog-content {
      padding: 1.5rem;
    }
  }
}

// 通知详情样式
.notification-detail {
  .detail-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid $border-lighter;

    .detail-title {
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0 0 0.75rem 0;
      line-height: 1.4;
    }

    .detail-meta {
      @include flex-center;
      gap: 1rem;

      .detail-type {
        padding: 0.25rem 0.75rem;
        background: rgba(239, 79, 39, 0.1);
        color: #EF4F27;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
      }

      .detail-status {
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        background: rgba(102, 102, 102, 0.1);
        color: $text-muted;

        &.is-read {
          background: rgba(34, 197, 94, 0.1);
          color: #22c55e;
        }
      }
    }
  }

  .detail-content {
    margin-bottom: 1.5rem;

    .detail-text {
      font-size: 1rem;
      line-height: 1.6;
      color: $text-secondary;
      margin: 0;
      white-space: pre-wrap;
      word-break: break-word;
    }
  }

  .detail-footer {
    padding-top: 1rem;
    border-top: 1px solid $border-lighter;

    .detail-time {
      font-size: 0.875rem;
      color: $text-muted;
      font-style: italic;
    }
  }
}

.btn {
  font-weight: 600;
}
</style>
