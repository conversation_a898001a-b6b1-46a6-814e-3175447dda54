<script setup lang="ts">
import { Format } from '@shared'
import { toTypedSchema } from '@vee-validate/yup'
import dayjs from 'dayjs'
import { useToast } from 'primevue/usetoast'
import { Field, Form as VeeForm } from 'vee-validate'
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import * as yup from 'yup'
import CustomDialog from '@/components/customDialog/index.vue'
import AddCardOrBank from '@/components/payment/addCardOrBank.vue'
import ScheduleEditDialog from '@/components/scheduleDialog/ScheduleEditDialog.vue'
import { useListRefresh } from '@/composables/useListRefresh'
import { home as homeApi, transactions as transactionsApi } from '@/services/flexirates'
import { getBankType, getBankTypeImageUrlLimit } from '@/utils/bank'
import { formatBankAccount } from '@/utils/format'
import AddPropertyDialog from './components/addPropertyDialog.vue'

const toast = useToast()
const route = useRoute()
const router = useRouter()

const { withRefresh } = useListRefresh('flexiratesPaymentMethodList')

const columns = ref<TableColumnItem[]>([
  {
    field: 'date',
    header: 'Due Date',
    style: { width: '120px' },
    template: 'date',
  },
  {
    field: 'amount',
    header: 'Amount',
    style: { width: '150px' },
    template: 'amount',
  },
  {
    field: 'payment_method_desc',
    header: 'Method',
    style: { width: '150px' },
  },
  {
    field: 'status_desc',
    header: 'Status',
    style: { width: '120px' },
  },
])

const newNickName = ref('')
const changeNameVisible = ref(false)
const changeNameLoading = ref(false)

const onceOffVisible = ref(false)
const editPaymentMethodVisible = ref(false)
const onceOffFormRef = ref()
const dialogVisible = ref(false)

const isScheduleEditDialogVisible = ref(false)

const scheduleEditDialogRef = ref<InstanceType<typeof ScheduleEditDialog>>()

const editPropertyList = ref<{ label: string, value: string }[]>([])

const propertyDetail = ref()

const editPaymentForm = ref({
  levelPaymentMethod: true,
  paymentMethod: null,
})

const onceOffForm = ref({
  property: Number(route.params?.id),
  paymentMethod: null,
  amount: null,
})

const schema = toTypedSchema(yup.object({
  levelPaymentMethod: yup.boolean(),
  paymentMethod: yup.mixed<number | string>()
    .test(
      'is-valid-payment-method',
      'Payment method is required',
      (value) => {
        // 如果是数字类型且大于等于 0，则有效
        if (typeof value === 'number' && !Number.isNaN(value)) {
          return true
        }
        return false // 其他情况都失败（如空字符串、null、undefined）
      },
    )
    .required('Payment method is required'),
}))

const onceOffSchema = toTypedSchema(yup.object({
  amount: yup.number().required('Amount is required'),
  paymentMethod: yup
    .mixed<number | string>()
    .test(
      'is-valid-payment-method',
      'Payment method is required',
      (value) => {
        // 如果是数字类型且大于等于 0，则有效
        if (typeof value === 'number' && !Number.isNaN(value)) {
          return true
        }
        // 如果是字符串类型且不是空字符串，也视为有效（例如 ID 字符串）
        if (typeof value === 'string' && value.trim() !== '') {
          return true
        }
        return false // 其他情况都失败（如空字符串、null、undefined）
      },
    )
    .required('Payment method is required'),
}))

const isLoading = ref(false)
const fetchDetail = async () => {
  isLoading.value = true
  const res = await homeApi.getPropertyDetail({ id: Number(route.params?.id) })
  if (res.code === 0) {
    propertyDetail.value = res.data
    editPropertyList.value = [{
      label: propertyDetail.value?.nickname,
      value: propertyDetail.value?.id,
    }]
  }
  isLoading.value = false
}

const getColor = (status: string) => {
  switch (status) {
    case 'Active':
      return '#39b54a'
    case 'Expired':
      return '#eb001b'
    default:
      return '#545454'
  }
}

const changeName = async () => {
  try {
    changeNameLoading.value = true
    const res = await homeApi.changeNickname({
      id: Number(route.params?.id),
      nickname: newNickName.value,
    })
    if (res.code === 0) {
      toast.add({ severity: 'success', summary: 'Tips', detail: 'Successfully change Nickname.', life: 3000 })
      changeNameVisible.value = false
      newNickName.value = ''
      fetchDetail()
      withRefresh()
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    changeNameLoading.value = false
  }
}

const paymentMethodOptions = ref()
const propertyOptions = ref()
const getAllPaymentMethodList = async () => {
  const res = await transactionsApi.getAllAccount()
  paymentMethodOptions.value = res.data.map((item) => {
    return {
      label: item.payment_method,
      value: item.id,
    }
  })
  paymentMethodOptions.value.push({ label: 'Add New Payment Method', value: '' })
}
const getAllPropertyList = async () => {
  const res = await transactionsApi.getAllProperty({ is_all: 1 })
  propertyOptions.value = res.data.map(item => ({
    label: item.street_address,
    value: item.id,
  }))
}

const paymentLoading = ref(false)
const onChangePaymentMethod = async (values: any) => {
  try {
    paymentLoading.value = true
    const sendData = {
      id: Number(route.params?.id),
      payment_method_id: values.paymentMethod,
      payment_method_type: values.levelPaymentMethod ? 1 : 2,
    }
    const res = await homeApi.changePaymentMethod(sendData)
    if (res.code === 0) {
      editPaymentMethodVisible.value = false
      window.$toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Change payment method successful',
      })
      fetchDetail()
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    paymentLoading.value = false
  }
}

const onceOffLoading = ref(false)
const onOnceOffSubmit = async (values: any) => {
  try {
    onceOffLoading.value = true
    const sendData = {
      property_id: Number(route.params?.id),
      payment_method_id: values.paymentMethod,
      amount: values.amount,

    }
    const res = await homeApi.onceOffPayment(sendData)
    if (res.code === 0) {
      onceOffVisible.value = false
      window.$toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'One-off payment successful',
      })
      fetchDetail()
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    onceOffLoading.value = false
  }
}
const showAddPaymentDialog = ref(false)

const handleSelectChange = (event: number | string) => {
  if (typeof event === 'string' && event === '') {
    showAddPaymentDialog.value = true
  }
}

const handleDialogClose = () => {
  getAllPaymentMethodList()
  showAddPaymentDialog.value = false
}

const handleScheduleEditConfirm = () => {
  fetchDetail()
}

const properties = ref<any>([])
const isPropertyLoading = ref(false)
const fetchPropertyList = async () => {
  isPropertyLoading.value = true
  const res = await homeApi.getPropertyList({
    page: 1,
    page_size: 50,
  })
  properties.value = res.data.data
  isPropertyLoading.value = false
}

// const jumpToScheduleDetail = () => {
//   const query = {
//     address: propertyDetail.value.address,
//     status: propertyDetail.value.status,
//     related_id: propertyDetail.value.id,
//   }
//   router.push({
//     name: 'schedulesDetail',
//     params: {
//       id: propertyDetail.value.schedule_details.customer_plan_id,
//     },
//     query,
//   })
// }

const jumpToTransactionList = () => {
  router.push({ name: 'transactions', query: { id: propertyDetail.value.id } })
}

onMounted(() => {
  fetchDetail()
  getAllPaymentMethodList()
  getAllPropertyList()
})
</script>

<template>
  <div>
    <div v-if="!isLoading" class="flexirates-wrap">
      <div class="flexirates-title">
        <div class="flexirates-title-text">
          {{ propertyDetail?.nickname }}
          <div class="flexirates-subtitle-text">
            {{ propertyDetail?.address }}
          </div>
          <div class="flexirates-subtitle-text">
            <span>Property Number:</span> {{ propertyDetail?.property_number }}
          </div>
        </div>
        <div class="flex flex-col lg:flex-row lg:items-center gap-6">
          <Button label="MAKE A ONE-OFF PAYMENT" severity="warn" class="btn" @click="onceOffVisible = true" />
          <Button label="CHANGE NICKNAME" class="btn" @click="changeNameVisible = true" />
          <Button class="btn !w-40" type="button" label="ADD PROPERTY" severity="warn" @click="dialogVisible = true" />
          <Button label="BACK" class="btn !w-24" @click="$router.back()" />
        </div>
      </div>
      <div class="detail">
        <div class="detail-overview">
          <div class="detail-overview-status">
            <div class="overview-title text-xl lg:text-2xl flex gap-2">
              <div>
                Status
              </div>
              <div v-if="propertyDetail?.status === 1 || propertyDetail?.status === 5">
                <BasePopover trigger="hover" placement="right" popper-class="user-setting-popper" width="340px">
                  <template #reference>
                    <i class="pi pi-question-circle" />
                  </template>
                  <div v-if="propertyDetail?.status === 1">
                    Your FlexiRates resigstration is Active.
                  </div>
                  <div v-else>
                    Your FlexiRates registration is On Hold due to non payment, dishonour.
                  </div>
                </BasePopover>
              </div>
            </div>
            <div class="overview-content" :style="{ color: getColor(propertyDetail?.status_desc) }">
              {{ propertyDetail?.status_desc }}
            </div>
            <div class="overview-date">
              since {{ dayjs(propertyDetail?.first_payment_date).format('DD/MM/YYYY') }}
            </div>
          </div>
          <div class="detail-overview-due">
            <div class="overview-title text-xl lg:text-2xl">
              Next Due
            </div>
            <div class="overview-content">
              {{ Format.formatAmount(propertyDetail?.next_payment_amount) }}
            </div>
            <div class="overview-date">
              on {{ dayjs(propertyDetail?.next_payment_date).format('DD/MM/YYYY') }}
            </div>
          </div>
          <div class="detail-overview-total">
            <div class="overview-title text-xl lg:text-2xl">
              Total Paid
            </div>
            <div class="overview-content">
              {{ Format.formatAmount(propertyDetail?.total_paid) }}
            </div>
          </div>
          <div class="detail-overview-total">
            <div class="overview-title text-xl lg:text-2xl">
              Remaining for FY
            </div>
            <div class="overview-content">
              {{ Format.formatAmount(propertyDetail?.total_remaining) }}
            </div>
          </div>
          <div class="detail-overview-total">
            <div class="overview-title text-2xl">
              Total Rate
            </div>
            <div class="overview-content">
              {{ Format.formatAmount(propertyDetail?.total_amount) }}
            </div>
          </div>
        </div>
        <div class="detail-schedule">
          <div class="detail-schedule-title text-xl lg:text-2xl font-semibold flex justify-between items-center">
            Schedule Details
            <Button
              label="EDIT SCHEDULE" class="btn" @click="() => {
                isScheduleEditDialogVisible = true;
                scheduleEditDialogRef?.setPropertyDetailAndLoad(propertyDetail.id)
              }"
            />
          </div>
          <div class="detail-schedule-content">
            <div class="flex justify-start  text-lg">
              <div class="w-100 font-semibold">
                Payment Frequency:
              </div>
              <div>{{ propertyDetail?.schedule_details?.payment_plan_desc }}</div>
            </div>
            <div class="flex justify-start  text-lg">
              <div class="w-100 font-semibold">
                Regular Payment Amount:
              </div>
              <div>
                {{ Format.formatAmount(propertyDetail?.schedule_details?.regular_payment_amount) }}
              </div>
            </div>
            <div class="flex justify-start  text-lg text-[#EB001B]">
              <div class="w-100 font-semibold">
                Failed Payment Count:
              </div>
              <div class="font-semibold">
                {{ propertyDetail?.schedule_details?.failed_payment_count }}
              </div>
            </div>
            <!-- <div class="flex justify-start  text-lg text-sky-600">
              <div class="w-100 font-semibold underline cursor-pointer" @click="jumpToScheduleDetail">
                Total Payments :
              </div>
              <div>
                {{ propertyDetail?.schedule_details?.total_schedules }}
              </div>
            </div>

            <div class="text-right mt-2 mb-2 text-lg text-blue-500 font-semibold">
              You have edited the payment {{ propertyDetail?.schedule_details?.edited_count }} of {{
                propertyDetail?.schedule_details?.editable_count }}
            </div> -->
          </div>
        </div>
        <div class="detail-payment">
          <div class="detail-payment-title text-xl lg:text-2xl font-semibold flex justify-between items-center">
            Payment Method
            <Button label="EDIT PAYMENT METHOD" class="btn" @click="editPaymentMethodVisible = true" />
          </div>
          <div class="detail-payment-content">
            <div v-for="item in propertyDetail?.payment_method" :key="item.id" class="payment-item">
              <div class=" flex items-center gap-4">
                <img :src="getBankTypeImageUrlLimit(item?.credit_brand)" alt="" style="width: 70px;">
                <div>
                  <div class="text-2xl font-semibold pb-3">
                    {{ getBankType(item?.credit_brand) }}
                  </div>
                  <div v-if="item?.account_no" class=" payment-date flex items-center justify-between gap-32">
                    <div>{{ formatBankAccount(item?.account_no) }}</div>
                    <div v-if="item?.expiration_month">
                      Expires <span>{{ item?.expiration_month }}/{{ item?.expiration_year }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="  flex flex-col gap-2 items-end">
                <div v-if="item?.weight === 1" class="flex gap-2 items-center pb-3">
                  <span class="text-(--color-green-600)">{{ item?.weight_desc }}</span>
                  <i class="pi pi-star-fill " style="color: #ffd058;font-size: 20px;" />
                </div>
                <div v-else class="flex gap-2 items-center pb-3 ">
                  <span>{{ item?.weight_desc }}</span>
                </div>
                <div v-if="item?.last_payment_amount">
                  Last Payment: {{ Format.formatAmount(item?.last_payment_amount) }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="detail-history">
          <div class="detail-history-title text-xl lg:text-2xl font-semibold">
            Transaction History
          </div>
          <div class="detail-history-content">
            <BaseDataTable
              class="transaction-history-table" :value="propertyDetail?.transaction_history || []"
              :columns="columns" :show-multiple-column="false" :paginator="false" :show-search-bar="false"
              min-width="60rem"
            >
              <!-- 自定义空状态 -->
              <template #date="{ data }">
                <span class="underline">{{ dayjs(data?.payment_date).format('DD MMM YYYY') }}</span>
              </template>
              <template #amount="{ data }">
                <span>{{ Format.formatAmount(data?.payment_amount) }}</span>
              </template>
              <template #empty-action>
                <Button label="Refresh" />
              </template>
            </BaseDataTable>
            <div v-if="propertyDetail?.transaction_history?.length > 0" class="py-6 flex justify-center">
              <Button label="VIEW ALL TRANSACTION HISTORY" @click="jumpToTransactionList" />
            </div>
          </div>
        </div>
      </div>
      <div class="dialog">
        <CustomDialog
          title="Change Nickname" :visible="changeNameVisible"
          @update:visible="(val) => (changeNameVisible = val)"
        >
          <template #content>
            <div class="w-[500px]">
              <div class="flex items-center py-8 w-full">
                <div class="font-semibold w-[200px] text-xl">
                  New Nickname
                </div>
                <div class="w-full">
                  <InputText v-model="newNickName" type="text" class="w-full" />
                </div>
              </div>
              <div class="flex justify-end">
                <Button
                  label="SAVE" severity="warn" class="save-btn" :loading="changeNameLoading"
                  @click="changeName"
                />
              </div>
            </div>
          </template>
        </CustomDialog>

        <CustomDialog
          title="Make a One-off Payment" :visible="onceOffVisible"
          @update:visible="(val) => (onceOffVisible = val)"
        >
          <template #content>
            <div class="w-[650px]">
              <VeeForm
                ref="onceOffFormRef" :initial-values="onceOffForm" :validation-schema="onceOffSchema"
                @submit="onOnceOffSubmit"
              >
                <div class="flex flex-col gap-6 pt-6">
                  <Field v-slot="{ handleChange }" as="div" name="property">
                    <div class="flex items-center justify-between">
                      <div class="font-semibold text-[16px] w-88">
                        Select Property
                      </div>
                      <Select
                        v-model="onceOffForm.property" :options="propertyOptions" option-label="label"
                        option-value="value" disabled placeholder="Select or add a payment Method" fluid
                        @value-change="handleChange"
                      />
                    </div>
                  </Field>
                  <Field
                    v-slot="{ field, errorMessage, handleChange }" v-model="onceOffForm.paymentMethod" as="div"
                    name="paymentMethod"
                  >
                    <div class="flex items-center justify-between relative">
                      <div class="font-semibold text-[16px] w-88">
                        Select Payment Method
                      </div>

                      <Select
                        v-model="field.value" :options="paymentMethodOptions" option-label="label"
                        option-value="value" class="w-full" placeholder="Select or add payment method" fluid
                        size="large" @value-change="
                          (e: any) => {
                            handleChange(e)
                            handleSelectChange(e)
                          }"
                      >
                        <template #option="slotProps">
                          <div class="flex items-center">
                            <div v-if="slotProps.option.value">
                              {{ slotProps.option.label }}
                            </div>
                            <div v-else>
                              {{ slotProps.option.label }}
                            </div>
                          </div>
                        </template>
                      </Select>
                      <Message
                        v-if="errorMessage" severity="error" variant="simple"
                        class="absolute top-[38px] left-60"
                      >
                        {{
                          errorMessage }}
                      </Message>
                    </div>
                  </Field>
                  <Field
                    v-slot="{ field, errorMessage, handleChange }" v-model="onceOffForm.amount" as="div"
                    name="amount"
                  >
                    <div class="flex items-center justify-between relative">
                      <div class="font-semibold text-[16px] w-88">
                        Amount
                      </div>
                      <InputNumber
                        v-model="field.value" :min-fraction-digits="2" :max-fraction-digits="2"
                        input-id="amount" fluid currency="AUD" locale="en-AU"
                        mode="currency"
                        @value-change="handleChange"
                      />
                      <Message
                        v-if="errorMessage" severity="error" variant="simple"
                        class="absolute top-[38px] left-60"
                      >
                        {{
                          errorMessage }}
                      </Message>
                    </div>
                  </Field>

                  <div class="once-off-tips">
                    <div>
                      Important:
                    </div>
                    <p>
                      Making a one-off payment will not affect your current property payment schedule.Once the payment
                      is settled, it will appear in your Transactions List, and your total remaining payments will be
                      reduced accordingly.
                    </p>
                  </div>
                  <div class="flex justify-end mt-2">
                    <Button
                      label="CONFIRM" severity="warn" class="confirm-btn" type="submit"
                      :loading="onceOffLoading"
                    />
                  </div>
                </div>
              </VeeForm>
            </div>
          </template>
        </CustomDialog>

        <CustomDialog
          title="Edit Payment Method" :visible="editPaymentMethodVisible"
          @update:visible="(val) => (editPaymentMethodVisible = val)"
        >
          <template #content>
            <VeeForm :validation-schema="schema" :initial-values="editPaymentForm" @submit="onChangePaymentMethod">
              <Field v-slot="{ field }" v-model="editPaymentForm.levelPaymentMethod" as="div" name="levelPaymentMethod">
                <div class="flex flex-wrap gap-14 py-6">
                  <div class="flex items-center gap-2">
                    <RadioButton v-model="field.value" input-id="primary" name="levelPaymentMethod" :value="true" />
                    <label for="primary">Change Primary Payment Method</label>
                  </div>
                  <div class="flex items-center gap-2">
                    <RadioButton v-model="field.value" input-id="secondary" name="levelPaymentMethod" :value="false" />
                    <label for="secondary">Change Secondary Payment Method</label>
                  </div>
                </div>
              </Field>
              <Field
                v-slot="{ field, errorMessage, handleChange }" v-model="editPaymentForm.paymentMethod" as="div"
                name="paymentMethod"
              >
                <div class="flex flex-col gap-6 mb-6">
                  <label class="font-semibold">Select an existing Payment Method to change to : </label>
                  <Select
                    v-model="field.value" :options="paymentMethodOptions" option-label="label"
                    option-value="value" placeholder="Select a Payment Method" class="w-full" @value-change="(e: number | string) => {
                      handleChange(e)
                      handleSelectChange(e)
                    }"
                  />
                </div>
                <Message v-if="errorMessage" severity="error" variant="simple" class="mb-2 -mt-4">
                  {{ errorMessage }}
                </Message>
              </Field>

              <div class="edit-tips mb-6">
                <p>
                  You can only select from existing payment methods.<br>
                  To add a new one, go to the Payment Methods section.
                </p>
              </div>
              <div class="flex justify-end">
                <Button label="CONFIRM" severity="warn" class="btn tips-btn" type="submit" :loading="paymentLoading" />
              </div>
            </VeeForm>
          </template>
        </CustomDialog>
        <AddCardOrBank v-model:visible="showAddPaymentDialog" :show-radio="true" @close="handleDialogClose" />
      </div>
    </div>
    <div v-else class="flex justify-center items-center py-60">
      <ProgressSpinner />
    </div>
    <ScheduleEditDialog
      ref="scheduleEditDialogRef" v-model:visible="isScheduleEditDialogVisible"
      :property-list="editPropertyList" @confirm="handleScheduleEditConfirm"
    />
    <div class="dialog">
      <AddPropertyDialog
        v-model:visible="dialogVisible" @close="handleDialogClose" @refresh="() => {
          fetchPropertyList()
          withRefresh()
        }"
      />
    </div>
  </div>
</template>

<style>
.transaction-history-table.p-datatable {
  --p-datatable-body-cell-padding: 1.2rem 0.5rem;
  --p-datatable-header-padding: 0.7rem 0.5rem;
  --p-datatable-header-cell-padding: 0.7rem 0.5rem;
  --p-datatable-row-background: #f5f5ff;

  .p-datatable-table-container {
    .p-datatable-thead {
      .p-datatable-column-header-content {
        padding: 4px 0.5rem;
      }

    }

    .p-datatable-tbody {
      tr {
        td {
          border-bottom: 1px solid #545454;
        }

        &:last-child {
          td {
            border-bottom: none;
          }
        }
      }
    }
  }
}
</style>

<style scoped lang="scss">
@use '@/styles/mixins/breakpoints' as *;

.btn {
  padding: 10px;
  width: 220px;
  --p-button-label-font-weight: 600;
}

.tips-btn {
  width: 120px;
}

.once-off-tips,
.edit-tips {
  color: #eb001b;
  border: 2px solid #0073cf;
  padding: 1rem;

  div {
    font-size: 18px;
    margin-bottom: 1.5rem;
    font-weight: 600;
  }
}

.flexirates-subtitle-text {
  font-size: 17px;
  color: #545454;
  font-weight: 400;
  margin-top: 10px;
  /* padding-bottom: 10px; */
}

.save-btn,
.confirm-btn {
  font-size: 16px;
  padding: 8px 20px;
  --p-button-label-font-weight: 600;
}

.detail {
  color: var(--color-gray-500);

  &-schedule {
    &-title {
      padding: 1rem 0 1rem 2rem;
    }

    &-content {
      padding: 2rem;
      background-color: var(--bg-colors-white);
      border-radius: 8px;
      color: var(--color-gray-500);
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }
  }

  &-overview {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    column-gap: 3rem;
    padding: 2rem 0;

    @include media-breakpoint-down(lg) {
      column-gap: 1rem;
      padding-right: .5rem;
      grid-template-columns: repeat(3, 1fr);
      gap: 1rem;
    }

    @include media-breakpoint-down(md) {
      column-gap: 1rem;
      padding-right: .5rem;
    }

    @include media-breakpoint-down(sm) {
      grid-template-columns: repeat(2, 1fr);
      row-gap: 2rem;
    }

    &>div {
      border-right: 2px solid var(--color-gray-500);

      &:last-child {
        border: none;
      }
    }

    @include media-breakpoint-down(sm) {
      &>div {
        border: none;

        &:nth-child(odd) {
          border-right: 2px solid var(--color-gray-500);
        }

        &:last-child {
          border-right: none;
        }
      }

    }

    @media (max-width:383px) {
      grid-template-columns: 1fr;

      &>div {
        border-right: none !important;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--color-gray-500);
      }
    }

  }

  &-payment {
    margin-top: 1.5rem;

    &-title {
      padding: 1rem 0 1rem 2rem;
    }

    &-content {

      .payment-item {
        display: flex;
        padding: 1rem 1rem 1rem 2rem;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        background-color: var(--bg-colors-white);
        border-radius: 8px;

        @include media-breakpoint-down(sm) {
          display: block;

        }

        .payment-date {
          @include media-breakpoint-down(md) {
            gap: 3rem;
          }

          @media(max-width: 640px) {
            gap: 1rem;
          }

        }
      }
    }
  }

  &-history {

    margin-top: 1.5rem;

    &-title {
      padding: 1rem 0 1rem 2rem;
    }

    &-content {
      padding: 1rem 2rem;
      border-radius: 8px;
      background-color: var(--bg-colors-white);
    }
  }

  .overview {

    &-title {
      font-weight: 600;
      color: var(--color-gray-500);
      // font-size: 1.5rem;
    }

    &-content {
      font-size: 2rem;
      font-weight: 700;
      padding-top: 2rem;
      padding-bottom: .5rem;
      color: var(--color-blue-500);

    }
  }

}
</style>
