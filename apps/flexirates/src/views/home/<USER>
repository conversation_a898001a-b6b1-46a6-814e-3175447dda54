<script setup lang="ts">
import dayjs from 'dayjs'
import Skeleton from 'primevue/skeleton'
import { Navigation } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { nextTick, onMounted, ref, watch } from 'vue'
import { useDict } from '@/composables/useDict'
import { useListRefresh } from '@/composables/useListRefresh'
import { home as homeApi } from '@/services/flexirates'

import AddPropertyDialog from './components/addPropertyDialog.vue'
// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'

const properties = ref<any>([])

const { getLabel: getStatusLabel } = useDict('payment_plan_show')

const { withRefresh } = useListRefresh('flexiratesPaymentMethodList')

// Swiper configuration
const swiperModules = [Navigation]
const swiperBreakpoints = {
  320: {
    slidesPerView: 1,
    spaceBetween: 20,
  },
  576: {
    slidesPerView: 1,
    spaceBetween: 20,
  },
  768: {
    slidesPerView: 3,
    spaceBetween: 16,
  },
  1024: {
    slidesPerView: 4,
    spaceBetween: 20,
  },
  1400: {
    slidesPerView: 4,
    spaceBetween: 20,
  },
  1600: {
    slidesPerView: 4,
    spaceBetween: 20,
  },
  1800: {
    slidesPerView: 5,
    spaceBetween: 20,
  },
}

const isLoading = ref(false)
const fetchPropertyList = async () => {
  isLoading.value = true
  const res = await homeApi.getPropertyList({
    page: 1,
    page_size: 500,
  })
  properties.value = res.data.data

  isLoading.value = false
}

// const deletePropertyItem = async (id: number) => {
//   const res = await deleteProperty({ property_ids: [id] })
//   if (res.code === 0) {
//     fetchPropertyList()
//     selectedProperty.value = null
//     toast.add({ severity: 'success', summary: 'Message', detail: 'Operation successful', life: 3000 })
//   }
// }

// const getColor = (title: string) => {
//   if (title === 'Active Schedules') {
//     return '#39b54a'
//   }
//   if (title === 'Failed Schedules') {
//     return '#eb001b'
//   }
//   return '#1b1548'
// }
onMounted(() => {
  fetchPropertyList()
})

const dialogVisible = ref(false)
const swiperRef = ref<typeof Swiper | null>(null)

// Navigation button visibility
const showPrevButton = ref(false)
const showNextButton = ref(false)

// Swiper event handlers
const onSwiperInit = (swiper: any) => {
  updateNavigationButtons(swiper)
}

const onSlideChange = (swiper: any) => {
  updateNavigationButtons(swiper)
}

const updateNavigationButtons = (swiper: any) => {
  showPrevButton.value = !swiper.isBeginning
  showNextButton.value = !swiper.isEnd
}

// Watch for properties changes to update navigation buttons
watch(properties, () => {
  nextTick(() => {
    if (swiperRef.value && swiperRef.value?.swiper) {
      updateNavigationButtons(swiperRef.value?.swiper)
    }
  })
}, { immediate: true })

const handleDialogClose = () => {
  dialogVisible.value = false
}
</script>

<template>
  <div class="flexirate-wrap">
    <div class="property-header flex justify-between items-center">
      <div class="property-title-text">
        Welcome to Your Dashboard
      </div>
      <div>
        <Button class="btn" type="button" label="ADD PROPERTY" severity="warn" @click="dialogVisible = true" />
      </div>
    </div>

    <!-- Loading Skeleton -->
    <div v-if="isLoading" class="flexirate-container">
      <div class="property-carousel">
        <div class="property-skeleton-container">
          <div v-for="n in 4" :key="n" class="property-skeleton-item">
            <div class="skeleton-card">
              <Skeleton class="skeleton-title" height="2rem" />
              <Skeleton class="skeleton-address" height="1rem" width="70%" />
              <Skeleton class="skeleton-image" height="90px" />
              <div class="skeleton-details">
                <div class="skeleton-detail-item">
                  <Skeleton height="1rem" width="60%" />
                  <Skeleton height="1rem" width="80%" />
                </div>
                <div class="skeleton-detail-item">
                  <Skeleton height="1rem" width="60%" />
                  <Skeleton height="1rem" width="50%" />
                </div>
                <div class="skeleton-detail-item">
                  <Skeleton height="1rem" width="70%" />
                  <Skeleton height="1rem" width="60%" />
                </div>
              </div>
              <Skeleton class="skeleton-button" height="2.5rem" width="70%" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Actual Content -->
    <div v-else class="flexirate-container">
      <div class="property-carousel">
        <Swiper
          ref="swiperRef" :modules="swiperModules" :slides-per-view="4" :space-between="24"
          :breakpoints="swiperBreakpoints" :navigation="{
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
          }" :loop="false" :allow-touch-move="true" class="property-swiper" @swiper="onSwiperInit"
          @slide-change="onSlideChange"
        >
          <SwiperSlide v-for="property in properties" :key="property.id">
            <div
              class="property-item flex flex-col gap-6 bg-white dark:bg-gray-800 py-10"
              @click="$router.push({ name: 'propertyDetail', params: { id: property.id } })"
            >
              <div class="text-(--color-blue-500) font-bold text-3xl line-clamp-1" :title="property?.nickname">
                {{ property?.nickname }}
              </div>
              <div class="text-(--color-gray-500) -mt-4 line-clamp-1">
                {{ property?.address }}
              </div>
              <div>
                <img src="@/assets/flexirates/property.png" alt="">
              </div>
              <div class="text-(--color-gray-500) flex flex-col gap-6">
                <div class="flex flex-col gap-2">
                  <div class="font-semibold">
                    Property Number
                  </div>
                  <div>
                    {{ property.property_number }}
                  </div>
                </div>
                <div class="flex flex-col gap-2">
                  <div class="font-semibold ">
                    Schedule Status
                  </div>
                  <div class="min-h-8">
                    {{ getStatusLabel(property.schedule_status) }}
                  </div>
                </div>
                <div class="flex flex-col gap-2">
                  <div class="font-semibold">
                    Next Payment Due Date
                  </div>
                  <div>
                    {{ dayjs(property.next_payment_date).format('DD MMM YYYY') }}
                  </div>
                </div>
              </div>
              <div class="flex justify-center mt-4">
                <Button
                  label="PROPERTY DETAILS" severity="warn" class="btn"
                  @click="$router.push({ name: 'propertyDetail', params: { id: property.id } })"
                />
              </div>
            </div>
          </SwiperSlide>

          <!-- Custom Navigation Buttons -->
          <div v-show="showPrevButton" class="swiper-button-prev">
            <i class="pi pi-chevron-left" />
          </div>
          <div v-show="showNextButton" class="swiper-button-next">
            <i class="pi pi-chevron-right" />
          </div>
        </Swiper>
      </div>
    </div>
    <div class="dialog">
      <AddPropertyDialog
        v-model:visible="dialogVisible" @close="handleDialogClose" @refresh="() => {
          fetchPropertyList()
          withRefresh()
        }"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
@use '@/styles/mixins/breakpoints' as *;

.btn {
  display: inline-block;
  padding: 10px 40px;
}

.flexirate-wrap {
  width: 100%;
  height: 100%;

  color: var( --colors-primary-1);

  .property-header {
    margin: 1rem 0 2rem 0;
    padding: 2.5rem 1.5rem;
    background-color: var(--color-white);
    border-radius: 16px;

    .property-title-text {
      font-size: 2.5rem;
      font-weight: 800;
      color: var(--color-blue-500);

      @include media-breakpoint-down(sm) {
        font-size: 1.6rem;
      }

    }
  }

  .property-carousel {
    position: relative;

    .property-swiper {

      @include media-breakpoint-down(md) {
        padding: 0 30px;
      }

      @include media-breakpoint-down(sm) {
        padding: 0 20px;
      }

      .property-item {
        max-width: 330px;
        border-radius: 16px;
        padding: 70px 30px;
        cursor: pointer;
        transition: transform 0.3s ease;

        img {
          height: 90px;
        }

        @include media-breakpoint-down(sm) {
          max-width: 100%;
        }
      }
    }

    // Custom navigation buttons
    .swiper-button-prev,
    .swiper-button-next {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 40px;
      height: 40px;
      background-color: #0073cf;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      z-index: 10;
      transition: background-color 0.3s ease;

      &:after {
        display: none;
      }

      i {
        color: var(--color-white);
        font-size: 16px;
      }

      &:hover {
        background-color: #0062b0;
      }
    }

    .swiper-button-prev {
      left: 10px;

      @include media-breakpoint-down(sm) {
        left: 5px;
        width: 35px;
        height: 35px;
      }
    }

    .swiper-button-next {
      right: 10px;

      @include media-breakpoint-down(sm) {
        right: 5px;
        width: 35px;
        height: 35px;
      }
    }
  }

  .flexirate-container {
    // padding: 1rem 1.5rem;
    border-radius: 8px;
    // background-color: #ffffff;

    .property-container {
      @include media-breakpoint-down(xl) {
        flex-direction: column;
        gap: 10px;
      }
    }

  }
}

.property-payments {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  column-gap: 20px;
  row-gap: 10px;
  padding: 1.5rem 0 1.5rem 0;

  @include media-breakpoint-down(xl) {
    width: 100%;
  }

  .property-wrap {
    background-color: var(--bg-colors-white);
    padding: 10px 0 2rem 0;

    @include media-breakpoint-down(xl) {
      display: grid;
      grid-template-columns: repeat(2, 1fr);

    }

    @include media-breakpoint-down(md) {
      display: grid;
      grid-template-columns: repeat(1, 1fr);

    }

    .property-item {
      // padding: 10px 60px 60px 60px;
      padding-bottom: 10px;
      margin: 0 30px;
      border-bottom: 2px solid var(--color-gray-500);
      display: flex;
      flex-direction: column;
      // align-items: center;
      justify-content: center;
      // border: 1px solid transparent;

      @include media-breakpoint-down(xl) {
        margin: 0 20px;

      }

      // &:hover {
      //   border: 1px solid #ff5400;
      // }

      // &.active {
      //   color: #ffffff;
      //   background-color: #ff5400;
      // }
      &:last-child {
        border-bottom: none;

        @include media-breakpoint-down(xl) {
          border-bottom: 2px solid var(--color-gray-500);
        }
      }

      .item-delete {
        font-size: 18px;

        // .delete-icon {
        //   color: #ff7f40;

        //   &:hover {
        //     color: #ff5400;
        //   }
        // }
      }

      .item-content {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 1.5rem;
        padding: 5px 0;

        .item-content-right {
          height: 100%;
          // display: flex;
          display: grid;
          width: 100%;
          // flex-direction: column;
          // justify-content: space-between;
          grid-auto-flow: row;

          .property-address {
            grid-template-columns: minmax(0, 1fr);
            // max-width: 385px;
            // width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .icon-home {
          color: var(--color-gray-500);
          font-size: 60px;
        }
      }
    }
  }

}

// Skeleton styles
.property-skeleton-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;

  @include media-breakpoint-down(xl) {
    grid-template-columns: repeat(3, 1fr);
  }

  @include media-breakpoint-down(lg) {
    grid-template-columns: repeat(2, 1fr);
  }

  @include media-breakpoint-down(sm) {
    grid-template-columns: repeat(1, 1fr);
  }

  .property-skeleton-item {
    .skeleton-card {
      background-color: var(--color-white);
      border-radius: 16px;
      padding: 70px 30px;
      display: flex;
      flex-direction: column;
      gap: 24px;
      max-width: 330px;

      @include media-breakpoint-down(sm) {
        max-width: 100%;
      }

      .skeleton-title {
        margin-bottom: 8px;
      }

      .skeleton-address {
        margin-bottom: 16px;
      }

      .skeleton-image {
        margin-bottom: 16px;
        border-radius: 8px;
      }

      .skeleton-details {
        display: flex;
        flex-direction: column;
        gap: 24px;

        .skeleton-detail-item {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }
      }

      .skeleton-button {
        margin: 16px auto 0;
        border-radius: 8px;
      }
    }
  }
}

.property-overview {
  @include media-breakpoint-down(xl) {
    width: 100%;
  }

  .schedule-overview {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    column-gap: 30px;
    color: var(--color-gray-500);

    @include media-breakpoint-down(sm) {
      grid-template-columns: repeat(2, 1fr);
    }

    &-item {
      margin: 2rem 0;

      @include media-breakpoint-down(sm) {
        margin: 1rem 0;
      }

    }

    &-title {
      font-weight: 600;

    }

    &-display {
      margin-top: 1rem;
      padding: 15px;
      background-color: var(--bg-colors-white);

      &-amount {
        font-size: 4rem;
        font-weight: 700;
        color: #1b1548;
      }
    }
  }

  .payment-schedule {
    color: #545454;

    .schedule-content {
      background-color: var(--bg-colors-white);
      padding: .5rem 2rem 1rem 2rem;

      &-item {
        @media (max-width:525px) {
          flex-direction: column;

          &>div {
            margin: 10px;
          }
        }

      }
    }

    .btn-view {
      display: inline-block;
      padding: 10px 50px;
      font-weight: 700;
    }

  }
}
</style>

<style lang="scss">
.property-dialog.p-dialog {
  background-color: #ffe3e8;
  color: #1b1548;

  .property-label {
    font-weight: 600;
    color: #545454;
    width: 150px;
    font-size: 1.2rem;
  }

  .property-input {
    border-color: #545454;
    background-color: #ffe3e8;
    width: 400px;
  }
}
</style>
