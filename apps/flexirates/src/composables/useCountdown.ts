import { computed, onMounted, onUnmounted, ref } from 'vue'

interface CountdownOptions {
  duration?: number // 倒计时时长（秒）
  storageKey?: string // localStorage 存储的键名
  onFinish?: () => void // 倒计时结束回调
}

export function useCountdown(options: CountdownOptions = {}) {
  const {
    duration = 60, // 默认60秒
    storageKey = 'countdown_timer',
    onFinish,
  } = options

  const counting = ref(false)
  const remainingSeconds = ref(0)
  let timer: number | null = null

  // 格式化显示的倒计时文本
  const formattedTime = computed(() => {
    if (remainingSeconds.value <= 0) { return '' }

    const minutes = Math.floor(remainingSeconds.value / 60)
    const seconds = remainingSeconds.value % 60

    if (minutes > 0) {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`
    }
    return `${seconds}s`
  })

  // 保存倒计时状态到 localStorage
  const saveState = () => {
    if (remainingSeconds.value > 0) {
      const endTime = Date.now() + remainingSeconds.value * 1000
      localStorage.setItem(storageKey, JSON.stringify({
        endTime,
        duration,
      }))
    }
    else {
      localStorage.removeItem(storageKey)
    }
  }

  // 从 localStorage 恢复倒计时状态
  const restoreState = () => {
    const stored = localStorage.getItem(storageKey)
    if (stored) {
      try {
        const { endTime } = JSON.parse(stored)
        const now = Date.now()
        const remaining = Math.round((endTime - now) / 1000)

        if (remaining > 0) {
          remainingSeconds.value = remaining
          counting.value = true
          startTimer()
        }
        else {
          // 倒计时已结束，清除存储
          localStorage.removeItem(storageKey)
        }
      }
      catch (e) {
        console.error('Error restoring countdown state:', e)
        localStorage.removeItem(storageKey)
      }
    }
  }

  // 开始倒计时
  const start = () => {
    if (counting.value) { return }

    remainingSeconds.value = duration
    counting.value = true
    saveState()
    startTimer()
  }

  // 内部启动定时器的方法
  const startTimer = () => {
    if (timer) { clearInterval(timer) }

    timer = window.setInterval(() => {
      remainingSeconds.value--
      saveState()

      if (remainingSeconds.value <= 0) {
        stop()
        if (onFinish) { onFinish() }
      }
    }, 1000)
  }

  // 停止倒计时
  const stop = () => {
    counting.value = false
    if (timer) {
      clearInterval(timer)
      timer = null
    }
    localStorage.removeItem(storageKey)
  }

  // 重置倒计时（不会自动开始）
  const reset = () => {
    stop()
    remainingSeconds.value = 0
  }

  // 组件挂载时尝试恢复倒计时状态
  onMounted(() => {
    restoreState()

    // 监听存储事件，支持多标签页同步
    window.addEventListener('storage', (e) => {
      if (e.key === storageKey) {
        restoreState()
      }
    })
  })

  // 组件卸载时清理定时器
  onUnmounted(() => {
    if (timer) {
      clearInterval(timer)
      timer = null
    }
  })

  return {
    start,
    stop,
    reset,
    counting,
    remainingSeconds,
    formattedTime,
  }
}
