import { useToast } from 'primevue/usetoast'
import { ref } from 'vue'

interface ExportOptions {
  /**
   * The export API function to call
   */
  exportFn: (params: any) => Promise<any>

  /**
   * Function to get the current search/filter parameters
   */
  getParams: () => any

  /**
   * Callback when export starts
   */
  onExportStart?: () => void

  /**
   * Callback when export completes successfully
   */
  onExportSuccess?: (url: string) => void

  /**
   * Callback when export fails
   */
  onExportError?: (error: any) => void
}

export function useExport(options: ExportOptions) {
  const toast = useToast()
  const isExporting = ref(false)

  /**
   * Handle the export process
   * @param format The export format (csv, xls, pdf)
   */
  const handleExport = async (file_type: string) => {
    try {
      isExporting.value = true

      // Call the onExportStart callback if provided
      if (options.onExportStart) {
        options.onExportStart()
      }

      // Get the current parameters
      const params = options.getParams()

      // Add the format to the parameters
      const exportParams = {
        ...params,
        file_type,
      }

      // Call the export function
      const response = await options.exportFn(exportParams)

      // Handle the response
      if (response.data && response.data.url) {
        const exportUrl = response.data.url

        // Open the URL in a new tab
        window.open(exportUrl, '_blank')

        // Show success message
        toast.add({
          severity: 'success',
          summary: 'Export Successful',
          detail: `Data has been exported as ${file_type.toUpperCase()}`,
          life: 3000,
        })

        // Call the onExportSuccess callback if provided
        if (options.onExportSuccess) {
          options.onExportSuccess(exportUrl)
        }
      }
      else {
        throw new Error('Export URL not found in response')
      }
    }
    catch (error) {
      console.error('Export failed:', error)

      // Show error message
      toast.add({
        severity: 'error',
        summary: 'Export Failed',
        detail: 'Failed to export data. Please try again.',
        life: 3000,
      })

      // Call the onExportError callback if provided
      if (options.onExportError) {
        options.onExportError(error)
      }
    }
    finally {
      isExporting.value = false
    }
  }

  return {
    isExporting,
    handleExport,
  }
}
