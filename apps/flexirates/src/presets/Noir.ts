import { definePreset, palette } from '@primeuix/themes'
import Aura from '@primeuix/themes/aura'

const primaryColors: Record<number, string> = palette('#031f73')
const primaryColorsDark: Record<number, string> = palette('#4f46e5')

const infoColors: Record<number, string> = palette('#00B7FF')
const infoColorsDark: Record<number, string> = palette('#3b82f6')

const warnColors: Record<number, string> = palette('#0073cf')
const warnColorsDark: Record<number, string> = palette('#f59e0b')

const successColors: Record<number, string> = palette('#39b54a')
const successColorsDark: Record<number, string> = palette('#10b981')

const whiteTheme = definePreset(Aura, {
  semantic: {
    primary: {
      ...primaryColors,
    },
    sky: {
      ...infoColors,
    },
    orange: {
      ...warnColors,
    },
    green: {
      ...successColors,
    },
  },
})

// 暗黑主题预设
const darkTheme = definePreset(Aura, {
  semantic: {
    primary: {
      ...primaryColorsDark,
    },
    sky: {
      ...infoColorsDark,
    },
    orange: {
      ...warnColorsDark,
    },
    green: {
      ...successColorsDark,
    },
  },
})

export { darkTheme, whiteTheme }
