import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const useFlexiratesHousesStore = defineStore('flexiratesHouses', () => {
  const id = ref<number | null>(null)

  const setId = (newId: number) => {
    id.value = newId
  }

  const clearId = () => {
    id.value = null
  }

  const isHasId = computed(() => id.value !== null)

  return {
    id,
    setId,
    clearId,
    isHasId,
  }
}, {
  persist: true,
})
