import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useListStore = defineStore('list', () => {
  // 使用 Map 存储每个列表的刷新状态
  const refreshFlags = ref(new Map<string, boolean>())

  // 设置某个列表的刷新状态
  const setNeedRefresh = (listName: string, value: boolean) => {
    refreshFlags.value.set(listName, value)
  }

  // 获取某个列表的刷新状态
  const getNeedRefresh = (listName: string) => {
    return refreshFlags.value.get(listName) || false
  }

  // 清除某个列表的刷新状态
  const clearRefreshFlag = (listName: string) => {
    refreshFlags.value.delete(listName)
  }

  return {
    setNeedRefresh,
    getNeedRefresh,
    clearRefreshFlag,
  }
}, {
  persist: true,
})
