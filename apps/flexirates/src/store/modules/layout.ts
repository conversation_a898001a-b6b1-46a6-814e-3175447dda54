import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import {
  getInitialThemeMode,
  getNextThemeMode,
  resolveThemeMode,
} from '@/utils/theme'

export const useLayoutStore = defineStore('layout', () => {
  // state
  const isSidebarVisible = ref(true)
  const isSidebarSlim = ref(false)
  const themeMode = ref<Theme.ThemeMode>(getInitialThemeMode())

  // computed - 根据主题模式计算实际的暗黑状态
  const isDarkTheme = computed(() => resolveThemeMode(themeMode.value))

  // 设置系统主题监听（仅在客户端）
  if (typeof window !== 'undefined') {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleSystemThemeChange = () => {
      // 仅在 system 模式下触发重新计算
      // computed 会自动处理，这里主要是为了确保响应性
      if (themeMode.value === 'system') {
        // 触发响应式更新（computed 会自动重新计算）
        // 这里不需要做任何事情，computed 会自动重新计算
      }
    }

    mediaQuery.addEventListener('change', handleSystemThemeChange)
  }

  // actions
  const setSidebarMode = (mode: 'slim' | 'expanded') => {
    isSidebarSlim.value = mode === 'slim'
  }

  const toggleSidebarVisible = () => {
    isSidebarVisible.value = !isSidebarVisible.value
  }

  const setThemeMode = (mode: Theme.ThemeMode) => {
    themeMode.value = mode
  }

  const toggleDarkTheme = () => {
    // 循环切换主题模式：light -> dark -> system -> light
    themeMode.value = getNextThemeMode(themeMode.value)
  }

  const setDarkTheme = (isDark: boolean) => {
    themeMode.value = isDark ? 'dark' : 'light'
  }

  return {
    // state
    isSidebarVisible,
    isSidebarSlim,
    themeMode,
    isDarkTheme, // computed

    // actions
    setSidebarMode,
    toggleSidebarVisible,
    setThemeMode,
    toggleDarkTheme,
    setDarkTheme,
  }
}, {
  persist: true,
})
