import { Utils } from '@bill-merchant/shared'
import { Constants } from '@shared'
import dayjs from 'dayjs'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { user as userApi } from '@/services/flexirates'

enum PaymentMethod {
  BANK_ACCOUNT = 1,
  CREDIT_CARD = 2,
}

interface RegisterModel {

  banking_id: number

  first_name: string
  last_name: string
  mobile_phone: string
  email: string
  property_number: string
  verification_code: string
  password: string

  payment_plan: number
  payment_plan_schedule: number
  first_payment_date: Date
  last_payment_date: string
  flexiRates: number
  paymentMethod: PaymentMethod
  terms: boolean
  notice: boolean
  amount: string
  regular_payment_amount: string
  no_of_regular_payment: number

  is_default_payment_method: boolean

  bank: {
    nickname: string
    bsb: string
    account_no: string
    account_name: string
  }

  card: {
    card_number: string
    security_code: string
    name_on_card: string
    expiry_date: string
    email: string
    billingAddress: {
      country: string
      first_name: string
      last_name: string
      company: string
      address_line1: string
      address_line2: string
      city: string
      state: string
      postal_code: string
      phone: string
    }
  }
}

export const useFlexiratesRegisterStore = defineStore('flexiratesRegister', () => {
  const resetModel = {
    banking_id: 0,

    first_name: '',
    last_name: '',
    mobile_phone: '',
    email: '',
    property_number: '',
    verification_code: '',
    password: '',

    // 步骤2
    payment_plan: Constants.Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS,
    payment_plan_schedule: 0,
    first_payment_date: dayjs().add(3, 'day').toDate(),
    last_payment_date: '',
    flexiRates: 0,
    // 1 / 2
    paymentMethod: PaymentMethod.CREDIT_CARD,
    amount: '',
    // 默认周期付款金额
    regular_payment_amount: '0',
    // 默认周期付款次数
    no_of_regular_payment: 0,

    // 步骤3

    bank: {
      nickname: '',
      bsb: '',
      account_no: '',
      account_name: '',
    },
    card: {
      card_number: '',
      security_code: '',
      name_on_card: '',
      expiry_date: '',
      email: '',
      billingAddress: {
        country: '',
        first_name: '',
        last_name: '',
        company: '',
        address_line1: '',
        address_line2: '',
        city: '',
        state: '',
        postal_code: '',
        phone: '',
      },
    },

    // 是否选中默认支付方式
    is_default_payment_method: true,

    // 条款和条件
    terms: false,
    // 隐私政策
    notice: false,
  }

  const model = ref<RegisterModel>(Utils.deepClone(resetModel) as RegisterModel)

  const currentStep = ref<1 | 2 | 3>(1)

  const setModel = (newModel: typeof model.value) => {
    model.value = newModel
  }

  const setCurrentStep = (newStep: 1 | 2 | 3) => {
    currentStep.value = newStep
  }

  const createBanking = async (data: Api.FlexiratesUserRegisterCreateBankingReq) => {
    const result = await userApi.registerCreateBanking(data)
    return result
  }

  const reset = () => {
    model.value = Utils.deepClone(resetModel) as RegisterModel
    currentStep.value = 1
  }

  return {
    model,
    currentStep,

    // actions
    createBanking,
    reset,
    setModel,
    setCurrentStep,
  }
}, {
  persist: {
    omit: ['currentStep'],
  },
})
