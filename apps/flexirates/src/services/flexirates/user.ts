import type { AxiosRequestConfig } from 'axios'
import { GET, POST } from '@/services/http'

export const login = (data: Api.FlexiratesUserLoginReq) => POST<Api.FlexiratesUserLoginRes>('login', data)

export const register = (data: Api.FlexiratesUserRegisterReq) => POST<Api.FlexiratesUserRegisterRes>('register', data)

export const registerCreateBanking = (data: Api.FlexiratesUserRegisterCreateBankingReq) => POST<Api.FlexiratesUserRegisterCreateBankingRes>('/registerCreateBanking', data)

export const getRegisterAccountInfo = (params: Api.RegisterAccountInfoReq) =>
  GET<Api.RegisterAccountInfoRes>('getAccountInfo', { params })

export const logout = () => GET('userLoginOut')

export const getUserInfo = () => GET<FlexiratesUser.Info>('personal/detail')

// Update user
export const updateUserInfo = (data: FlexiratesUser.UserInfoUpdateReq) => POST<FlexiratesUser.Info>('userUpdate', data)

// upload_files[]
export const uploadFile = (data: FormData, config?: AxiosRequestConfig) => POST<{ file_path: string[] }>('/uploadFile', data, {
  headers: {
    'Content-Type': 'multipart/form-data',
  },
  ...config,
})

// send verification code
export const sendVerificationCode = (params: Api.SendVerificationCodeReq) => GET<Api.SendVerificationCodeRes>('verifyCode', { params })

export const sendResetPasswordEmail = (data: Api.SendResetPasswordEmailReq) => POST<Api.SendResetPasswordEmailRes>('resetPassEmail', data)

export const resetPassword = (data: Api.ResetPasswordReq) => POST<Api.ResetPasswordRes>('resetPass', data)

export const updatePersonalInfo = (data: FlexiratesUser.updatePersonalInfoReq) => POST('personal/update', data)

export const getCheckTimeRegister = () => GET<{ allowRegister: boolean }>('/registerDateCheck')
