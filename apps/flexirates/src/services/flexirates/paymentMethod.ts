import { GET, POST } from '@/services/http'

export const getList = () => GET<CommonListRes<Api.PaymentMethodInfo>>('paymentMethod/list')

export const getBankOrCardDetail = (params: { id: number }) => GET<CommonRes>('paymentMethod/detail', { params })

export const changeNickName = (data: Api.FlexiratesChangeNickNameReq) => POST('paymentMethod/changeNickname', data)

export const deletePaymentMethod = (data: { id: number }) => POST('paymentMethod/delete', data)

export const editBank = (data: Api.FlexiratesEditBankReq) => POST('paymentMethod/editBank', data)

export const addCardOrBank = (data: Api.FlexiratesAddCardOrBankReq) => POST('paymentMethod/create', data)

export const updateWeight = (data: { property_id: number, id: number }) => POST('paymentMethod/updatePrimaryWeight', data)
