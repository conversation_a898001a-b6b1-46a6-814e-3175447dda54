import { GET, POST } from '@/services/http'

export const getNotifyList = (params: CommonSearchListParams) => GET('/notify/list', { params })

export const deleteNotify = (data: Api.FlexiratesNotifyDeleteReq) => POST('/notify/delete', data)

export const updateNotify = (data: Api.FlexiratesNotifyDeleteReq) => POST('/notify/updateRead', data)

export const getUnreadCount = () => GET('/notify/unreadCount')
