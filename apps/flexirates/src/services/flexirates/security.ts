import { GET, POST } from '@/services/http'

export const getAuth = () => GET<Api.FlexiratesAuthRes>('user/getAuthDevices')

export const revokeDevice = (data: any) => POST('user/removeAuthDevice', data)

export const getActivityLog = (params?: Api.UserActivityLogListReq) => GET<Api.UserActivityLogListRes>('/activityLog/list', { params })

export const exportActivityLog = (params: Api.UserActivityLogListReq) => GET('/activityLog/export', { params })
