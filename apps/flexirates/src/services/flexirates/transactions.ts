import { GET, POST } from '@/services/http'

export const getList = (params: Api.FlexiratesTransactionsReq) => GET<Api.FlexiratesTransactionsRes>('transaction/list', { params })

export const sendReceipt = (data: Api.FlexiratesTransactionsSendReceipt) => POST('transaction/receiptSend', data)

export const getAllProperty = (params?: { is_all?: number, is_include_cancel?: number }) => GET<FlexiratesTransaction.PropertyInfo[]>('customerProperty/getAllProperty', { params })

export const getAllAccount = () => GET<FlexiratesTransaction.BankingInfo[]>('customerBanking/getAllCustomerBanking')

export const getTransactionDetail = (id: number) => GET<Api.FlexiratesTransactionDetailRes>('transaction/detail', { params: { id } })

export const exportTransactionsList = (params: Api.FlexiratesTransactionsExportReq) => GET<Api.FlexiratesTransactionsExportRes>('/transaction/export', { params })
