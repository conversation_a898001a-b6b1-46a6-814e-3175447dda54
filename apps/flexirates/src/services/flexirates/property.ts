import { GET, POST } from '@/services/http'

export const getPropertyList = (params: Api.FlexiratesGetPropertyListReq) => GET<Api.FlexiratesGetPropertyListRes>('/property/baseInfo', { params })

export const getPropertyDetail = (id: number) => GET<Api.FlexiratesPropertyDetailRes>('/property/detail', { params: { id } })

export const updatePropertySchedule = (data: Api.FlexiratesUpdatePropertyScheduleReq) => POST('/property/editSchedule', data)
