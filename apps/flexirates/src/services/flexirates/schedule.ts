import { GET } from '@/services/http'

export const getScheduleList = (params: Api.FlexiratesGetScheduleReq) => GET<CommonListRes<Api.FlexiratesListItem>>('schedule/list', { params })

export const getScheduleDetail = (id: number) => GET<Api.FlexiratesScheduleDetailRes>('schedule/detail', { params: { id } })

export const exportSchedule = (params: Api.FlexiratesGetScheduleReq) => GET<Api.FlexiratesScheduleDetailRes>('schedule/detailExporter', { params })
