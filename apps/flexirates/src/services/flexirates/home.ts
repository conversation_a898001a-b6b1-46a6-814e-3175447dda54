import { GET, POST } from '@/services/http'

export const getPropertyList = (params: CommonSearchListParams) => GET<Api.FlexiratesGetPropertyListRes>('property/list', { params })

export const createProperty = (data: Api.FlexiratesCreatePropertyReq) => POST('property/create', data)

export const deleteProperty = (data: Api.FlexiratesDeletePropertyReq) => POST<any>('property/delete', data)

export const getPropertyDetail = (params: { id: number }) => GET<Api.FlexiratesGetPropertyDetailRes>('/property/detail', { params })

export const changeNickname = (data: Api.FlexiratesChangeNicknameReq) => POST('property/changeNickname', data)

export const changePaymentMethod = (data: Api.FlexiratesChangePaymentMethodReq) => POST('property/changePaymentMethod', data)

export const onceOffPayment = (data: Api.FlexiratesOnceOffPaymentReq) => POST('property/onceOffPayment', data)
