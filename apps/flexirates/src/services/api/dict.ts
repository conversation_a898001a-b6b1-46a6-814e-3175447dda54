import { GET } from '@/services/http/index'

export interface DictItem {
  label: string
  value: string | number | null
}

export interface DictData {
  [key: string]: DictItem[]
}

/**
 * Get dictionary data by type
 * @param type Dictionary type
 */
export const getDictByType = (type: string) => {
  return GET<DictItem[]>(`/getDictDataConst`, {
    params: { type },
    baseURL: `${import.meta.env.VITE_API_BASE_URL}/merchant`,
  })
}
